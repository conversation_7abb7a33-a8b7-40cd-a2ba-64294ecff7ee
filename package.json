{"name": "digital-wave-systems-llc", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test:cta": "node scripts/test-cta-integration.js", "test:cta:watch": "nodemon scripts/test-cta-integration.js"}, "dependencies": {"@auth/prisma-adapter": "^2.9.0", "@heroicons/react": "^2.2.0", "@heroui/react": "^2.7.11", "@prisma/client": "^5.11.0", "@react-three/drei": "^10.0.5", "@react-three/fiber": "^9.1.1", "@types/animejs": "^3.1.13", "@vercel/speed-insights": "^1.0.12", "animejs": "^4.0.1", "axios": "^1.6.2", "bcrypt": "^5.0.1", "date-fns": "^3.4.0", "framer-motion": "^12.19.2", "gray-matter": "^4.0.3", "next": "^15.1.7", "next-auth": "^4.24.5", "next-intl": "^4.3.4", "next-themes": "^0.3.0", "nodemailer": "^6.9.12", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "prisma": "^5.7.0", "prismjs": "^1.30.0", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.4.1", "react-icons": "^5.5.0", "rehype-prism-plus": "^2.0.0", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-html": "^16.0.1", "remark-rehype": "^11.1.1", "stripe": "^14.7.0", "tailgrids": "^2.1.0", "three": "^0.175.0", "web-vitals": "^5.0.3"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/node": "22.13.4", "@types/nodemailer": "^6.4.14", "@types/prismjs": "^1.26.3", "@types/react": "19.0.10", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "^14.1.3", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}