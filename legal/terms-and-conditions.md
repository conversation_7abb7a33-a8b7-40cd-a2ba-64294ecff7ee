# Terms and Conditions

**Digital Wave Systems LLC**  
**Effective Date:** January 3, 2025  
**Last Updated:** January 3, 2025

---

## Table of Contents

1. [Introduction and Acceptance](#1-introduction-and-acceptance)
2. [Definitions](#2-definitions)
3. [Services Overview](#3-services-overview)
4. [Service Agreements and Scope](#4-service-agreements-and-scope)
5. [Intellectual Property Rights](#5-intellectual-property-rights)
6. [Payment Terms and Billing](#6-payment-terms-and-billing)
7. [Service Level Agreements (SLAs)](#7-service-level-agreements-slas)
8. [Confidentiality and Non-Disclosure](#8-confidentiality-and-non-disclosure)
9. [Data Protection and Security](#9-data-protection-and-security)
10. [Liability and Risk Allocation](#10-liability-and-risk-allocation)
11. [Professional Standards and Compliance](#11-professional-standards-and-compliance)
12. [Termination and Suspension](#12-termination-and-suspension)
13. [<PERSON> Majeure](#13-force-majeure)
14. [Dispute Resolution](#14-dispute-resolution)
15. [Governing Law and Jurisdiction](#15-governing-law-and-jurisdiction)
16. [General Provisions](#16-general-provisions)

---

## 1. Introduction and Acceptance

### 1.1 Agreement Formation
These Terms and Conditions ("Terms") constitute a legally binding agreement between Digital Wave Systems LLC ("Company," "we," "our," or "us") and the client organization ("Client," "you," or "your") for the provision of professional software development and consulting services.

### 1.2 Acceptance
By engaging our services, executing a Statement of Work (SOW), or making payment for services, you acknowledge that you have read, understood, and agree to be bound by these Terms and any applicable SOW.

### 1.3 Precedence
In case of conflict between these Terms and a specific SOW, the SOW shall take precedence for that particular engagement, provided it does not contradict fundamental provisions of these Terms.

---

## 2. Definitions

**"Confidential Information"** means any non-public, proprietary information disclosed by either party.

**"Deliverables"** means the specific work products, software, documentation, or services to be provided under a SOW.

**"Intellectual Property"** includes patents, copyrights, trademarks, trade secrets, and other proprietary rights.

**"Professional Services"** means consulting, development, implementation, and support services provided by Company.

**"Statement of Work (SOW)"** means a detailed project specification document outlining scope, deliverables, timeline, and pricing.

**"Work Product"** means all materials, software, documentation, and other items created or developed by Company in performing services.

---

## 3. Services Overview

### 3.1 Core Service Offerings
Company provides the following professional services to business clients:

**a) Custom Software Development**
- Web and mobile application development
- Enterprise software solutions
- API development and integration
- Legacy system modernization

**b) AI/ML Integration and Consulting**
- Machine learning model development
- AI strategy and implementation
- Data analytics and insights
- Automated decision-making systems

**c) Cybersecurity Solutions**
- Security assessments and audits
- Vulnerability testing and remediation
- Compliance consulting (SOC 2, ISO 27001)
- Incident response and forensics

**d) Cloud and DevOps Services**
- Cloud migration and architecture
- Infrastructure automation
- CI/CD pipeline implementation
- Performance optimization

**e) Enterprise Consulting**
- Digital transformation strategy
- Technology roadmap development
- Process optimization
- Change management

**f) Talent Acquisition and HR Services**
- Technical recruitment and staffing
- Team augmentation
- Skills assessment and training
- HR technology implementation

### 3.2 Service Delivery Models
- **Project-Based:** Fixed scope with defined deliverables and timeline
- **Time and Materials:** Hourly or daily rate for ongoing consulting
- **Retainer:** Monthly fee for dedicated resources and support
- **Hybrid:** Combination of fixed and variable components

---

## 4. Service Agreements and Scope

### 4.1 Statement of Work Requirements
Each engagement shall be governed by a SOW that includes:
- Detailed project scope and objectives
- Specific deliverables and acceptance criteria
- Project timeline and milestones
- Resource allocation and team structure
- Pricing and payment schedule
- Change management procedures

### 4.2 Scope Changes
- All scope changes must be documented in writing
- Changes may affect timeline, budget, and resource allocation
- Client approval required before implementing scope changes
- Additional charges apply for out-of-scope work

### 4.3 Client Responsibilities
Client agrees to:
- Provide timely access to necessary systems and information
- Designate qualified personnel for project collaboration
- Review and approve deliverables within specified timeframes
- Maintain appropriate security and access controls
- Comply with all applicable laws and regulations

### 4.4 Acceptance Procedures
- Deliverables subject to Client review and acceptance
- Acceptance period of 10 business days unless otherwise specified
- Deemed acceptance if no response within acceptance period
- Rejection must include specific, actionable feedback

---

## 5. Intellectual Property Rights

### 5.1 Client-Owned IP
Client retains ownership of:
- Pre-existing intellectual property and data
- Business processes and proprietary information
- Custom software developed specifically for Client (upon full payment)
- Data and content provided by Client

### 5.2 Company-Owned IP
Company retains ownership of:
- Pre-existing methodologies, tools, and frameworks
- General knowledge and experience gained
- Reusable components and libraries
- Proprietary development processes

### 5.3 Work Product Ownership
- Custom software: Ownership transfers to Client upon full payment
- Consulting deliverables: Client receives unlimited use license
- Shared components: Joint ownership with mutual licensing rights
- Open source components: Subject to applicable open source licenses

### 5.4 IP Indemnification
Company will defend Client against claims that Company-developed software infringes third-party IP rights, provided:
- Client promptly notifies Company of any claims
- Company has sole control of defense and settlement
- Client cooperates fully in the defense
- Limitation: Company's total liability capped at fees paid for the specific deliverable

---

## 6. Payment Terms and Billing

### 6.1 Pricing Structure
**Project-Based Services:**
- Fixed fee based on defined scope and deliverables
- Payment schedule tied to project milestones
- 25-50% deposit required to commence work

**Time and Materials:**
- Hourly rates: $75-$200 USD depending on seniority and specialization
- Daily rates: $600-$1,600 USD for dedicated resources
- Monthly invoicing for hours worked

**Retainer Services:**
- Monthly fee for dedicated team allocation
- Includes specified hours and support level
- Additional hours billed at standard rates

### 6.2 Payment Terms
- Net 30 days from invoice date
- Late payment fee: 1.5% per month on overdue amounts
- Currency: USD unless otherwise specified in SOW
- Payment methods: Wire transfer, ACH, or approved payment platforms

### 6.3 Expenses and Reimbursements
- Pre-approved travel and accommodation expenses
- Third-party software licenses and tools
- Cloud infrastructure and hosting costs
- All expenses documented with receipts

### 6.4 Taxes
- Prices exclude applicable taxes unless stated otherwise
- Client responsible for local taxes and withholdings
- Company will provide necessary tax documentation

---

## 7. Service Level Agreements (SLAs)

### 7.1 Response Times
**Standard Support (Business Hours: 9 AM - 6 PM COT)**
- Critical issues: 4 hours
- High priority: 8 hours
- Medium priority: 24 hours
- Low priority: 72 hours

**Premium Support (24/7 Coverage)**
- Critical issues: 1 hour
- High priority: 4 hours
- Medium priority: 12 hours
- Low priority: 24 hours

### 7.2 Availability Commitments
- **Development Services:** 99.5% availability during business hours
- **Production Support:** 99.9% availability for critical systems
- **Cloud Infrastructure:** 99.95% uptime (excluding planned maintenance)

### 7.3 Performance Standards
- Code quality: Adherence to industry best practices and standards
- Security: Implementation of appropriate security controls
- Documentation: Comprehensive technical and user documentation
- Testing: Minimum 80% code coverage for custom software

### 7.4 SLA Remedies
- Service credits for availability failures
- Expedited resolution for repeated issues
- Escalation to senior management for persistent problems
- Right to terminate for material SLA breaches

---

## 8. Confidentiality and Non-Disclosure

### 8.1 Confidentiality Obligations
Both parties agree to:
- Maintain strict confidentiality of all Confidential Information
- Use Confidential Information solely for the purpose of the engagement
- Implement appropriate safeguards to protect confidential data
- Return or destroy Confidential Information upon request

### 8.2 Exceptions
Confidentiality obligations do not apply to information that:
- Is publicly available through no breach of this agreement
- Was known prior to disclosure
- Is independently developed without use of Confidential Information
- Must be disclosed by law or court order

### 8.3 Employee and Contractor Obligations
- All Company personnel bound by confidentiality agreements
- Background checks for personnel with access to sensitive data
- Regular training on confidentiality and data protection
- Immediate notification of any suspected breaches

### 8.4 Duration
Confidentiality obligations survive termination of the agreement and continue for 5 years unless otherwise specified.

---

## 9. Data Protection and Security

### 9.1 Data Processing
- Company acts as data processor for Client's personal data
- Processing limited to purposes specified in SOW
- Compliance with applicable data protection laws (GDPR, CCPA)
- Data Processing Agreement (DPA) available upon request

### 9.2 Security Measures
**Technical Safeguards:**
- Encryption in transit (TLS 1.3) and at rest (AES-256)
- Multi-factor authentication for system access
- Regular security assessments and penetration testing
- Secure development lifecycle practices

**Organizational Measures:**
- Security awareness training for all personnel
- Incident response and breach notification procedures
- Regular security audits and compliance reviews
- Vendor security assessments for third-party services

### 9.3 Data Breach Response
- Immediate investigation and containment
- Notification to Client within 24 hours of discovery
- Assistance with regulatory notifications as required
- Detailed incident report and remediation plan

---

## 10. Liability and Risk Allocation

### 10.1 Limitation of Liability
**General Limitation:**
Company's total liability for any claim shall not exceed the total fees paid by Client in the 12 months preceding the claim, or $100,000 USD, whichever is greater.

**Excluded Damages:**
Neither party shall be liable for:
- Indirect, incidental, or consequential damages
- Loss of profits, revenue, or business opportunities
- Loss of data (except as specifically covered)
- Punitive or exemplary damages

### 10.2 Exceptions to Limitations
Liability limitations do not apply to:
- Willful misconduct or gross negligence
- Breach of confidentiality obligations
- IP indemnification obligations
- Death or personal injury caused by negligence

### 10.3 Cybersecurity Consulting Limitations
For cybersecurity services:
- Assessments based on point-in-time analysis
- No guarantee of complete security or breach prevention
- Client responsible for implementing recommendations
- Liability limited to re-performance of defective services

### 10.4 Professional Liability Insurance
Company maintains professional liability insurance with minimum coverage of $2,000,000 USD per occurrence and $5,000,000 USD aggregate.

---

## 11. Professional Standards and Compliance

### 11.1 Industry Standards
Company adheres to:
- Software development best practices (SDLC, Agile, DevOps)
- Security frameworks (NIST, ISO 27001, SOC 2)
- AI/ML ethical guidelines and transparency principles
- Professional codes of conduct for consulting services

### 11.2 Regulatory Compliance
- Compliance with applicable laws in jurisdictions of operation
- Industry-specific regulations (HIPAA, PCI DSS, GDPR)
- Export control and trade compliance
- Anti-corruption and anti-bribery policies

### 11.3 Quality Assurance
- Peer review processes for all deliverables
- Automated testing and continuous integration
- Regular quality audits and assessments
- Client feedback integration and improvement processes

---

## 12. Termination and Suspension

### 12.1 Termination for Convenience
Either party may terminate with 30 days written notice:
- Client pays for work completed through termination date
- Company delivers all completed work products
- Confidentiality and IP provisions survive termination

### 12.2 Termination for Cause
Immediate termination allowed for:
- Material breach not cured within 30 days of notice
- Insolvency or bankruptcy proceedings
- Violation of confidentiality or security requirements
- Failure to pay undisputed invoices within 60 days

### 12.3 Suspension of Services
Company may suspend services for:
- Non-payment of undisputed invoices
- Client breach of security requirements
- Violation of acceptable use policies
- Legal or regulatory compliance issues

### 12.4 Post-Termination Obligations
- Return of Confidential Information and Client data
- Final invoicing and payment reconciliation
- Transition assistance (if requested and compensated)
- Survival of applicable provisions

---

## 13. Force Majeure

Neither party shall be liable for delays or failures due to circumstances beyond reasonable control, including:
- Natural disasters and acts of God
- War, terrorism, or civil unrest
- Government actions or regulatory changes
- Pandemic or public health emergencies
- Cyber attacks or infrastructure failures

**Mitigation Requirements:**
- Prompt notification of force majeure events
- Reasonable efforts to minimize impact and duration
- Alternative solutions and workarounds where possible
- Right to terminate if event continues for more than 90 days

---

## 14. Dispute Resolution

### 14.1 Negotiation
Parties agree to first attempt resolution through good faith negotiation between senior executives within 30 days of dispute notice.

### 14.2 Mediation
If negotiation fails, disputes shall be submitted to mediation before a mutually agreed mediator or mediation service.

### 14.3 Arbitration
Unresolved disputes shall be settled by binding arbitration:
- Single arbitrator for disputes under $500,000 USD
- Three-arbitrator panel for larger disputes
- Rules: International Chamber of Commerce (ICC)
- Location: Bogotá, Colombia or mutually agreed location
- Language: English or Spanish as agreed

### 14.4 Exceptions
The following may be pursued in court without prior mediation/arbitration:
- Injunctive relief for IP or confidentiality breaches
- Collection of undisputed invoices
- Emergency measures to prevent irreparable harm

---

## 15. Governing Law and Jurisdiction

### 15.1 Governing Law
These Terms shall be governed by the laws of Colombia, without regard to conflict of law principles.

### 15.2 International Considerations
For international clients:
- Local law compliance as specified in SOW
- Currency and tax provisions as applicable
- Export control and trade regulation compliance
- Mutual recognition of judgments and awards

### 15.3 Jurisdiction
Subject to arbitration provisions, courts of Bogotá, Colombia shall have exclusive jurisdiction over any legal proceedings.

---

## 16. General Provisions

### 16.1 Entire Agreement
These Terms, together with applicable SOWs, constitute the entire agreement and supersede all prior negotiations, representations, or agreements.

### 16.2 Amendments
Modifications must be in writing and signed by authorized representatives of both parties.

### 16.3 Assignment
Neither party may assign this agreement without prior written consent, except:
- Assignment to affiliates or subsidiaries
- Assignment in connection with merger or acquisition
- Assignment of payment obligations

### 16.4 Severability
If any provision is deemed invalid or unenforceable, the remainder of the agreement shall remain in full force and effect.

### 16.5 Waiver
Failure to enforce any provision shall not constitute a waiver of that provision or any other provision.

### 16.6 Notices
All notices must be in writing and delivered to the addresses specified in the SOW or these Terms.

### 16.7 Survival
The following provisions survive termination: Confidentiality, IP Rights, Payment Obligations, Liability Limitations, and Dispute Resolution.

---

**Contact Information:**
Digital Wave Systems LLC  
Email: <EMAIL>  
Address: 7901 4TH ST N STE 300 ST. PETERSBURG, FL. US 33702
Phone: +57 3053661000

*These Terms are governed by Colombian law and international business practices. Clients are advised to consult with qualified legal counsel regarding their specific rights and obligations.*
