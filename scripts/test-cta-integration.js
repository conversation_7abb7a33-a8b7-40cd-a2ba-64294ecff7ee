#!/usr/bin/env node

/**
 * Test script for CTA integration with n8n webhooks
 * Run with: node scripts/test-cta-integration.js
 */

const https = require('https');
const http = require('http');

// Configuration
const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
const TEST_TIMEOUT = 10000; // 10 seconds

// Test data templates
const testData = {
  leadCapture: {
    name: "Test User",
    email: "<EMAIL>",
    phone: "+1234567890",
    company: "Test Company",
    serviceType: "custom-software",
    projectDescription: "Test project description for automated testing",
    timeline: "3-months",
    budgetRange: "50k-100k",
    companySize: "medium",
    industry: "technology",
    urgencyLevel: "medium",
    preferredContactMethod: "email",
    additionalNotes: "This is a test submission",
    leadSource: "automated-test",
    pageUrl: `${BASE_URL}/test`,
    marketingConsent: true,
    privacyConsent: true
  },
  
  consultationBooking: {
    name: "Test Consultant",
    email: "<EMAIL>",
    phone: "+1234567890",
    company: "Test Consulting Corp",
    jobTitle: "CTO",
    consultationType: "discovery",
    serviceArea: "ai-ml",
    preferredDate: "2024-03-15",
    preferredTime: "14:00",
    timezone: "America/New_York",
    duration: "30min",
    projectDescription: "Test consultation booking",
    currentChallenges: "Testing the booking system",
    desiredOutcomes: "Successful test completion",
    timeline: "immediate",
    budgetRange: "25k-50k",
    meetingType: "video",
    meetingPlatform: "zoom",
    urgencyLevel: "medium",
    additionalNotes: "Automated test booking",
    privacyConsent: true
  },
  
  projectEstimation: {
    name: "Test Estimator",
    email: "<EMAIL>",
    company: "Test Estimation Inc",
    projectType: "web-application",
    projectDescription: "Test project for estimation system validation",
    complexity: "moderate",
    timeline: "6-months",
    budgetRange: "100k-250k",
    platforms: ["web", "mobile"],
    integrations: ["stripe", "hubspot"],
    userBase: "medium",
    complianceRequirements: ["GDPR"],
    existingSystems: "Legacy system",
    currentTechStack: ["React", "Node.js"],
    internalTeamSize: "small",
    technicalExpertise: "medium",
    decisionTimeframe: "1-month",
    privacyConsent: true
  },

  startProject: {
    name: "Test Project Manager",
    email: "<EMAIL>",
    phone: "+1234567890",
    jobTitle: "CTO",
    company: "Test Enterprise Corp",
    companySize: "large",
    industry: "technology",
    projectName: "Enterprise Platform",
    projectType: "enterprise-software",
    projectDescription: "Comprehensive enterprise software solution for managing complex business processes and workflows",
    businessObjectives: "Streamline operations, reduce costs, improve efficiency, and enable digital transformation",
    timeline: "12-18-months",
    budgetRange: "500k-1m",
    platforms: ["web", "mobile-ios", "api"],
    complianceRequirements: ["GDPR", "SOC2"],
    existingSystems: "Legacy ERP system, CRM, and multiple databases",
    performanceRequirements: "Support 10,000+ concurrent users",
    securityRequirements: "Enterprise-grade security with SSO",
    decisionTimeframe: "1-month",
    decisionMakers: "CTO, CEO, and Board of Directors",
    approvalProcess: "Technical review, budget approval, board approval",
    internalTeamSize: "6-10",
    technicalExpertise: "advanced",
    projectManager: true,
    urgencyLevel: "high",
    preferredStartDate: "2024-06-01",
    criticalDeadlines: "Must launch before Q4 2024",
    privacyConsent: true,
    marketingConsent: true
  },

  getQuote: {
    name: "Test Lead",
    email: "<EMAIL>",
    phone: "+1234567890",
    company: "Test Startup",
    serviceInterest: "custom-software",
    consultationType: "video",
    timelineFlexibility: "flexible",
    privacyConsent: true
  }
};

// Utility functions
const makeRequest = (url, data, method = 'POST') => {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'CTA-Integration-Test/1.0'
      },
      timeout: TEST_TIMEOUT
    };

    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
};

const logResult = (testName, success, details) => {
  const status = success ? '✅ PASS' : '❌ FAIL';
  console.log(`${status} ${testName}`);
  if (details) {
    console.log(`   ${details}`);
  }
  console.log('');
};

// Test functions
const testLeadCaptureEndpoint = async () => {
  console.log('🧪 Testing Lead Capture Endpoint...');
  
  try {
    const response = await makeRequest(
      `${BASE_URL}/api/n8n-webhooks/lead-capture`,
      testData.leadCapture
    );
    
    if (response.statusCode === 200 && response.data.success) {
      logResult(
        'Lead Capture API',
        true,
        `Lead ID: ${response.data.leadId}, Score: ${response.data.leadScore}`
      );
      return true;
    } else {
      logResult(
        'Lead Capture API',
        false,
        `Status: ${response.statusCode}, Error: ${response.data.error || 'Unknown error'}`
      );
      return false;
    }
  } catch (error) {
    logResult('Lead Capture API', false, `Error: ${error.message}`);
    return false;
  }
};

const testConsultationBookingEndpoint = async () => {
  console.log('🧪 Testing Consultation Booking Endpoint...');
  
  try {
    const response = await makeRequest(
      `${BASE_URL}/api/n8n-webhooks/consultation-booking`,
      testData.consultationBooking
    );
    
    if (response.statusCode === 200 && response.data.success) {
      logResult(
        'Consultation Booking API',
        true,
        `Booking ID: ${response.data.bookingId}`
      );
      return true;
    } else {
      logResult(
        'Consultation Booking API',
        false,
        `Status: ${response.statusCode}, Error: ${response.data.error || 'Unknown error'}`
      );
      return false;
    }
  } catch (error) {
    logResult('Consultation Booking API', false, `Error: ${error.message}`);
    return false;
  }
};

const testProjectEstimationEndpoint = async () => {
  console.log('🧪 Testing Project Estimation Endpoint...');

  try {
    const response = await makeRequest(
      `${BASE_URL}/api/n8n-webhooks/project-estimation`,
      testData.projectEstimation
    );

    if (response.statusCode === 200 && response.data.success) {
      logResult(
        'Project Estimation API',
        true,
        `Estimation ID: ${response.data.estimationId}`
      );
      return true;
    } else {
      logResult(
        'Project Estimation API',
        false,
        `Status: ${response.statusCode}, Error: ${response.data.error || 'Unknown error'}`
      );
      return false;
    }
  } catch (error) {
    logResult('Project Estimation API', false, `Error: ${error.message}`);
    return false;
  }
};

const testStartProjectEndpoint = async () => {
  console.log('🧪 Testing Start Project Endpoint...');

  try {
    const response = await makeRequest(
      `${BASE_URL}/api/n8n-webhooks/start-project`,
      testData.startProject
    );

    if (response.statusCode === 200 && response.data.success) {
      logResult(
        'Start Project API',
        true,
        `Project ID: ${response.data.projectId}, Score: ${response.data.leadScore}, Priority: ${response.data.priority}`
      );
      return true;
    } else {
      logResult(
        'Start Project API',
        false,
        `Status: ${response.statusCode}, Error: ${response.data.error || 'Unknown error'}`
      );
      return false;
    }
  } catch (error) {
    logResult('Start Project API', false, `Error: ${error.message}`);
    return false;
  }
};

const testGetQuoteEndpoint = async () => {
  console.log('🧪 Testing Get Quote Endpoint...');

  try {
    const response = await makeRequest(
      `${BASE_URL}/api/n8n-webhooks/get-quote`,
      testData.getQuote
    );

    if (response.statusCode === 200 && response.data.success) {
      logResult(
        'Get Quote API',
        true,
        `Quote ID: ${response.data.quoteId}, Score: ${response.data.leadScore}, Sequence: ${response.data.nurturingSequence}`
      );
      return true;
    } else {
      logResult(
        'Get Quote API',
        false,
        `Status: ${response.statusCode}, Error: ${response.data.error || 'Unknown error'}`
      );
      return false;
    }
  } catch (error) {
    logResult('Get Quote API', false, `Error: ${error.message}`);
    return false;
  }
};

const testValidationErrors = async () => {
  console.log('🧪 Testing Validation Errors...');
  
  const invalidData = {
    name: "", // Invalid: empty name
    email: "invalid-email", // Invalid: bad email format
    serviceType: "", // Invalid: empty service type
    privacyConsent: false // Invalid: must be true
  };
  
  try {
    const response = await makeRequest(
      `${BASE_URL}/api/n8n-webhooks/lead-capture`,
      invalidData
    );
    
    if (response.statusCode === 400 && !response.data.success) {
      logResult(
        'Validation Error Handling',
        true,
        'Correctly rejected invalid data'
      );
      return true;
    } else {
      logResult(
        'Validation Error Handling',
        false,
        'Should have rejected invalid data'
      );
      return false;
    }
  } catch (error) {
    logResult('Validation Error Handling', false, `Error: ${error.message}`);
    return false;
  }
};

const testRateLimiting = async () => {
  console.log('🧪 Testing Rate Limiting...');
  
  const requests = [];
  const numRequests = 15; // Exceed typical rate limit
  
  for (let i = 0; i < numRequests; i++) {
    requests.push(
      makeRequest(
        `${BASE_URL}/api/n8n-webhooks/lead-capture`,
        { ...testData.leadCapture, email: `test${i}@example.com` }
      )
    );
  }
  
  try {
    const responses = await Promise.allSettled(requests);
    const rateLimitedResponses = responses.filter(
      result => result.status === 'fulfilled' && result.value.statusCode === 429
    );
    
    if (rateLimitedResponses.length > 0) {
      logResult(
        'Rate Limiting',
        true,
        `${rateLimitedResponses.length} requests were rate limited`
      );
      return true;
    } else {
      logResult(
        'Rate Limiting',
        false,
        'No rate limiting detected (may not be configured)'
      );
      return false;
    }
  } catch (error) {
    logResult('Rate Limiting', false, `Error: ${error.message}`);
    return false;
  }
};

const testEnvironmentConfiguration = () => {
  console.log('🧪 Testing Environment Configuration...');
  
  const requiredEnvVars = [
    'N8N_WEBHOOK_SECRET'
  ];
  
  const optionalEnvVars = [
    'N8N_LEAD_CAPTURE_WEBHOOK_URL',
    'N8N_CONSULTATION_WEBHOOK_URL',
    'N8N_ESTIMATION_WEBHOOK_URL'
  ];
  
  let allRequired = true;
  let warnings = [];
  
  // Check required variables
  requiredEnvVars.forEach(varName => {
    if (!process.env[varName]) {
      logResult(`Environment Variable: ${varName}`, false, 'Required but not set');
      allRequired = false;
    } else {
      logResult(`Environment Variable: ${varName}`, true, 'Set');
    }
  });
  
  // Check optional variables
  optionalEnvVars.forEach(varName => {
    if (!process.env[varName]) {
      warnings.push(`Optional variable ${varName} is not set`);
    } else {
      logResult(`Environment Variable: ${varName}`, true, 'Set');
    }
  });
  
  if (warnings.length > 0) {
    console.log('⚠️  Warnings:');
    warnings.forEach(warning => console.log(`   ${warning}`));
    console.log('');
  }
  
  return allRequired;
};

// Main test runner
const runTests = async () => {
  console.log('🚀 Starting CTA Integration Tests\n');
  console.log(`Testing against: ${BASE_URL}\n`);
  
  const results = [];
  
  // Environment tests
  results.push(testEnvironmentConfiguration());
  
  // API endpoint tests
  results.push(await testLeadCaptureEndpoint());
  results.push(await testConsultationBookingEndpoint());
  results.push(await testProjectEstimationEndpoint());
  results.push(await testStartProjectEndpoint());
  results.push(await testGetQuoteEndpoint());
  
  // Validation tests
  results.push(await testValidationErrors());
  
  // Performance tests
  results.push(await testRateLimiting());
  
  // Summary
  const passed = results.filter(Boolean).length;
  const total = results.length;
  
  console.log('📊 Test Summary');
  console.log('================');
  console.log(`Total Tests: ${total}`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${total - passed}`);
  console.log(`Success Rate: ${Math.round((passed / total) * 100)}%`);
  
  if (passed === total) {
    console.log('\n🎉 All tests passed!');
    process.exit(0);
  } else {
    console.log('\n❌ Some tests failed. Please review the results above.');
    process.exit(1);
  }
};

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('Test runner error:', error);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  testLeadCaptureEndpoint,
  testConsultationBookingEndpoint,
  testProjectEstimationEndpoint,
  testStartProjectEndpoint,
  testGetQuoteEndpoint,
  testValidationErrors,
  testRateLimiting,
  testEnvironmentConfiguration
};
