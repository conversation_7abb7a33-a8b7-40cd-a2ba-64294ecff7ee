# Robots.txt for Digital Wave Systems
# Software Development & Consulting Company
# https://digitalwavesystems.com.co

# Allow all search engines to crawl the site
User-agent: *
Allow: /

# Block access to sensitive directories and files
Disallow: /api/
Disallow: /_next/
Disallow: /admin/
Disallow: /private/
Disallow: /.env
Disallow: /.env.local
Disallow: /.env.production
Disallow: /node_modules/
Disallow: /src/
Disallow: /docs/
Disallow: /markdown/
Disallow: /temp/
Disallow: /tmp/

# Block access to development and testing files
Disallow: /*.json$
Disallow: /*.config.js$
Disallow: /*.config.ts$
Disallow: /package.json
Disallow: /package-lock.json
Disallow: /yarn.lock
Disallow: /tsconfig.json
Disallow: /next.config.js
Disallow: /tailwind.config.ts

# Block access to version control
Disallow: /.git/
Disallow: /.gitignore

# Allow access to important SEO files
Allow: /sitemap.xml
Allow: /sitemap-es.xml
Allow: /sitemap-index.xml
Allow: /robots.txt
Allow: /favicon.ico

# Allow access to images and assets
Allow: /images/
Allow: /icons/
Allow: /assets/

# Specific rules for major search engines
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Slurp
Allow: /
Crawl-delay: 2

User-agent: DuckDuckBot
Allow: /
Crawl-delay: 1

User-agent: Baiduspider
Allow: /
Crawl-delay: 2

User-agent: YandexBot
Allow: /
Crawl-delay: 2

# Block problematic bots
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

User-agent: SemrushBot
Disallow: /

User-agent: MegaIndex
Disallow: /

# Sitemap locations
Sitemap: https://digitalwavesystems.com.co/sitemap-index.xml
Sitemap: https://digitalwavesystems.com.co/sitemap.xml
Sitemap: https://digitalwavesystems.com.co/sitemap-es.xml

# Host directive (preferred domain)
Host: https://digitalwavesystems.com.co
