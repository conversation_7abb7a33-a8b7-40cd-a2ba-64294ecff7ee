# Google Search Console Setup Guide for Digital Wave Systems

## Overview

This guide provides step-by-step instructions for setting up Google Search Console for the Digital Wave Systems website, including verification, sitemap submission, and ongoing monitoring.

## Table of Contents

1. [Initial Setup](#initial-setup)
2. [Domain Verification](#domain-verification)
3. [Sitemap Submission](#sitemap-submission)
4. [International Targeting](#international-targeting)
5. [Performance Monitoring](#performance-monitoring)
6. [Troubleshooting](#troubleshooting)

## Initial Setup

### Step 1: Access Google Search Console

1. Go to [Google Search Console](https://search.google.com/search-console)
2. Sign in with your Google account (use the company Google account)
3. Click "Add Property"

### Step 2: Add Your Property

Choose **URL prefix** method and enter:
```
https://digitalwavesystems.com.co
```

## Domain Verification

### Method 1: HTML File Upload (Recommended)

1. Download the verification file from Google Search Console
2. Replace the placeholder file at `/public/google-site-verification.html` with the downloaded file
3. Ensure the file is accessible at: `https://digitalwavesystems.com.co/google[verification-code].html`
4. Click "Verify" in Google Search Console

### Method 2: Meta Tag (Alternative)

1. Copy the meta tag provided by Google Search Console
2. Add it to the SEOHead component or update the verification code in `src/utils/seo.ts`:

```typescript
// In src/utils/seo.ts, update the verification object:
verification: {
  google: 'your-actual-verification-code-here',
},
```

### Method 3: DNS Verification (For Domain-level Access)

1. Add a TXT record to your DNS settings:
   - Name: `@` or leave blank
   - Value: `google-site-verification=your-verification-code`
2. Wait for DNS propagation (up to 24 hours)
3. Click "Verify" in Google Search Console

## Sitemap Submission

### Step 1: Submit Main Sitemap Index

1. In Google Search Console, go to "Sitemaps" in the left sidebar
2. Add the main sitemap index:
   ```
   https://digitalwavesystems.com.co/sitemap-index.xml
   ```

### Step 2: Submit Individual Sitemaps

Add these individual sitemaps:

1. **English Sitemap:**
   ```
   https://digitalwavesystems.com.co/sitemap.xml
   ```

2. **Spanish Sitemap:**
   ```
   https://digitalwavesystems.com.co/sitemap-es.xml
   ```

### Step 3: Verify Sitemap Status

- Check that all sitemaps show "Success" status
- Monitor for any errors or warnings
- Verify that the number of submitted URLs matches your expectations

## International Targeting

### Step 1: Set Up Properties for Each Language

Create separate properties in Google Search Console:

1. **Main Property (English):**
   ```
   https://digitalwavesystems.com.co
   ```

2. **Spanish Property:**
   ```
   https://digitalwavesystems.com.co/es/
   ```

### Step 2: Configure International Targeting

For the Spanish property:
1. Go to "Settings" → "International Targeting"
2. Check "Country" and select "Colombia" (or your target Spanish-speaking country)
3. The hreflang tags in your sitemaps will handle language targeting automatically

## Performance Monitoring

### Key Metrics to Monitor

1. **Search Performance:**
   - Total clicks
   - Total impressions
   - Average CTR
   - Average position

2. **Coverage:**
   - Valid pages
   - Error pages
   - Excluded pages
   - Warnings

3. **Core Web Vitals:**
   - Largest Contentful Paint (LCP)
   - First Input Delay (FID)
   - Cumulative Layout Shift (CLS)

### Regular Monitoring Tasks

1. **Weekly:**
   - Check for new coverage issues
   - Monitor search performance trends
   - Review Core Web Vitals

2. **Monthly:**
   - Analyze top-performing pages
   - Review search queries
   - Check for manual actions

3. **Quarterly:**
   - Review international targeting performance
   - Analyze seasonal trends
   - Update sitemaps if new pages added

## Troubleshooting

### Common Issues and Solutions

#### Sitemap Not Found
- Verify the sitemap URL is accessible
- Check robots.txt allows sitemap access
- Ensure proper XML formatting

#### Verification Failed
- Clear browser cache and try again
- Check that verification file/meta tag is properly placed
- Wait 24-48 hours for DNS changes to propagate

#### Pages Not Indexed
- Check robots.txt isn't blocking pages
- Verify meta robots tags allow indexing
- Submit individual URLs for indexing

#### Hreflang Errors
- Verify hreflang tags are properly implemented
- Check that alternate language pages exist
- Ensure x-default points to the correct default language

### Contact Information

For technical issues related to SEO implementation:
- Development Team: [<EMAIL>]
- SEO Specialist: [<EMAIL>]

## Additional Resources

- [Google Search Console Help](https://support.google.com/webmasters)
- [Google SEO Starter Guide](https://developers.google.com/search/docs/beginner/seo-starter-guide)
- [International SEO Guidelines](https://developers.google.com/search/docs/advanced/crawling/managing-multi-regional-sites)

## Verification Checklist

- [ ] Google Search Console property created
- [ ] Domain ownership verified
- [ ] Main sitemap index submitted
- [ ] Individual language sitemaps submitted
- [ ] International targeting configured
- [ ] Performance monitoring dashboard set up
- [ ] Regular monitoring schedule established
- [ ] Team access permissions configured
