# Quick Quote Implementation Guide

## Overview

The Quick Quote form is a simplified, conversion-focused CTA component designed to capture leads through immediate consultation options rather than detailed requirements gathering. This approach prioritizes getting visitors to commit to a conversation where the sales process can begin.

## Design Philosophy

### Conversion-First Approach
- **Minimal friction**: Only essential fields to reduce abandonment
- **Immediate value**: Offer instant consultation scheduling or callback
- **Clear next steps**: Visitors know exactly what happens after submission

### Two-Path Strategy
1. **Video Call Path**: Calendly integration for self-service scheduling
2. **Phone Call Path**: Immediate callback through n8n automation

## Component Structure

### QuickQuoteForm.tsx
Main form component with conditional display based on consultation type.

**Key Features:**
- Progressive disclosure (show relevant fields based on selection)
- Real-time validation with HeroUI components
- Conditional Calendly widget integration
- n8n webhook integration for phone callbacks

### QuickQuoteCTA.tsx
Reusable CTA button component that opens the Quick Quote form in a modal.

**Preset Variations:**
- `GetQuoteButton` - Standard quote request
- `TalkToExpertButton` - Expert consultation focus
- `StartProjectButton` - Project initiation focus
- `HeroQuoteButton` - Hero section styling
- `CallNowButton` - Urgency-focused callback

## Form Fields Strategy

### Essential Fields Only
```typescript
interface QuoteFormData {
  // Contact (Required)
  name: string;
  email: string;
  phone: string; // Required only for phone consultations
  company: string; // Optional
  
  // Consultation Preference (Required)
  consultationType: 'video' | 'phone';
  
  // Context (Optional but valuable)
  serviceInterest: string;
  urgency: 'asap' | 'this-week' | 'this-month' | 'exploring';
  
  // Legal (Required)
  privacyConsent: boolean;
}
```

### Why These Fields?
- **Contact Info**: Minimum needed to reach the prospect
- **Consultation Type**: Determines the automation workflow
- **Service Interest**: Helps with lead routing and preparation
- **Urgency**: Affects callback timing and lead priority
- **Privacy Consent**: Legal requirement

## Consultation Workflows

### Video Call Workflow
1. **Form Submission** → Lead data sent to n8n
2. **Calendly Display** → User selects available time slot
3. **Confirmation** → Meeting details sent via email
4. **Preparation** → Pre-meeting questionnaire sent
5. **Follow-up** → Automated reminders and materials

### Phone Call Workflow
1. **Form Submission** → Immediate n8n trigger
2. **Lead Routing** → Based on urgency and service interest
3. **Callback Scheduling** → Automated callback within specified timeframe
4. **CRM Integration** → Lead details added to sales pipeline
5. **Follow-up** → Post-call automation sequence

## n8n Integration

### API Endpoint: `/api/n8n-webhooks/quick-quote`

**Payload Structure:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+**********",
  "company": "Example Corp",
  "consultationType": "phone",
  "serviceInterest": "custom-software",
  "urgency": "this-week",
  "leadSource": "hero-section",
  "pageUrl": "https://digitalwavesystems.com.co",
  "timestamp": "2024-01-15T10:30:00Z",
  "intent": "request_immediate_callback",
  "privacyConsent": true
}
```

### Lead Scoring Algorithm
```typescript
function calculateQuickLeadScore(data: QuickQuoteData): number {
  let score = 30; // Base score
  
  // Urgency scoring (10-40 points)
  score += urgencyScores[data.urgency];
  
  // Consultation type (15-20 points)
  score += data.consultationType === 'phone' ? 20 : 15;
  
  // Service interest (5-25 points)
  score += serviceScores[data.serviceInterest] || 10;
  
  // Additional factors (5-10 points each)
  if (data.company) score += 10;
  if (data.phone) score += 5;
  
  return Math.min(score, 100);
}
```

### Callback Timing Logic
```typescript
const callbackTimes = {
  'asap': 'within 15 minutes',      // Immediate workflow
  'this-week': 'within 2 hours',   // Priority workflow  
  'this-month': 'within 24 hours', // Standard workflow
  'exploring': 'within 48 hours'   // Exploratory workflow
};
```

## Calendly Integration

### Configuration
```typescript
const calendlyUrl = `https://calendly.com/your-link?
  hide_event_type_details=1&
  hide_gdpr_banner=1&
  prefill_name=${encodeURIComponent(name)}&
  prefill_email=${encodeURIComponent(email)}`;
```

### Best Practices
- **Pre-fill contact information** to reduce friction
- **Hide unnecessary details** to focus on scheduling
- **Responsive iframe** for mobile compatibility
- **Fallback option** to switch to phone call if needed

## HeroUI Design Compliance

### Component Styling
All components use HeroUI design tokens and components:

```tsx
// Form styling
<Card className="w-full max-w-2xl mx-auto">
  <CardBody className="p-6">
    <Input variant="flat" />
    <Select variant="flat" />
    <RadioGroup orientation="horizontal" />
    <Button color="primary" size="lg" />
  </CardBody>
</Card>

// Notification styling  
<Card className="border-primary/20 bg-primary/5">
  <CardBody className="p-4">
    <div className="flex items-center gap-2 text-primary">
      <PhoneIcon className="h-5 w-5" />
      <span className="font-medium">Message</span>
    </div>
  </CardBody>
</Card>
```

### Color Scheme
- **Primary**: `#3d4afc` (brand color)
- **Text**: `text-dark dark:text-white`
- **Body**: `text-body-color dark:text-dark-6`
- **Backgrounds**: `bg-white dark:bg-dark`

## Analytics & Tracking

### Event Tracking
```typescript
// Button clicks
gtag('event', 'quick_quote_clicked', {
  event_category: 'CTA',
  event_label: 'quick_quote',
  custom_parameter_1: source,
  value: 1
});

// Form submissions
gtag('event', 'conversion', {
  event_category: 'Lead Generation',
  event_label: `quick_quote_${consultationType}`,
  value: 1
});
```

### Key Metrics to Track
- **Form completion rate** by source
- **Consultation type preference** (video vs phone)
- **Urgency distribution** across leads
- **Callback success rate** for phone requests
- **Meeting show rate** for video consultations

## Implementation Examples

### Hero Section
```tsx
<HeroQuoteButton
  ctaText="Start Your Software Project"
  source="hero-section"
  analyticsEvent="hero_start_project"
/>
```

### Service Cards
```tsx
<QuickQuoteCTA
  variant="primary"
  className="w-full"
  ctaText="Get Quote for AI Solutions"
  source="services-ai-ml"
  analyticsEvent="service_ai_quote"
/>
```

### Floating CTA
```tsx
<QuickQuoteCTA
  variant="primary"
  size="lg"
  className="fixed bottom-6 right-6 z-50 shadow-2xl"
  ctaText="Get Quick Quote"
  source="floating-cta"
/>
```

## Testing Strategy

### A/B Testing Opportunities
1. **CTA Text Variations**:
   - "Get Development Quote" vs "Talk to an Expert"
   - "Start Your Project" vs "Get Free Consultation"

2. **Form Length**:
   - Current minimal form vs even shorter version
   - Optional company field vs required

3. **Consultation Options**:
   - Video-first vs phone-first presentation
   - Immediate callback vs scheduled callback

### Conversion Optimization
- **Mobile responsiveness** testing
- **Form field validation** timing
- **Loading state** feedback
- **Error message** clarity

## Success Metrics

### Primary KPIs
- **Form completion rate**: Target >15%
- **Lead quality score**: Average >60
- **Callback connection rate**: >80% for phone requests
- **Meeting show rate**: >70% for video consultations

### Secondary Metrics
- **Time to form completion**: <2 minutes
- **Mobile conversion rate**: Within 10% of desktop
- **Source attribution accuracy**: 100%
- **n8n workflow success rate**: >95%

## Maintenance & Updates

### Regular Reviews
- **Monthly**: Conversion rate analysis
- **Quarterly**: Form field optimization
- **Bi-annually**: Calendly integration updates
- **Annually**: Complete UX review

### Optimization Opportunities
- **Dynamic urgency options** based on time of day
- **Smart service suggestions** based on page context
- **Progressive profiling** for return visitors
- **Personalized callback timing** based on timezone

This implementation focuses on conversion optimization while maintaining the professional HeroUI design system and providing immediate value to website visitors through streamlined consultation booking.
