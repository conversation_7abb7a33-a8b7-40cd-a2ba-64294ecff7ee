# n8n Integration Guide for Digital Wave Systems

## Overview

This guide provides comprehensive documentation for integrating Digital Wave Systems' website with n8n automation workflows. The integration enables automated lead capture, consultation booking, project estimation, and follow-up sequences.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [API Endpoints](#api-endpoints)
3. [n8n Workflow Setup](#n8n-workflow-setup)
4. [Environment Configuration](#environment-configuration)
5. [Data Flow](#data-flow)
6. [Testing](#testing)
7. [Troubleshooting](#troubleshooting)

## Architecture Overview

The integration consists of:

- **Frontend CTA Components**: Enhanced forms and buttons that collect user data
- **API Endpoints**: Next.js API routes that process form submissions and send data to n8n
- **n8n Webhooks**: Automation workflows that handle lead processing, email sequences, and CRM integration
- **Utility Functions**: Helper functions for data validation, sanitization, and webhook management

### Component Structure

```
src/
├── components/
│   └── CTAForms/
│       ├── LeadCaptureForm.tsx          # Multi-step lead capture form
│       ├── ConsultationBookingForm.tsx  # Consultation scheduling form
│       └── EnhancedCTAButton.tsx        # Configurable CTA button component
├── app/api/n8n-webhooks/
│   ├── lead-capture/route.ts            # Lead capture API endpoint
│   ├── consultation-booking/route.ts    # Consultation booking API endpoint
│   └── project-estimation/route.ts      # Project estimation API endpoint
└── utils/
    └── n8nIntegration.ts                # Utility functions for n8n integration
```

## API Endpoints

### 1. Lead Capture Endpoint

**URL**: `/api/n8n-webhooks/lead-capture`
**Method**: POST

**Request Body**:
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "company": "Example Corp",
  "serviceType": "custom-software",
  "projectDescription": "Need a web application",
  "timeline": "3-months",
  "budgetRange": "50k-100k",
  "companySize": "medium",
  "industry": "technology",
  "urgencyLevel": "medium",
  "preferredContactMethod": "email",
  "additionalNotes": "Looking for React expertise",
  "leadSource": "website",
  "pageUrl": "https://digitalwavesystems.com",
  "marketingConsent": true,
  "privacyConsent": true
}
```

**Response**:
```json
{
  "success": true,
  "message": "Lead captured successfully",
  "leadId": "lead_1234567890_abc123",
  "leadScore": 75,
  "leadPriority": "high",
  "nextSteps": [
    "Thank you for your interest in our services",
    "A senior consultant will contact you within 2 hours",
    "Please check your email for a calendar link to schedule a discovery call"
  ]
}
```

### 2. Consultation Booking Endpoint

**URL**: `/api/n8n-webhooks/consultation-booking`
**Method**: POST

**Request Body**:
```json
{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "company": "Tech Startup",
  "jobTitle": "CTO",
  "consultationType": "technical-review",
  "serviceArea": "ai-ml",
  "preferredDate": "2024-02-15",
  "preferredTime": "14:00",
  "timezone": "America/New_York",
  "duration": "60min",
  "projectDescription": "AI-powered recommendation system",
  "currentChallenges": "Scalability issues with current ML pipeline",
  "desiredOutcomes": "Improved performance and accuracy",
  "timeline": "6-months",
  "budgetRange": "100k-250k",
  "meetingType": "video",
  "meetingPlatform": "zoom",
  "urgencyLevel": "high",
  "additionalNotes": "Need to discuss data privacy compliance",
  "privacyConsent": true
}
```

### 3. Project Estimation Endpoint

**URL**: `/api/n8n-webhooks/project-estimation`
**Method**: POST

**Request Body**:
```json
{
  "name": "Bob Johnson",
  "email": "<EMAIL>",
  "company": "Enterprise Corp",
  "projectType": "enterprise-software",
  "projectDescription": "Customer management system with AI features",
  "complexity": "complex",
  "timeline": "1-year+",
  "budgetRange": "500k+",
  "platforms": ["web", "mobile", "api"],
  "integrations": ["salesforce", "hubspot", "stripe"],
  "userBase": "enterprise",
  "complianceRequirements": ["GDPR", "SOC2"],
  "existingSystems": "Legacy CRM system",
  "currentTechStack": ["Java", "Oracle", "Angular"],
  "internalTeamSize": "large",
  "technicalExpertise": "high",
  "decisionTimeframe": "3-months",
  "privacyConsent": true
}
```

## n8n Workflow Setup

### 1. Lead Capture Workflow

**Webhook Trigger**: Receives lead data from the website
**Workflow Steps**:

1. **Data Validation**: Validate incoming lead data
2. **Lead Scoring**: Calculate lead score based on criteria
3. **CRM Integration**: Add lead to CRM (HubSpot, Salesforce, etc.)
4. **Lead Routing**: Route to appropriate sales team member
5. **Email Automation**: Send welcome email and nurture sequence
6. **Slack Notification**: Notify sales team of new lead
7. **Calendar Integration**: Schedule follow-up tasks

**Example n8n Workflow JSON**:
```json
{
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "lead-capture",
        "responseMode": "responseNode"
      },
      "name": "Webhook",
      "type": "n8n-nodes-base.webhook"
    },
    {
      "parameters": {
        "conditions": {
          "number": [
            {
              "value1": "={{$json.leadScore}}",
              "operation": "larger",
              "value2": 70
            }
          ]
        }
      },
      "name": "High Priority Lead?",
      "type": "n8n-nodes-base.if"
    },
    {
      "parameters": {
        "resource": "contact",
        "operation": "create",
        "email": "={{$json.email}}",
        "additionalFields": {
          "firstName": "={{$json.name.split(' ')[0]}}",
          "lastName": "={{$json.name.split(' ')[1]}}",
          "company": "={{$json.company}}",
          "phone": "={{$json.phone}}"
        }
      },
      "name": "Create HubSpot Contact",
      "type": "n8n-nodes-base.hubspot"
    }
  ]
}
```

### 2. Consultation Booking Workflow

**Workflow Steps**:
1. **Calendar Check**: Check team availability
2. **Meeting Creation**: Create calendar event
3. **Confirmation Email**: Send meeting confirmation
4. **Preparation Materials**: Send pre-meeting questionnaire
5. **Reminder Setup**: Schedule meeting reminders
6. **CRM Update**: Update contact record with meeting details

### 3. Project Estimation Workflow

**Workflow Steps**:
1. **Requirements Analysis**: Process project requirements
2. **Complexity Assessment**: Calculate project complexity
3. **Resource Planning**: Determine required resources
4. **Proposal Generation**: Create preliminary proposal
5. **Review Assignment**: Assign to technical lead for review
6. **Client Communication**: Send estimation timeline to client

## Environment Configuration

### Required Environment Variables

```bash
# n8n Integration
N8N_WEBHOOK_SECRET="your_secure_webhook_secret"
N8N_LEAD_CAPTURE_WEBHOOK_URL="https://your-n8n.com/webhook/lead-capture"
N8N_CONSULTATION_WEBHOOK_URL="https://your-n8n.com/webhook/consultation"
N8N_ESTIMATION_WEBHOOK_URL="https://your-n8n.com/webhook/estimation"

# Optional: Additional integrations
N8N_PAYMENT_WEBHOOK_URL="https://your-n8n.com/webhook/payment"
N8N_SUPPORT_WEBHOOK_URL="https://your-n8n.com/webhook/support"
```

### n8n Instance Setup

1. **Create n8n Instance**: Deploy n8n on your preferred platform
2. **Configure Webhooks**: Set up webhook endpoints for each workflow
3. **Set Authentication**: Configure webhook authentication using the secret key
4. **Test Connections**: Verify webhook connectivity

## Data Flow

### Lead Capture Flow

```mermaid
graph TD
    A[User Fills Form] --> B[Frontend Validation]
    B --> C[Submit to API]
    C --> D[Server Validation]
    D --> E[Calculate Lead Score]
    E --> F[Send to n8n Webhook]
    F --> G[n8n Processes Lead]
    G --> H[CRM Integration]
    G --> I[Email Automation]
    G --> J[Team Notification]
    G --> K[Follow-up Scheduling]
```

### Consultation Booking Flow

```mermaid
graph TD
    A[User Books Consultation] --> B[Form Validation]
    B --> C[Submit to API]
    C --> D[Generate Meeting Agenda]
    D --> E[Send to n8n Webhook]
    E --> F[Check Calendar Availability]
    F --> G[Create Meeting]
    G --> H[Send Confirmation]
    H --> I[Schedule Reminders]
    I --> J[Send Preparation Materials]
```

## Testing

### Manual Testing

1. **Form Submission Testing**:
   - Test each form with valid data
   - Test validation with invalid data
   - Verify error handling

2. **API Endpoint Testing**:
   - Use tools like Postman or curl
   - Test with various payload combinations
   - Verify response formats

3. **n8n Workflow Testing**:
   - Test webhook triggers
   - Verify data processing
   - Check integrations (CRM, email, etc.)

### Automated Testing

Create test scripts for:
- Form validation
- API endpoint responses
- Webhook delivery
- Data integrity

### Example Test Script

```javascript
// Test lead capture endpoint
const testLeadCapture = async () => {
  const testData = {
    name: "Test User",
    email: "<EMAIL>",
    serviceType: "custom-software",
    timeline: "3-months",
    budgetRange: "50k-100k",
    privacyConsent: true
  };

  const response = await fetch('/api/n8n-webhooks/lead-capture', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(testData)
  });

  const result = await response.json();
  console.log('Lead capture test:', result);
};
```

## Troubleshooting

### Common Issues

1. **Webhook Not Receiving Data**:
   - Check n8n webhook URL
   - Verify authentication token
   - Check network connectivity

2. **Form Validation Errors**:
   - Review required fields
   - Check data types
   - Verify email format

3. **n8n Workflow Failures**:
   - Check workflow logs
   - Verify node configurations
   - Test individual nodes

### Debugging Tips

1. **Enable Logging**: Add console.log statements in API routes
2. **Use n8n Logs**: Monitor n8n execution logs
3. **Test Incrementally**: Test each component separately
4. **Validate Data**: Ensure data format matches expectations

### Support

For technical support:
- Check n8n documentation: https://docs.n8n.io/
- Review API endpoint logs
- Contact development team for custom issues

## Security Considerations

1. **Webhook Authentication**: Always use secure tokens
2. **Data Validation**: Validate all incoming data
3. **Rate Limiting**: Implement rate limiting on endpoints
4. **HTTPS Only**: Use HTTPS for all webhook communications
5. **Data Sanitization**: Sanitize data before processing

## Performance Optimization

1. **Async Processing**: Use async/await for webhook calls
2. **Error Handling**: Implement proper error handling and retries
3. **Caching**: Cache frequently accessed data
4. **Monitoring**: Monitor webhook performance and response times
