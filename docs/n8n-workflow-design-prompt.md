# Comprehensive n8n Workflow Design Prompt for Digital Wave Systems

## Project Context

You are tasked with designing professional n8n automation workflows for Digital Wave Systems, a software development consultancy that has implemented a sophisticated lead capture and CTA system on their website. The system includes multiple form types with different user intent levels and requires corresponding automation workflows to handle leads effectively.

## Current Implementation Overview

### Website Architecture
- **Technology Stack**: Next.js 14, TypeScript, HeroUI design system
- **Primary Color**: #3d4afc (brand color)
- **CTA Strategy**: Intent-based form selection with progressive disclosure
- **Lead Scoring**: Differentiated algorithms based on form completion depth

### Implemented CTA Forms

#### 1. "Start Your Project" Form (High-Intent Sales)
- **Purpose**: Enterprise sales qualification for immediate project needs
- **Target**: Decision makers with budget approval and defined requirements
- **Form Type**: 5-step multi-step form (25+ fields)
- **Lead Scoring**: 50-100 points (budget-weighted, enterprise-focused)
- **API Endpoint**: `/api/n8n-webhooks/start-project`
- **Expected Volume**: Low volume, high quality (8-12% conversion)
- **Average Deal Size**: $100K+ (enterprise focus)

#### 2. "Get Development Quote" Form (Lead Generation)
- **Purpose**: Lead nurturing for early-stage prospects
- **Target**: Researchers exploring solutions without immediate budget
- **Form Type**: Single-step minimal form (8 core fields)
- **Lead Scoring**: 20-100 points (engagement-focused)
- **API Endpoint**: `/api/n8n-webhooks/get-quote`
- **Expected Volume**: High volume, mixed quality (25-35% conversion)
- **Nurture Cycle**: 15-20% lead-to-customer over longer cycle

#### 3. Quick Quote Consultation Form
- **Purpose**: Immediate consultation scheduling with video/phone options
- **Features**: Calendly integration for video, callback automation for phone
- **API Endpoint**: `/api/n8n-webhooks/quick-quote`
- **Consultation Types**: Video (Calendly), Phone (immediate callback), Email

#### 4. General Lead Capture Form
- **Purpose**: Comprehensive lead qualification and routing
- **API Endpoint**: `/api/n8n-webhooks/lead-capture`
- **Features**: Progressive profiling, UTM tracking, service-specific routing

### Lead Scoring Algorithms

#### High-Intent Scoring (Start Project)
```javascript
Base Score: 50 points
+ Budget Range: up to 30 points (2M+ = 30, 500K-1M = 20, etc.)
+ Company Size: up to 20 points (enterprise = 20, large = 15, etc.)
+ Urgency Level: up to 15 points (urgent = 15, high = 10, etc.)
+ Decision Speed: up to 15 points (1 week = 15, 1 month = 8, etc.)
+ Complexity Bonus: up to 20 points (compliance, platforms, integrations)
```

#### Lead Generation Scoring (Get Quote)
```javascript
Base Score: 20 points
+ Service Interest: up to 25 points (enterprise = 25, custom = 20, etc.)
+ Timeline Urgency: up to 25 points (immediate = 25, flexible = 15, etc.)
+ Consultation Type: up to 20 points (video = 20, phone = 15, email = 5)
+ Context Bonus: up to 15 points (company info, phone provided)
```

### Sales Routing Logic

#### High-Intent Routing
- **Score 85+ or Urgent**: Executive team, 1-hour response, executive engagement
- **Score 70+ or High**: Senior sales, 2-hour response, high-value prospect
- **Score 55+**: Sales team, 4-hour response, qualified lead
- **Score <55**: Sales team, 8-hour response, standard nurture

#### Lead Generation Routing
- **High-Intent Nurture**: Solution-focused content, 4-hour response
- **Qualified Lead Nurture**: Educational with solutions, 12-hour response
- **Educational Nurture**: Pure educational content, 48-hour response

## Required n8n Workflow Designs

### 1. High-Intent "Start Your Project" Workflow

**Objective**: Automate enterprise sales process for high-value project inquiries

**Required Features**:
- **Webhook Trigger**: Receive data from `/api/n8n-webhooks/start-project`
- **Data Validation**: Validate all 25+ form fields with error handling
- **Lead Scoring**: Implement high-intent scoring algorithm
- **Executive Routing**: Route score 85+ to executive team within 1 hour
- **CRM Integration**: Create detailed opportunity in HubSpot/Salesforce with:
  - Complete project requirements
  - Technical specifications
  - Decision maker information
  - Budget and timeline data
- **Immediate Notifications**:
  - Slack alert to appropriate team (executive/senior sales/sales)
  - Email notification with lead summary and next actions
  - Calendar booking for discovery call within response timeframe
- **Follow-up Automation**:
  - Send confirmation email to prospect with next steps
  - Schedule automated follow-up reminders
  - Create tasks for assigned team member
- **Proposal Workflow**: Trigger proposal generation process for qualified leads

### 2. Lead Generation "Get Development Quote" Workflow

**Objective**: Nurture early-stage prospects with educational content sequences

**Required Features**:
- **Webhook Trigger**: Receive data from `/api/n8n-webhooks/get-quote`
- **Lead Scoring**: Implement lead generation scoring algorithm
- **Nurture Sequence Selection**: Route to appropriate nurture track based on score
- **CRM Integration**: Create contact/lead in CRM with nurture status
- **Content Delivery**:
  - Send personalized welcome email with relevant resources
  - Deliver service-specific educational content
  - Schedule progressive content sequence over 30-60 days
- **Engagement Tracking**: Monitor email opens, clicks, and content downloads
- **Progressive Qualification**: Move engaged leads to sales-qualified status
- **Consultation Scheduling**: Offer consultation when engagement threshold met

### 3. Quick Quote Consultation Workflow

**Objective**: Handle immediate consultation requests with video/phone options

**Required Features**:
- **Webhook Trigger**: Receive data from `/api/n8n-webhooks/quick-quote`
- **Consultation Type Routing**:
  - **Video**: Integrate with Calendly API for booking confirmation
  - **Phone**: Trigger immediate callback workflow
  - **Email**: Route to standard lead nurture
- **Calendar Integration**: 
  - Create calendar events for confirmed consultations
  - Send calendar invitations to both prospect and consultant
  - Set up automated reminders (24h, 2h before)
- **Preparation Automation**:
  - Send pre-consultation questionnaire
  - Provide relevant case studies and materials
  - Brief assigned consultant with lead context
- **Follow-up Sequence**: Post-consultation proposal and next steps

### 4. General Lead Capture Workflow

**Objective**: Comprehensive lead qualification and routing system

**Required Features**:
- **Webhook Trigger**: Receive data from `/api/n8n-webhooks/lead-capture`
- **UTM Attribution**: Capture and store campaign source data
- **Service-Specific Routing**: Route leads based on service interest
- **Lead Qualification**: Score and categorize leads for appropriate follow-up
- **Multi-Channel Notifications**: Slack, email, and CRM updates
- **Automated Responses**: Service-specific welcome emails and resources

## Technical Specifications

### Webhook Configuration
- **Authentication**: Bearer token authentication using N8N_WEBHOOK_SECRET
- **Data Format**: JSON payload with comprehensive lead data
- **Error Handling**: Retry logic with exponential backoff
- **Monitoring**: Success/failure tracking and alerting

### Integration Requirements

#### CRM Integration (HubSpot/Salesforce)
- **Contact/Lead Creation**: Map form fields to CRM properties
- **Opportunity Management**: Create deals for high-intent leads
- **Custom Properties**: Lead score, form type, source attribution
- **Pipeline Management**: Move leads through appropriate sales stages

#### Email Automation
- **Provider**: Integration with SendGrid, Mailchimp, or HubSpot
- **Templates**: Professional email templates matching brand design
- **Personalization**: Dynamic content based on service interest and lead data
- **Tracking**: Open rates, click rates, and engagement metrics

#### Calendar Integration
- **Calendly API**: Automated booking for video consultations
- **Google Calendar**: Internal team calendar management
- **Scheduling Logic**: Route to appropriate team member based on expertise

#### Team Notifications
- **Slack Integration**: Channel-specific notifications based on lead priority
- **Email Alerts**: Immediate notifications for high-priority leads
- **Mobile Notifications**: Push notifications for urgent leads

### Data Flow Architecture

```
Website Form → API Endpoint → n8n Webhook → 
Lead Scoring → Routing Logic → CRM Integration → 
Team Notification → Follow-up Automation → 
Engagement Tracking → Progressive Qualification
```

### Monitoring and Analytics

#### Workflow Metrics
- **Execution Success Rate**: Track workflow completion rates
- **Processing Time**: Monitor webhook response times
- **Error Rates**: Alert on failed executions or integrations
- **Lead Conversion**: Track progression through sales funnel

#### Business Metrics
- **Lead Quality Scores**: Distribution and trends
- **Response Times**: Team performance against SLAs
- **Conversion Rates**: Form completion to sales qualified leads
- **Revenue Attribution**: Track deals back to original form source

## Documentation Requirements

### 1. Workflow Setup Instructions
- **Step-by-step setup guide** for each workflow
- **Node configuration details** with screenshots
- **Integration credentials** and environment variables
- **Testing procedures** for each workflow component

### 2. Integration Guides
- **CRM Setup**: HubSpot/Salesforce configuration and field mapping
- **Email Provider**: SendGrid/Mailchimp setup and template creation
- **Calendar Integration**: Calendly and Google Calendar configuration
- **Slack Setup**: Workspace integration and channel configuration

### 3. Troubleshooting Documentation
- **Common issues** and resolution steps
- **Error handling** procedures and escalation paths
- **Performance optimization** recommendations
- **Backup and recovery** procedures

### 4. Maintenance Procedures
- **Regular monitoring** checklists and schedules
- **Performance tuning** guidelines
- **Update procedures** for workflow modifications
- **Security reviews** and credential rotation

## Success Criteria

### Performance Targets
- **Webhook Response Time**: <2 seconds for all endpoints
- **Lead Processing**: Complete within 5 minutes of form submission
- **Team Notification**: Immediate for urgent leads, within SLA for others
- **CRM Sync**: Real-time data synchronization with <1% error rate

### Business Outcomes
- **Lead Response Time**: Meet or exceed defined SLAs by priority level
- **Lead Quality**: Maintain high lead scores and conversion rates
- **Team Efficiency**: Reduce manual lead processing by 90%
- **Revenue Impact**: Increase qualified lead volume by 50%

## Deliverables Expected

1. **Complete n8n Workflow Files**: Exportable JSON files for each workflow
2. **Setup Documentation**: Comprehensive installation and configuration guides
3. **Integration Guides**: Step-by-step integration setup for all external services
4. **Testing Procedures**: Validation scripts and test cases
5. **Monitoring Dashboard**: n8n workflow monitoring and alerting setup
6. **Training Materials**: Team training documentation for workflow management

Please design these workflows with enterprise-grade reliability, scalability, and maintainability in mind. The workflows should handle high volumes efficiently while maintaining data integrity and providing excellent user experience for both prospects and internal teams.
