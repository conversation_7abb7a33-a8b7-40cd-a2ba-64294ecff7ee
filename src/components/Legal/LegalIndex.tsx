"use client";

import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>er,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Link as HeroLink
} from '@heroui/react';
import {
  DocumentTextIcon,
  ShieldCheckIcon,
  ScaleIcon,
  GlobeAltIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';

const LegalIndex: React.FC = () => {
  const legalDocuments = [
    {
      id: 'privacy-policy',
      title: 'Privacy Policy',
      description: 'Comprehensive privacy practices covering GDPR, CCPA compliance, and data protection for software development, AI/ML, cybersecurity, and HR services.',
      icon: <ShieldCheckIcon className="h-8 w-8" />,
      color: 'success',
      lastUpdated: 'January 3, 2025',
      keyFeatures: [
        'GDPR & CCPA Compliance',
        'AI/ML Data Processing',
        'Cybersecurity Data Protection',
        'International Data Transfers',
        'Third-party Integrations'
      ],
      href: '/legal/privacy-policy'
    },
    {
      id: 'terms-conditions',
      title: 'Terms & Conditions',
      description: 'B2B-focused terms covering service agreements, intellectual property rights, liability limitations, and international dispute resolution.',
      icon: <ScaleIcon className="h-8 w-8" />,
      color: 'primary',
      lastUpdated: 'January 3, 2025',
      keyFeatures: [
        'B2B Service Agreements',
        'IP Rights & Licensing',
        'Service Level Agreements',
        'International Compliance',
        'Dispute Resolution'
      ],
      href: '/legal/terms-and-conditions'
    },
    {
      id: 'refund-policy',
      title: 'Refund Policy',
      description: 'Milestone-based refund structure for development projects, consulting services, cybersecurity assessments, and talent acquisition.',
      icon: <DocumentTextIcon className="h-8 w-8" />,
      color: 'warning',
      lastUpdated: 'January 3, 2025',
      keyFeatures: [
        'Milestone-based Refunds',
        'Service-specific Terms',
        'Partial Completion Calculations',
        'Emergency Service Terms',
        'Guarantee Structures'
      ],
      href: '/legal/refund-policy'
    }
  ];

  const complianceFrameworks = [
    { name: 'GDPR', description: 'General Data Protection Regulation (EU)', status: 'compliant' },
    { name: 'CCPA', description: 'California Consumer Privacy Act (USA)', status: 'compliant' },
    { name: 'Colombian Law', description: 'Law 1581 of 2012 (Data Protection)', status: 'compliant' },
    { name: 'SOC 2', description: 'Service Organization Control 2', status: 'compliant' },
    { name: 'ISO 27001', description: 'Information Security Management', status: 'compliant' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-content1">
      {/* Hero Section */}
      <section className="pt-20 pb-16 lg:pt-[120px] lg:pb-24">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="flex justify-center mb-6">
              <div className="p-4 rounded-full bg-primary/10 text-primary">
                <ScaleIcon className="h-12 w-12" />
              </div>
            </div>
            
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6">
              Legal Framework
            </h1>
            
            <p className="text-xl text-foreground-600 mb-8 max-w-4xl mx-auto">
              Comprehensive legal documentation designed for professional B2B software development 
              and consulting services with international compliance standards.
            </p>

            <div className="flex flex-wrap justify-center gap-4">
              <Chip
                color="success"
                variant="flat"
                startContent={<GlobeAltIcon className="h-4 w-4" />}
              >
                International Compliance
              </Chip>
              <Chip
                color="primary"
                variant="flat"
                startContent={<ShieldCheckIcon className="h-4 w-4" />}
              >
                B2B Focused
              </Chip>
              <Chip
                color="secondary"
                variant="flat"
                startContent={<CheckCircleIcon className="h-4 w-4" />}
              >
                Industry Standards
              </Chip>
            </div>
          </div>

          {/* Legal Notice */}
          <div className="max-w-4xl mx-auto mb-16">
            <Card className="shadow-lg border border-warning-200 dark:border-warning-800">
              <CardBody className="p-6">
                <div className="flex items-start gap-4">
                  <ExclamationTriangleIcon className="h-6 w-6 text-warning-600 flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="font-semibold text-warning-800 dark:text-warning-200 mb-2">
                      Important Legal Notice
                    </h3>
                    <p className="text-warning-700 dark:text-warning-300 mb-4">
                      These documents constitute legally binding agreements between Digital Wave Systems LLC 
                      and our clients. Please review carefully and consult with qualified legal counsel 
                      regarding your specific rights and obligations.
                    </p>
                    <div className="flex flex-wrap gap-3">
                      <Button
                        as={Link}
                        href="/contact"
                        color="warning"
                        variant="flat"
                        size="sm"
                      >
                        Contact Legal Team
                      </Button>
                      <Button
                        as={HeroLink}
                        href="mailto:<EMAIL>"
                        color="default"
                        variant="bordered"
                        size="sm"
                      >
                        Email Legal Department
                      </Button>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        </div>
      </section>

      {/* Legal Documents Grid */}
      <section className="pb-16 lg:pb-24">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Legal Documents
            </h2>
            <p className="text-foreground-600 max-w-2xl mx-auto">
              Comprehensive legal framework covering all aspects of our professional services
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {legalDocuments.map((doc) => (
              <Card key={doc.id} className="shadow-lg hover:shadow-xl transition-all duration-300">
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-4">
                    <div className={`p-3 rounded-full bg-${doc.color}/10 text-${doc.color}`}>
                      {doc.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-foreground">
                        {doc.title}
                      </h3>
                      <p className="text-sm text-foreground-600">
                        Updated: {doc.lastUpdated}
                      </p>
                    </div>
                  </div>
                </CardHeader>
                
                <Divider />
                
                <CardBody className="pt-4">
                  <p className="text-foreground-600 mb-6">
                    {doc.description}
                  </p>

                  <div className="mb-6">
                    <h4 className="font-semibold text-foreground mb-3">Key Features:</h4>
                    <ul className="space-y-2">
                      {doc.keyFeatures.map((feature, index) => (
                        <li key={index} className="flex items-center gap-2 text-sm text-foreground-600">
                          <CheckCircleIcon className="h-4 w-4 text-success flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Button
                    as={Link}
                    href={doc.href}
                    color={doc.color as any}
                    variant="solid"
                    fullWidth
                    className="font-medium"
                  >
                    Read {doc.title}
                  </Button>
                </CardBody>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Compliance Section */}
      <section className="pb-16 lg:pb-24 bg-content1/50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Compliance Standards
            </h2>
            <p className="text-foreground-600 max-w-2xl mx-auto">
              Our legal framework ensures compliance with international standards and regulations
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <Card className="shadow-lg">
              <CardBody className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {complianceFrameworks.map((framework, index) => (
                    <div key={index} className="flex items-center gap-4 p-4 rounded-lg bg-success/5 border border-success/20">
                      <CheckCircleIcon className="h-6 w-6 text-success flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold text-foreground">{framework.name}</h4>
                        <p className="text-sm text-foreground-600">{framework.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="pb-16 lg:pb-24">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Card className="shadow-lg">
              <CardBody className="p-8">
                <h2 className="text-2xl font-bold text-foreground mb-4">
                  Need Legal Assistance?
                </h2>
                <p className="text-foreground-600 mb-6">
                  Our legal team is available to answer questions about our policies, 
                  contract terms, and compliance requirements.
                </p>
                
                <div className="flex flex-wrap justify-center gap-4">
                  <Button
                    as={Link}
                    href="/contact"
                    color="primary"
                    variant="solid"
                    size="lg"
                  >
                    Contact Legal Team
                  </Button>
                  <Button
                    as={HeroLink}
                    href="mailto:<EMAIL>"
                    color="default"
                    variant="bordered"
                    size="lg"
                  >
                    Email Legal Department
                  </Button>
                </div>

                <Divider className="my-6" />

                <div className="text-sm text-foreground-600">
                  <p className="mb-2">
                    <strong>Legal Department:</strong> <EMAIL>
                  </p>
                  <p className="mb-2">
                    <strong>Business Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM (COT)
                  </p>
                  <p>
                    <strong>Emergency Legal Matters:</strong> Available 24/7 for critical issues
                  </p>
                </div>
              </CardBody>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
};

export default LegalIndex;
