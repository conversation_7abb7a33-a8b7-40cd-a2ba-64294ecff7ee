"use client";

import React from 'react';

interface MarkdownContentProps {
  content: string;
}

const MarkdownContent: React.FC<MarkdownContentProps> = ({ content }) => {
  // Convert markdown to JSX elements
  const renderContent = () => {
    const lines = content.split('\n');
    const elements: React.ReactNode[] = [];
    let currentList: string[] = [];
    let inCodeBlock = false;
    let codeBlockContent = '';

    const flushList = () => {
      if (currentList.length > 0) {
        elements.push(
          <ul key={`list-${elements.length}`} className="list-disc list-inside mb-6 space-y-2 ml-4">
            {currentList.map((item, index) => (
              <li key={index} className="text-foreground-700 leading-relaxed">
                {renderInlineElements(item)}
              </li>
            ))}
          </ul>
        );
        currentList = [];
      }
    };

    const renderInlineElements = (text: string) => {
      // Handle bold text
      text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
      
      // Handle italic text
      text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
      
      // Handle inline code
      text = text.replace(/`([^`]+)`/g, '<code className="bg-content2 px-2 py-1 rounded text-sm">$1</code>');
      
      // Handle links
      text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" className="text-primary hover:underline transition-colors">$1</a>');
      
      return <span dangerouslySetInnerHTML={{ __html: text }} />;
    };

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();

      // Handle code blocks
      if (trimmed.startsWith('```')) {
        if (inCodeBlock) {
          // End code block
          elements.push(
            <pre key={`code-${elements.length}`} className="bg-content2 p-4 rounded-lg mb-6 overflow-x-auto">
              <code className="text-sm">{codeBlockContent}</code>
            </pre>
          );
          inCodeBlock = false;
          codeBlockContent = '';

        } else {
          // Start code block
          flushList();
          inCodeBlock = true;

        }
        continue;
      }

      if (inCodeBlock) {
        codeBlockContent += line + '\n';
        continue;
      }

      // Handle headers
      if (trimmed.startsWith('# ')) {
        flushList();
        const text = trimmed.substring(2);
        const id = text.toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-');
        elements.push(
          <h1 key={`h1-${elements.length}`} id={id} className="text-3xl font-bold text-foreground mb-8 mt-12 first:mt-0">
            {text}
          </h1>
        );
      } else if (trimmed.startsWith('## ')) {
        flushList();
        const text = trimmed.substring(3);
        const id = text.toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-');
        elements.push(
          <h2 key={`h2-${elements.length}`} id={id} className="text-2xl font-bold text-foreground mb-6 mt-10">
            {text}
          </h2>
        );
      } else if (trimmed.startsWith('### ')) {
        flushList();
        const text = trimmed.substring(4);
        const id = text.toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-');
        elements.push(
          <h3 key={`h3-${elements.length}`} id={id} className="text-xl font-semibold text-foreground mb-4 mt-8">
            {text}
          </h3>
        );
      } else if (trimmed.startsWith('#### ')) {
        flushList();
        const text = trimmed.substring(5);
        const id = text.toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-');
        elements.push(
          <h4 key={`h4-${elements.length}`} id={id} className="text-lg font-medium text-foreground mb-3 mt-6">
            {text}
          </h4>
        );
      }
      // Handle list items
      else if (trimmed.startsWith('- ') || trimmed.startsWith('* ')) {
        const listItem = trimmed.substring(2);
        currentList.push(listItem);
      }
      // Handle horizontal rules
      else if (trimmed === '---') {
        flushList();
        elements.push(<hr key={`hr-${elements.length}`} className="my-8 border-divider" />);
      }
      // Handle paragraphs
      else if (trimmed && !trimmed.startsWith('**') && !trimmed.includes('**Effective Date:**') && !trimmed.includes('**Last Updated:**')) {
        flushList();
        elements.push(
          <p key={`p-${elements.length}`} className="mb-4 text-foreground-700 leading-relaxed">
            {renderInlineElements(trimmed)}
          </p>
        );
      }
      // Handle empty lines
      else if (!trimmed) {
        flushList();
      }
    }

    // Flush any remaining list items
    flushList();

    return elements;
  };

  return <div className="legal-content">{renderContent()}</div>;
};

export default MarkdownContent;
