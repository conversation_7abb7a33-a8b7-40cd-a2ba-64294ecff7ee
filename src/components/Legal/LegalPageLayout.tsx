"use client";

import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON> as <PERSON>L<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  BreadcrumbItem
} from '@heroui/react';
import {
  DocumentTextIcon,
  ShieldCheckIcon,
  ScaleIcon,
  ClockIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';

interface LegalPageLayoutProps {
  title: string;
  lastUpdated: string;
  effectiveDate: string;
  children: React.ReactNode;
  documentType: 'privacy' | 'terms' | 'refund';
  tableOfContents?: Array<{
    id: string;
    title: string;
    level: number;
  }>;
}

const LegalPageLayout: React.FC<LegalPageLayoutProps> = ({
  title,
  lastUpdated,
  effectiveDate,
  children,
  documentType,
  tableOfContents = []
}) => {
  const getDocumentIcon = () => {
    switch (documentType) {
      case 'privacy':
        return <ShieldCheckIcon className="h-6 w-6" />;
      case 'terms':
        return <ScaleIcon className="h-6 w-6" />;
      case 'refund':
        return <DocumentTextIcon className="h-6 w-6" />;
      default:
        return <DocumentTextIcon className="h-6 w-6" />;
    }
  };

  const getDocumentColor = () => {
    switch (documentType) {
      case 'privacy':
        return 'success';
      case 'terms':
        return 'primary';
      case 'refund':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-content1">
      {/* Header Section */}
      <section className="pt-20 pb-10 lg:pt-[120px] lg:pb-16">
        <div className="container mx-auto px-4">
          {/* Breadcrumbs */}
          <div className="mb-8">
            <Breadcrumbs>
              <BreadcrumbItem>
                <Link href="/">Home</Link>
              </BreadcrumbItem>
              <BreadcrumbItem>
                <Link href="/legal">Legal</Link>
              </BreadcrumbItem>
              <BreadcrumbItem>{title}</BreadcrumbItem>
            </Breadcrumbs>
          </div>

          {/* Page Header */}
          <div className="text-center mb-12">
            <div className="flex justify-center mb-6">
              <div className="p-4 rounded-full bg-primary/10 text-primary">
                {getDocumentIcon()}
              </div>
            </div>
            
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6">
              {title}
            </h1>
            
            <p className="text-lg text-foreground-600 mb-8 max-w-3xl mx-auto">
              Digital Wave Systems LLC - Professional B2B Software Development and Consulting Services
            </p>

            {/* Document Metadata */}
            <div className="flex flex-wrap justify-center gap-4 mb-8">
              <Chip
                color={getDocumentColor() as any}
                variant="flat"
                startContent={<ClockIcon className="h-4 w-4" />}
              >
                Effective: {effectiveDate}
              </Chip>
              <Chip
                color="default"
                variant="flat"
                startContent={<ClockIcon className="h-4 w-4" />}
              >
                Updated: {lastUpdated}
              </Chip>
              <Chip
                color="secondary"
                variant="flat"
                startContent={<GlobeAltIcon className="h-4 w-4" />}
              >
                International Compliance
              </Chip>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-wrap justify-center gap-4">
              <Button
                as={Link}
                href="/contact"
                color="primary"
                variant="solid"
                size="lg"
              >
                Contact Legal Team
              </Button>
              <Button
                as={HeroLink}
                href="#content"
                color="default"
                variant="bordered"
                size="lg"
              >
                Read Document
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="pb-16 lg:pb-24">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap -mx-4">
            {/* Table of Contents Sidebar */}
            {tableOfContents.length > 0 && (
              <div className="w-full lg:w-1/4 px-4 mb-8 lg:mb-0">
                <div className="sticky top-24">
                  <Card className="shadow-lg">
                    <CardHeader className="pb-3">
                      <h3 className="text-lg font-semibold text-foreground">
                        Table of Contents
                      </h3>
                    </CardHeader>
                    <Divider />
                    <CardBody className="pt-3">
                      <nav className="space-y-2">
                        {tableOfContents.map((item) => (
                          <HeroLink
                            key={item.id}
                            href={`#${item.id}`}
                            className={`block text-sm hover:text-primary transition-colors ${
                              item.level === 1 ? 'font-medium' : 'ml-4 text-foreground-600'
                            }`}
                          >
                            {item.title}
                          </HeroLink>
                        ))}
                      </nav>
                    </CardBody>
                  </Card>
                </div>
              </div>
            )}

            {/* Document Content */}
            <div className={`w-full ${tableOfContents.length > 0 ? 'lg:w-3/4' : ''} px-4`}>
              <Card className="shadow-lg" id="content">
                <CardBody className="p-8 lg:p-12">
                  {/* Legal Disclaimer */}
                  <div className="mb-8 p-6 bg-warning-50 dark:bg-warning-900/20 border border-warning-200 dark:border-warning-800 rounded-lg">
                    <div className="flex items-start gap-3">
                      <ShieldCheckIcon className="h-6 w-6 text-warning-600 flex-shrink-0 mt-0.5" />
                      <div>
                        <h4 className="font-semibold text-warning-800 dark:text-warning-200 mb-2">
                          Legal Document Notice
                        </h4>
                        <p className="text-sm text-warning-700 dark:text-warning-300">
                          This document constitutes a legally binding agreement. Please read carefully and consult 
                          with qualified legal counsel if you have questions about your rights and obligations. 
                          By using our services, you acknowledge acceptance of these terms.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Document Content */}
                  <div className="prose prose-lg max-w-none dark:prose-invert">
                    {children}
                  </div>

                  {/* Footer Actions */}
                  <Divider className="my-8" />
                  <div className="flex flex-wrap justify-between items-center gap-4">
                    <div className="text-sm text-foreground-600">
                      <p>Questions about this document?</p>
                      <HeroLink href="mailto:<EMAIL>" className="text-primary">
                        Contact our legal team
                      </HeroLink>
                    </div>
                    <div className="flex gap-3">
                      <Button
                        as={Link}
                        href="/legal"
                        color="default"
                        variant="bordered"
                      >
                        All Legal Documents
                      </Button>
                      <Button
                        as={Link}
                        href="/contact"
                        color="primary"
                        variant="solid"
                      >
                        Get Legal Support
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Related Documents */}
      <section className="pb-16 lg:pb-24 bg-content1/50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Related Legal Documents
            </h2>
            <p className="text-foreground-600 max-w-2xl mx-auto">
              Explore our comprehensive legal framework designed for professional B2B services
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Privacy Policy Card */}
            <Card className="shadow-lg hover:shadow-xl transition-shadow">
              <CardBody className="p-6 text-center">
                <div className="mb-4">
                  <div className="p-3 rounded-full bg-success/10 text-success inline-flex">
                    <ShieldCheckIcon className="h-6 w-6" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold mb-3">Privacy Policy</h3>
                <p className="text-foreground-600 mb-4">
                  GDPR & CCPA compliant privacy practices for international B2B clients
                </p>
                <Button
                  as={Link}
                  href="/legal/privacy-policy"
                  color="success"
                  variant="flat"
                  fullWidth
                >
                  Read Privacy Policy
                </Button>
              </CardBody>
            </Card>

            {/* Terms and Conditions Card */}
            <Card className="shadow-lg hover:shadow-xl transition-shadow">
              <CardBody className="p-6 text-center">
                <div className="mb-4">
                  <div className="p-3 rounded-full bg-primary/10 text-primary inline-flex">
                    <ScaleIcon className="h-6 w-6" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold mb-3">Terms & Conditions</h3>
                <p className="text-foreground-600 mb-4">
                  Comprehensive B2B service agreements and professional standards
                </p>
                <Button
                  as={Link}
                  href="/legal/terms-and-conditions"
                  color="primary"
                  variant="flat"
                  fullWidth
                >
                  Read Terms
                </Button>
              </CardBody>
            </Card>

            {/* Refund Policy Card */}
            <Card className="shadow-lg hover:shadow-xl transition-shadow">
              <CardBody className="p-6 text-center">
                <div className="mb-4">
                  <div className="p-3 rounded-full bg-warning/10 text-warning inline-flex">
                    <DocumentTextIcon className="h-6 w-6" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold mb-3">Refund Policy</h3>
                <p className="text-foreground-600 mb-4">
                  Milestone-based refund structure for development and consulting services
                </p>
                <Button
                  as={Link}
                  href="/legal/refund-policy"
                  color="warning"
                  variant="flat"
                  fullWidth
                >
                  Read Refund Policy
                </Button>
              </CardBody>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
};

export default LegalPageLayout;
