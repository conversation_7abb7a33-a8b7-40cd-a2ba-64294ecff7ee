import { Feature } from "@/types/feature";
import Link from "next/link";
import { Card, CardBody, Tooltip } from "@heroui/react";
import { ArrowRightIcon } from "@heroicons/react/24/outline";

const SingleFeature = ({ feature }: { feature: Feature }) => {
  const { icon, title, paragraph, btn, btnLink } = feature;
  return (
    <div className="w-full px-4 md:w-1/2 lg:w-1/3">
      <div className="wow fadeInUp group mb-12 h-full" data-wow-delay=".15s">
        <Card className="h-full min-h-[450px] bg-white dark:bg-dark-2 border border-gray-100 dark:border-dark-3 shadow-sm hover:shadow-xl hover:-translate-y-2 transition-all duration-300 rounded-xl">
          <CardBody className="p-8 sm:p-10 lg:p-12 flex flex-col justify-between h-full">
            <div>
              {/* Icon container with enhanced styling */}
              <div className="relative mb-8">
                <Tooltip content={`Learn more about ${title}`} placement="top">
                  <div className="flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br from-primary to-blue-600 shadow-lg cursor-pointer">
                    <div className="text-white text-3xl">
                      {icon}
                    </div>
                  </div>
                </Tooltip>
                {/* Background decoration */}
                <div className="absolute -top-3 -left-3 h-20 w-20 rounded-2xl bg-primary/10 -z-10 transition-transform duration-300 group-hover:rotate-6"></div>
              </div>

              {/* Content with enhanced spacing */}
              <div className="space-y-6 mb-8">
                <h3 className="text-xl sm:text-2xl font-bold text-dark dark:text-white leading-tight">
                  {title}
                </h3>
                <p className="text-body-color dark:text-dark-6 leading-relaxed text-base sm:text-lg line-clamp-4">
                  {paragraph}
                </p>
              </div>
            </div>

            {/* CTA Link with enhanced styling - positioned at bottom */}
            <div className="mt-auto pt-6 border-t border-gray-100 dark:border-dark-3">
              <Link
                href={btnLink}
                className="inline-flex items-center gap-3 text-primary hover:text-primary-dark font-semibold text-base transition-all duration-200 group/link"
              >
                <span>{btn}</span>
                <ArrowRightIcon className="h-5 w-5 transition-transform duration-200 group-hover/link:translate-x-1" />
              </Link>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default SingleFeature;
