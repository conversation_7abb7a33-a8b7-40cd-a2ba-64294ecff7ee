import { Feature } from "@/types/feature";
import {
  CodeBracketIcon,
  ShieldCheckIcon,
  CubeTransparentIcon,
  CpuChipIcon,
  CloudArrowUpIcon,
  Cog6ToothIcon
} from "@heroicons/react/24/outline";

const featuresData: Feature[] = [
  {
    id: 1,
    icon: <CodeBracketIcon className="h-8 w-8 text-white" />,
    title: "Custom Software Development",
    paragraph: "Enterprise-grade custom software solutions built with modern technologies. From web applications to mobile apps, we deliver scalable, maintainable code that grows with your business.",
    btn: "Learn More",
    btnLink: "/services/custom-development",
  },
  {
    id: 2,
    icon: <CpuChipIcon className="h-8 w-8 text-white" />,
    title: "AI & Machine Learning",
    paragraph: "Transform your business with intelligent automation, predictive analytics, and AI-powered solutions. We integrate cutting-edge AI technologies to drive innovation and efficiency.",
    btn: "Learn More",
    btnLink: "/services/ai-development",
  },
  {
    id: 3,
    icon: <ShieldCheckIcon className="h-8 w-8 text-white" />,
    title: "Cybersecurity Solutions",
    paragraph: "Comprehensive security consulting and implementation services. Protect your digital assets with advanced threat detection, security audits, and compliance frameworks.",
    btn: "Learn More",
    btnLink: "/services/cybersecurity",
  },
  {
    id: 4,
    icon: <CloudArrowUpIcon className="h-8 w-8 text-white" />,
    title: "Cloud & DevOps",
    paragraph: "Accelerate your digital transformation with cloud migration, infrastructure automation, and DevOps best practices. Optimize performance, scalability, and cost efficiency.",
    btn: "Learn More",
    btnLink: "/services/cloud-devops",
  },
  {
    id: 5,
    icon: <CubeTransparentIcon className="h-8 w-8 text-white" />,
    title: "API & Integration Services",
    paragraph: "Seamlessly connect your systems with robust API development and third-party integrations. Enable data flow and process automation across your technology stack.",
    btn: "Learn More",
    btnLink: "/services/api-integration",
  },
  {
    id: 6,
    icon: <Cog6ToothIcon className="h-8 w-8 text-white" />,
    title: "Technology Consulting",
    paragraph: "Strategic technology guidance from industry experts. We help you make informed decisions about technology investments, architecture design, and digital strategy.",
    btn: "Learn More",
    btnLink: "/services/consulting",
  },
];
export default featuresData;
