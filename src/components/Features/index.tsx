import SectionTitle from "../Common/SectionTitle";
import SingleFeature from "./SingleFeature";
import featuresData from "./featuresData";

const Features = () => {
  return (
    <section className="pb-8 pt-20 dark:bg-dark lg:pb-[70px] lg:pt-[120px]">
      <div className="container">
        <SectionTitle
          subtitle="Our Expertise"
          title="Comprehensive Software Consulting Services"
          paragraph="We deliver enterprise-grade solutions that drive digital transformation and accelerate business growth through cutting-edge technology and expert consulting."
        />

        <div className="mt-16 lg:mt-24">
          <div className="flex flex-wrap -mx-4">
            {featuresData.map((feature) => (
              <SingleFeature key={feature.id} feature={feature} />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;
