import { Testimonial } from "@/types/testimonial";
import Image from "next/image";
import { Card, CardBody } from "@heroui/react";
const starIcon = (
  <svg width="18" height="16" viewBox="0 0 18 16" className="fill-current">
    <path d="M9.09815 0.360596L11.1054 6.06493H17.601L12.3459 9.5904L14.3532 15.2947L9.09815 11.7693L3.84309 15.2947L5.85035 9.5904L0.595291 6.06493H7.0909L9.09815 0.360596Z" />
  </svg>
);

const SingleTestimonial = ({ testimonial }: { testimonial: Testimonial }) => {
  const { star, name, image, content, designation } = testimonial;

  let ratingIcons = [];
  for (let index = 0; index < star; index++) {
    ratingIcons.push(
      <span key={index} className="text-[#fbb040]">
        {starIcon}
      </span>,
    );
  }

  return (
    <div className="w-full px-4 md:w-1/2 lg:w-1/3">
      <Card
        className="group relative overflow-hidden min-h-[320px] h-full hover:-translate-y-2 hover:shadow-xl transition-all duration-150"
        data-wow-delay=".1s"
      >
        <CardBody className="p-6 sm:p-8 flex flex-col h-full">
          {/* Decorative elements */}
          <div className="absolute -right-12 -top-12 z-[-1] h-[180px] w-[180px] rounded-full bg-primary/5 transition-all duration-150 group-hover:bg-primary/10"></div>
          <div className="absolute -bottom-12 -left-12 z-[-1] h-[120px] w-[120px] rounded-full bg-primary/5 transition-all duration-150 group-hover:bg-primary/10"></div>

          {/* Rating stars */}
          <div className="mb-5 flex items-center gap-1">
            {ratingIcons}
          </div>

          {/* Quote marks */}
          <div className="absolute right-8 top-8 text-5xl font-serif text-primary/10 transition-all duration-150 group-hover:text-primary/20">"</div>

          {/* Content */}
          <p className="mb-8 text-base leading-relaxed text-body-color dark:text-[#8890AD] relative z-10 flex-grow line-clamp-5">
            "{content}"
          </p>

          {/* Author info */}
          <div className="flex items-center gap-4 mt-auto">
            <div>
              <h3 className="text-lg font-semibold text-dark transition-all duration-150 group-hover:text-primary dark:text-white">
                {name}
              </h3>
              <p className="text-sm text-body-color dark:text-[#8890AD]">{designation}</p>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default SingleTestimonial;
