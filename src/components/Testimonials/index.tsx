import { Testimonial } from "@/types/testimonial";
import SectionTitle from "../Common/SectionTitle";
import SingleTestimonial from "./SingleTestimonial";

const testimonialData: Testimonial[] = [
  {
    id: 1,
    name: "<PERSON>",
    designation: "CTO",
    content:
      "Digital Wave Systems transformed our AI infrastructure. Their expertise in machine learning and cloud solutions helped us achieve 40% faster processing times and significantly reduced operational costs.",
    image: "/images/testimonials/author-01.png",
    star: 5,
  },
  {
    id: 2,
    name: "<PERSON>",
    designation: "Head of Digital Transformation",
    content:
      "Digital Wave Systems revolutionized our customer service with AI. Their NLP solutions reduced response times by 60% while maintaining exceptional accuracy and customer satisfaction.",
    image: "/images/testimonials/author-02.png",
    star: 5,
  },
  {
    id: 3,
    name: "<PERSON>",
    designation: "Director of Innovation",
    content:
      "Digital Wave Systems transformed our data analytics platform. Their AI expertise helped us achieve unprecedented insights and optimize our business processes effectively.",
    image: "/images/testimonials/author-03.png",
    star: 5,
  },
];

const Testimonials = () => {
  return (
    <section className="bg-gray-1 py-20 dark:bg-dark-2 md:py-[120px]">
      <div className="container px-4">
        <SectionTitle
          subtitle="Client Success Stories"
          title="What Our Partners Say"
          paragraph="Discover how leading companies are leveraging our AI solutions to drive innovation and achieve measurable results in their digital transformation journeys."
          width="640px"
          center
        />

        <div className="mt-[60px] flex flex-wrap lg:mt-20 gap-y-8">
          {testimonialData.map((testimonial, i) => (
            <SingleTestimonial key={i} testimonial={testimonial} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
