"use client";

import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Accordion,
  AccordionItem,
  Divider,
  Progress
} from '@heroui/react';
import {
  CheckCircleIcon,
  ClockIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ChartBarIcon,
  LightBulbIcon,
  CodeBracketIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import PageHero from '../Common/PageHero';
import OptimizedImage from '../Common/OptimizedImage';
import { StartProjectCTA, GetQuoteServicesCTA } from '../CTAForms/ProjectCTAButtons';

interface ServiceDetailPageProps {
  service: {
    id: string;
    title: string;
    subtitle: string;
    heroImage: string;
    description: string;
    overview: {
      title: string;
      content: string;
      benefits: string[];
    };
    services: Array<{
      title: string;
      description: string;
      features: string[];
    }>;
    process: {
      title: string;
      steps: Array<{
        phase: string;
        duration: string;
        description: string;
        deliverables: string[];
      }>;
    };
    technologies: {
      [key: string]: string[];
    };
    caseStudies: Array<{
      title: string;
      client: string;
      challenge: string;
      solution: string;
      results: string[];
      technologies: string[];
    }>;
    pricing: {
      title: string;
      models: Array<{
        type: string;
        description: string;
        startingPrice: string;
        features: string[];
      }>;
    };
    faq: Array<{
      question: string;
      answer: string;
    }>;
  };
}

const ServiceDetailPage: React.FC<ServiceDetailPageProps> = ({ service }) => {
  return (
    <>
      {/* Hero Section */}
      <PageHero
        badge="Professional Services"
        title={service.title}
        description={service.subtitle}
        backgroundImage={service.heroImage}
        primaryCTA={
          <StartProjectCTA
            variant="primary"
            size="lg"
            className="bg-white text-primary hover:bg-blue-50 font-semibold"
            source={`${service.id}-hero`}
            analyticsEvent="hero_start_project"
          />
        }
        secondaryCTA={
          <GetQuoteServicesCTA
            variant="outline"
            size="lg"
            className="text-white border-white/30 hover:bg-white/10 font-medium"
            source={`${service.id}-hero`}
            analyticsEvent="hero_get_quote"
          />
        }
      />

      {/* Main Content */}
      <section className="py-16 lg:py-20 bg-gradient-to-br from-white to-blue-50 dark:from-dark dark:to-dark-800">
        <div className="container mx-auto px-4">
          
          {/* Service Overview */}
          <div className="mb-16">
            <div className="max-w-4xl mx-auto text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-dark dark:text-white mb-6">
                {service.overview.title}
              </h2>
              <p className="text-lg text-body-color dark:text-dark-6 leading-relaxed">
                {service.description}
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <p className="text-body-color dark:text-dark-6 text-lg leading-relaxed mb-8">
                  {service.overview.content}
                </p>
                
                <div className="grid sm:grid-cols-2 gap-4">
                  {service.overview.benefits.map((benefit, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <CheckCircleIcon className="h-6 w-6 text-primary flex-shrink-0 mt-1" />
                      <span className="text-body-color dark:text-dark-6">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="relative">
                <OptimizedImage
                  src={service.heroImage}
                  alt={service.title}
                  width={600}
                  height={400}
                  className="rounded-2xl shadow-2xl"
                />
              </div>
            </div>
          </div>

          {/* Services Grid */}
          <div className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-dark dark:text-white mb-4">
                Our Services
              </h2>
              <p className="text-lg text-body-color dark:text-dark-6 max-w-2xl mx-auto">
                Comprehensive solutions tailored to your business needs
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {service.services.map((serviceItem, index) => (
                <Card key={index} className="h-full hover:shadow-xl transition-all duration-300">
                  <CardBody className="p-8">
                    <h3 className="text-xl font-bold text-dark dark:text-white mb-4">
                      {serviceItem.title}
                    </h3>
                    <p className="text-body-color dark:text-dark-6 mb-6">
                      {serviceItem.description}
                    </p>
                    <ul className="space-y-3">
                      {serviceItem.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start gap-3">
                          <CheckCircleIcon className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                          <span className="text-body-color dark:text-dark-6">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardBody>
                </Card>
              ))}
            </div>
          </div>

          {/* Development Process */}
          <div className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-dark dark:text-white mb-4">
                {service.process.title}
              </h2>
              <p className="text-lg text-body-color dark:text-dark-6 max-w-2xl mx-auto">
                Our proven methodology ensures successful project delivery
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-8">
              {service.process.steps.map((step, index) => (
                <Card key={index} className="hover:shadow-xl transition-all duration-300">
                  <CardBody className="p-8">
                    <div className="flex items-start gap-4 mb-4">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white text-lg font-bold flex-shrink-0">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-dark dark:text-white mb-2">
                          {step.phase}
                        </h3>
                        <div className="flex items-center gap-2 text-sm text-body-color dark:text-dark-6 mb-4">
                          <ClockIcon className="h-4 w-4" />
                          <span>{step.duration}</span>
                        </div>
                      </div>
                    </div>
                    
                    <p className="text-body-color dark:text-dark-6 mb-6">
                      {step.description}
                    </p>
                    
                    <div>
                      <h4 className="font-semibold text-dark dark:text-white mb-3">Deliverables:</h4>
                      <ul className="space-y-2">
                        {step.deliverables.map((deliverable, idx) => (
                          <li key={idx} className="flex items-center gap-2">
                            <CheckCircleIcon className="h-4 w-4 text-success" />
                            <span className="text-body-color dark:text-dark-6 text-sm">{deliverable}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </div>

          {/* Technologies */}
          <div className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-bold text-dark dark:text-white mb-4">
                Technologies We Use
              </h2>
              <p className="text-lg text-body-color dark:text-dark-6 max-w-2xl mx-auto">
                Cutting-edge technologies for robust, scalable solutions
              </p>
            </div>

            <Card>
              <CardBody className="p-8">
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {Object.entries(service.technologies).map(([category, techs]) => (
                    <div key={category}>
                      <h3 className="text-lg font-semibold text-dark dark:text-white mb-4 capitalize">
                        {category}
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {techs.map((tech, idx) => (
                          <Chip 
                            key={idx} 
                            size="sm" 
                            variant="flat" 
                            color="primary"
                            className="mb-2"
                          >
                            {tech}
                          </Chip>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          </div>

          {/* CTA Section */}
          <div className="text-center mb-16">
            <Card className="bg-gradient-to-r from-primary to-primary/80 text-white">
              <CardBody className="p-12">
                <h2 className="text-3xl lg:text-4xl font-bold mb-6">
                  Ready to Transform Your Business?
                </h2>
                <p className="text-xl mb-8 opacity-90">
                  Let's discuss your project and create a solution that drives real results
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <StartProjectCTA
                    variant="primary"
                    size="lg"
                    className="bg-white text-primary hover:bg-gray-100"
                    source={`${service.id}-detail-page`}
                  />
                  <GetQuoteServicesCTA
                    variant="outline"
                    size="lg"
                    className="border-white text-white hover:bg-white hover:text-primary"
                    source={`${service.id}-detail-page`}
                  />
                </div>
              </CardBody>
            </Card>
          </div>
        </div>
      </section>
    </>
  );
};

export default ServiceDetailPage;
