"use client";
import Link from "next/link";
import React, { useEffect, useState, useRef } from "react";
import { SiReact, SiNodedotjs, SiPython, SiTypescript, <PERSON>Docker, SiKuberne<PERSON> } from "react-icons/si";
// Import AnimJS with the specific exports
import { animate, createScope } from 'animejs';
import { Button, Spacer } from "@heroui/react";
import { BriefcaseIcon, CodeBracketIcon, CommandLineIcon, CpuChipIcon } from "@heroicons/react/24/outline";
import { StartProjectHeroCTA, GetQuoteHeroCTA } from "../CTAForms/ProjectCTAButtons";

// Enhanced Software Development Animation Component
const TechAnimation = () => {
  const floatingElementsRef = useRef<HTMLDivElement[]>([]);

  // Tech stack data for floating elements
  const techStack = [
    { icon: SiReact, name: 'React', color: '#61DAFB' },
    { icon: SiNodedotjs, name: 'Node.js', color: '#339933' },
    { icon: SiPython, name: 'Python', color: '#3776AB' },
    { icon: SiTypescript, name: 'TypeScript', color: '#3178C6' },
    { icon: SiDocker, name: 'Docker', color: '#2496ED' },
    { icon: SiKubernetes, name: 'Kubernetes', color: '#326CE5' }
  ];

  // Code snippets for floating animation
  const codeSnippets = [
    'const app = express();',
    'npm install react',
    'docker build -t app .',
    'kubectl apply -f deployment.yaml',
    'async function getData() {',
    'export default Component;',
    'pip install tensorflow',
    'git commit -m "feat: new feature"'
  ];

  // Terminal commands for typewriter effect
  const terminalCommands = [
    'npm run build',
    'git push origin main',
    'docker-compose up',
    'npm test --coverage',
    'yarn deploy --prod'
  ];

  const [currentCommand, setCurrentCommand] = useState(0);

  // Cycle through terminal commands
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentCommand((prev) => (prev + 1) % terminalCommands.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [terminalCommands.length]);

  // Set up floating elements animations
  useEffect(() => {
    // Animate floating tech icons
    floatingElementsRef.current.forEach((element, index) => {
      if (element) {
        animate(element, {
          translateY: [0, -20, 0],
          translateX: [0, Math.sin(index) * 10, 0],
          rotate: [0, 5, -5, 0],
          scale: [1, 1.1, 1],
          duration: 3000 + (index * 500),
          delay: index * 200,
          loop: true,
          easing: 'easeInOutSine'
        });
      }
    });
  }, []);

  return (
    <div className="animation-wrapper relative w-full">
      {/* Floating Tech Icons */}
      {techStack.map((tech, index) => {
        const IconComponent = tech.icon;
        return (
          <div
            key={tech.name}
            ref={(el) => {
              if (el) floatingElementsRef.current[index] = el;
            }}
            className="absolute z-10 opacity-80 hover:opacity-100 transition-opacity duration-300"
            style={{
              top: `${20 + (index * 12)}%`,
              left: `${15 + (index % 2) * 70}%`,
              color: tech.color,
            }}
          >
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/20">
              <IconComponent className="w-6 h-6" />
              <span className="text-white text-sm font-medium">{tech.name}</span>
            </div>
          </div>
        );
      })}

      {/* Floating Code Snippets */}
      {codeSnippets.slice(0, 4).map((snippet, index) => (
        <div
          key={index}
          ref={(el) => {
            if (el) floatingElementsRef.current[techStack.length + index] = el;
          }}
          className="absolute z-10 opacity-70 hover:opacity-100 transition-opacity duration-300"
          style={{
            top: `${30 + (index * 15)}%`,
            right: `${10 + (index % 2) * 15}%`,
          }}
        >
          <div className="bg-gray-900/80 backdrop-blur-sm rounded-md px-3 py-2 border border-green-400/30">
            <code className="text-green-400 text-xs font-mono">{snippet}</code>
          </div>
        </div>
      ))}

      {/* Terminal Window */}
      <div
        ref={(el) => {
          if (el) floatingElementsRef.current[techStack.length + 4] = el;
        }}
        className="absolute top-10 left-10 z-10 opacity-90"
      >
        <div className="bg-gray-900/90 backdrop-blur-sm rounded-lg border border-gray-600/50 shadow-xl">
          <div className="flex items-center gap-2 px-4 py-2 bg-gray-800/80 rounded-t-lg border-b border-gray-600/50">
            <div className="flex gap-1">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
            </div>
            <span className="text-gray-300 text-xs font-mono ml-2">terminal</span>
          </div>
          <div className="px-4 py-3">
            <div className="flex items-center gap-2">
              <span className="text-green-400 text-sm font-mono">$</span>
              <span className="text-white text-sm font-mono typewriter" key={currentCommand}>
                {terminalCommands[currentCommand]}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Tech Background */}
      <div className="tech-background absolute inset-0 overflow-hidden">
        {/* Animated Grid Pattern */}
        <div className="grid-pattern absolute inset-0 opacity-20">
          <svg width="100%" height="100%" className="grid-svg">
            <defs>
              <pattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse">
                <path d="M 60 0 L 0 0 0 60" fill="none" stroke="rgba(255,255,255,0.1)" strokeWidth="1"/>
              </pattern>
              <pattern id="dots" width="40" height="40" patternUnits="userSpaceOnUse">
                <circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.2)"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
            <rect width="100%" height="100%" fill="url(#dots)" />
          </svg>
        </div>

        {/* Floating Geometric Shapes */}
        <div className="geometric-shapes absolute inset-0">
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="floating-shape absolute opacity-10"
              style={{
                top: `${Math.random() * 80 + 10}%`,
                left: `${Math.random() * 80 + 10}%`,
                animationDelay: `${i * 0.5}s`,
              }}
            >
              <div className={`shape-${i % 4} bg-white/20 backdrop-blur-sm`} />
            </div>
          ))}
        </div>



        {/* Particle System */}
        <div className="particles absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="particle absolute w-1 h-1 bg-white/30 rounded-full"
              style={{
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                animationDelay: `${i * 0.3}s`,
                animationDuration: `${3 + Math.random() * 4}s`,
              }}
            />
          ))}
        </div>
      </div>

      
      <style>{`
        .animation-wrapper {
          width: 100%;
          height: 450px;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          overflow: hidden;
        }



        .typewriter {
          overflow: hidden;
          border-right: 2px solid #22c55e;
          white-space: nowrap;
          animation: typing 3s steps(15, end) infinite, blink-caret 0.75s step-end infinite;
        }

        @keyframes typing {
          0% { width: 0; }
          50% { width: 100%; }
          100% { width: 100%; }
        }

        @keyframes blink-caret {
          from, to { border-color: transparent; }
          50% { border-color: #22c55e; }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
          .tech-background {
            background: radial-gradient(circle at 50% 30%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
                        radial-gradient(circle at 50% 70%, rgba(16, 185, 129, 0.08) 0%, transparent 50%);
          }

          .floating-shape {
            display: none;
          }

          .circuit-pattern {
            opacity: 0.1;
          }

          .particles .particle:nth-child(n+11) {
            display: none;
          }
        }

        @media (min-width: 500px) {
          .sphere path {
            stroke-width: .4px;
          }
        }

        @media (min-width: 1024px) {
          .tech-background {
            background: radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.12) 0%, transparent 50%),
                        radial-gradient(circle at 75% 25%, rgba(16, 185, 129, 0.12) 0%, transparent 50%),
                        radial-gradient(circle at 25% 75%, rgba(139, 92, 246, 0.12) 0%, transparent 50%),
                        radial-gradient(circle at 75% 75%, rgba(245, 101, 101, 0.12) 0%, transparent 50%);
          }
        }

        /* Enhanced Tech Background Animations */
        .tech-background {
          background: radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                      radial-gradient(circle at 70% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
                      radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
        }

        .grid-pattern {
          animation: grid-move 20s linear infinite;
        }

        @keyframes grid-move {
          0% { transform: translate(0, 0); }
          100% { transform: translate(60px, 60px); }
        }

        .floating-shape {
          animation: float-complex 8s ease-in-out infinite;
        }

        .shape-0 {
          width: 20px;
          height: 20px;
          border-radius: 50%;
        }

        .shape-1 {
          width: 16px;
          height: 16px;
          transform: rotate(45deg);
        }

        .shape-2 {
          width: 24px;
          height: 4px;
          border-radius: 2px;
        }

        .shape-3 {
          width: 18px;
          height: 18px;
          clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
        }

        @keyframes float-complex {
          0%, 100% {
            transform: translateY(0px) translateX(0px) rotate(0deg);
            opacity: 0.1;
          }
          25% {
            transform: translateY(-20px) translateX(10px) rotate(90deg);
            opacity: 0.3;
          }
          50% {
            transform: translateY(-10px) translateX(-5px) rotate(180deg);
            opacity: 0.2;
          }
          75% {
            transform: translateY(-30px) translateX(-10px) rotate(270deg);
            opacity: 0.4;
          }
        }



        .particle {
          animation: particle-float 6s ease-in-out infinite;
        }

        @keyframes particle-float {
          0%, 100% {
            transform: translateY(0px) scale(1);
            opacity: 0.3;
          }
          50% {
            transform: translateY(-40px) scale(1.2);
            opacity: 0.8;
          }
        }



        /* Floating elements animations */
        @keyframes float-up-down {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }

        @keyframes pulse-glow {
          0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.3); }
          50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.6); }
        }
      `}</style>
    </div>
  );
};

const Hero = () => {
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <>
      <section
        id="home"
        className="relative overflow-hidden bg-gradient-to-b from-primary to-primary/90 pt-[120px] md:pt-[130px] lg:pt-[160px]"
      >
        <div className="container">
          <div className="flex flex-wrap items-center -mx-4">
            <div className="w-full px-4 lg:w-1/2">
              <div
                className={`hero-content mx-auto max-w-[780px] lg:mx-0 lg:text-left text-center transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}
              >
                <h1 className="mb-8 text-4xl font-extrabold leading-[1.1] tracking-tight text-white sm:text-5xl sm:leading-[1.1] lg:text-7xl lg:leading-[1.1]">
                  <span className="block">
                    <span className="text-transparent bg-clip-text bg-gradient-to-r from-white via-blue-100 to-white">Full-Service</span>
                  </span>
                  <span className="block">
                    <span className="text-blue-300 font-black">Software</span>{' '}
                    <span className="relative inline-block">
                      <span className="text-blue-300 font-black">Development</span>
                    </span>
                  </span>
                </h1>

                <div className="mb-10 space-y-4">
                  <p className="text-xl font-medium leading-relaxed text-white/95 sm:text-2xl lg:text-xl lg:leading-relaxed">
                    Digital Wave Systems is a <span className="font-semibold text-blue-200">leading software development company</span> that delivers enterprise-grade solutions for businesses worldwide.
                  </p>
                  <p className="text-lg leading-relaxed text-white/80 sm:text-xl lg:text-lg lg:leading-relaxed">
                    We specialize in <span className="font-medium text-blue-300">custom software development</span>, <span className="font-medium text-blue-300">full-stack applications</span>, <span className="font-medium text-blue-300">AI/ML integration</span>, and <span className="font-medium text-blue-300">cloud/DevOps solutions</span>.
                  </p>
                </div>

                <Spacer y={4} />

                <div className="mb-12 flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-4">
                  <div className={`transform transition-all duration-1000 delay-300 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
                    <StartProjectHeroCTA
                      ctaText="Start Your Software Project"
                      source="hero-section"
                      analyticsEvent="hero_start_project"
                      icon={<span className="ml-1">→</span>}
                    />
                  </div>
                  <div className={`transform transition-all duration-1000 delay-500 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
                    <GetQuoteHeroCTA
                      size="lg"
                      className="text-white border-white/30 hover:bg-white/10 font-medium px-8 py-3"
                      ctaText="Get Development Quote"
                      source="hero-section"
                      analyticsEvent="hero_get_quote"
                      icon={<BriefcaseIcon className="h-5 w-5" />}
                    />
                  </div>
                </div>
                
                {/* Trust indicators */}
                <div className={`flex flex-wrap justify-center lg:justify-start gap-6 items-center transition-all duration-1000 delay-700 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
                  <div className="flex items-center gap-2 bg-white/5 backdrop-blur-sm px-4 py-2 rounded-full">
                    <div className="text-green-400">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                    </div>
                    <span className="text-white/90 text-sm">500+ Projects Delivered</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white/5 backdrop-blur-sm px-4 py-2 rounded-full">
                    <div className="text-blue-300">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                      </svg>
                    </div>
                    <span className="text-white/90 text-sm">Fortune 500 Clients</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white/5 backdrop-blur-sm px-4 py-2 rounded-full">
                    <div className="text-yellow-400">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                      </svg>
                    </div>
                    <span className="text-white/90 text-sm">98% Client Satisfaction</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="w-full px-4 lg:w-1/2">
              <div className={`relative z-10 mx-auto mt-16 lg:mt-0 max-w-[650px] bg-transparent transform transition-all duration-1000 delay-200 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
                {/* Enhanced Tech Animation */}
                <TechAnimation />
              </div>
            </div>
          </div>
          
          {/* Partner/client logos section */}
          <div className={`mt-16 border-t border-white/10 pt-8 transform transition-all duration-1000 delay-800 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <p className="text-center text-sm font-medium text-white/80 mb-8">
              Trusted by leading enterprises and innovative startups worldwide
            </p>
          </div>
        </div>
        
        {/* Enhanced background gradient elements */}
        <div className="absolute top-0 right-0 -z-10 opacity-30">
          <svg width="450" height="556" viewBox="0 0 450 556" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="277" cy="63" r="225" fill="url(#paint0_linear_25:217)">
              <animate attributeName="r" values="225;235;225" dur="8s" repeatCount="indefinite" />
            </circle>
            <circle cx="17.9997" cy="182" r="18" fill="url(#paint1_radial_25:217)">
              <animate attributeName="cy" values="182;195;182" dur="5s" repeatCount="indefinite" />
            </circle>
            <circle cx="76.9997" cy="288" r="34" fill="url(#paint2_radial_25:217)">
              <animate attributeName="r" values="34;38;34" dur="6s" repeatCount="indefinite" />
            </circle>
            <circle cx="325.486" cy="302.87" r="180" transform="rotate(-37.6852 325.486 302.87)" fill="url(#paint3_linear_25:217)">
              <animate attributeName="r" values="180;190;180" dur="7s" repeatCount="indefinite" />
            </circle>
            <circle opacity="0.8" cx="184.521" cy="315.521" r="132.862" transform="rotate(114.874 184.521 315.521)" fill="url(#paint4_linear_25:217)">
              <animate attributeName="cx" values="184.521;195;184.521" dur="9s" repeatCount="indefinite" />
            </circle>
            <defs>
              <linearGradient id="paint0_linear_25:217" x1="277" y1="-162" x2="277" y2="338" gradientUnits="userSpaceOnUse">
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
              </linearGradient>
              <radialGradient id="paint1_radial_25:217" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(17.9997 182) rotate(90) scale(18)">
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
              </radialGradient>
              <radialGradient id="paint2_radial_25:217" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(76.9997 288) rotate(90) scale(34)">
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
              </radialGradient>
              <linearGradient id="paint3_linear_25:217" x1="325.486" y1="122.87" x2="325.486" y2="482.87" gradientUnits="userSpaceOnUse">
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
              </linearGradient>
              <linearGradient id="paint4_linear_25:217" x1="184.521" y1="182.659" x2="184.521" y2="448.383" gradientUnits="userSpaceOnUse">
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
              </linearGradient>
            </defs>
          </svg>
        </div>
        
        {/* Enhanced animated particles */}
        <div className="absolute inset-0 overflow-hidden -z-10">
          {Array(15).fill(0).map((_, index) => (
            <div key={index} 
                 className="absolute rounded-full bg-blue-400/20 backdrop-blur-sm"
                 style={{
                   width: `${Math.random() * 10 + 4}px`,
                   height: `${Math.random() * 10 + 4}px`,
                   top: `${Math.random() * 100}%`,
                   left: `${Math.random() * 100}%`,
                   animation: `float ${Math.random() * 10 + 15}s linear infinite`,
                   animationDelay: `${Math.random() * 5}s`
                 }}
            ></div>
          ))}
        </div>
        
        {/* Add enhanced animations */}
        <style jsx>{` /* Added jsx prop back */
          @keyframes float {
            0% { transform: tranlateY(0) translateX(0); opacity: 0.7; }
            25% { transform: translateY(-30px) translateX(15px); opacity: 1; }
            50% { transform: translateY(-10px) translateX(30px); opacity: 0.7; }
            75% { transform: translateY(-25px) translateX(15px); opacity: 1; }
            100% { transform: translateY(0) translateX(0); opacity: 0.7; }
          }
          
          .typewriter {
            overflow: hidden;
            border-right: 2px solid rgba(255,255,255,0.75);
            white-space: nowrap;
            display: inline-block;
            animation: typing 3s steps(50, end) infinite, blink-caret 0.75s step-end infinite;
          }
          
          .typewriter-heading {
            overflow: hidden;
            border-right: 3px solid rgba(255,255,255,0.85);
            white-space: nowrap;
            display: inline-block;
            animation: typing 4s steps(40, end) infinite, blink-caret 0.75s step-end infinite;
            min-width: 220px;
          }
          
          @keyframes typing {
            0% { width: 0; }
            60% { width: 100%; }
            100% { width: 100%; }
          }
          
          @keyframes blink-caret {
            from, to { border-color: transparent }
            50% { border-color: rgba(255,255,255,0.75); }
          }
        `}</style>
      </section>
    </>
  );
};

export default Hero;