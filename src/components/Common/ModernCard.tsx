"use client";

import { Card, CardBody, CardHeader } from "@heroui/react";
import { ReactNode } from "react";

interface ModernCardProps {
  children: ReactNode;
  header?: ReactNode;
  className?: string;
  hoverable?: boolean;
  gradient?: boolean;
}

const ModernCard = ({
  children,
  header,
  className = "",
  hoverable = true,
  gradient = false,
}: ModernCardProps) => {
  return (
    <Card
      className={`
        ${hoverable ? "hover:-translate-y-2 hover:shadow-xl hover:shadow-primary/5" : ""}
        ${gradient ? "bg-gradient-to-br from-white via-white to-blue-50/30" : "bg-white"}
        border border-gray-100 dark:border-dark-4 dark:bg-dark-2
        transition-all duration-300 rounded-2xl shadow-sm
        ${className}
      `}
      shadow="none"
    >
      {header && (
        <CardHeader className="pb-4">
          {header}
        </CardHeader>
      )}
      <CardBody className="p-8">
        {children}
      </CardBody>
    </Card>
  );
};

export default ModernCard;
