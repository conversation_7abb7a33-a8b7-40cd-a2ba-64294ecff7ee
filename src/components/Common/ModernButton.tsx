"use client";

import { <PERSON><PERSON> } from "@heroui/react";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import { ReactNode } from "react";

interface ModernButtonProps {
  children: ReactNode;
  href?: string;
  variant?: "primary" | "secondary" | "ghost";
  size?: "sm" | "md" | "lg";
  showArrow?: boolean;
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
}

const ModernButton = ({
  children,
  href,
  variant = "primary",
  size = "md",
  showArrow = false,
  className = "",
  onClick,
  disabled = false,
}: ModernButtonProps) => {
  const getVariantStyles = () => {
    switch (variant) {
      case "primary":
        return "bg-primary text-white hover:bg-primary-dark shadow-lg hover:shadow-xl";
      case "secondary":
        return "bg-white/10 text-white border border-white/20 hover:bg-white/20 backdrop-blur-md";
      case "ghost":
        return "bg-transparent text-primary hover:bg-primary/10 border border-primary/20 hover:border-primary/30";
      default:
        return "bg-primary text-white hover:bg-primary-dark";
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case "sm":
        return "px-4 py-2 text-sm";
      case "md":
        return "px-6 py-3 text-base";
      case "lg":
        return "px-8 py-4 text-lg";
      default:
        return "px-6 py-3 text-base";
    }
  };

  const buttonContent = (
    <Button
      className={`
        ${getVariantStyles()}
        ${getSizeStyles()}
        font-semibold rounded-xl transition-all duration-200 
        transform hover:-translate-y-0.5 focus:outline-none 
        focus:ring-2 focus:ring-primary/50 focus:ring-offset-2
        ${className}
      `}
      onClick={onClick}
      disabled={disabled}
      endContent={showArrow ? <ArrowRightIcon className="h-4 w-4 ml-1" /> : undefined}
    >
      {children}
    </Button>
  );

  if (href) {
    return (
      <Link href={href} className="inline-block">
        {buttonContent}
      </Link>
    );
  }

  return buttonContent;
};

export default ModernButton;
