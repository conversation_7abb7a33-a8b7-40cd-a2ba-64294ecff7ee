import { Chip } from "@heroui/react";

const SectionTitle = ({
  subtitle,
  title,
  paragraph,
  width = "635px",
  center,
}: {
  subtitle?: string;
  title: string;
  paragraph: string;
  width?: string;
  center?: boolean;
}) => {
  return (
    <div className="-mx-4 flex flex-wrap">
      <div
        className={`wow fadeInUp w-full px-4 ${
          center ? "mx-auto text-center" : ""
        }`}
        data-wow-delay=".1s"
        style={{ maxWidth: width }}
      >
        {subtitle && (
          <div className="mb-6 inline-flex items-center">
            <Chip
              color="primary"
              variant="flat"
              className="font-medium"
            >
              {subtitle}
            </Chip>
          </div>
        )}
        <h2 className="mb-6 text-3xl font-bold leading-tight text-dark dark:text-white sm:text-4xl md:text-[42px] md:leading-[1.15] tracking-tight">
          {title.split(' ').map((word, i) => {
            // Highlight key words (every 3rd word or words containing specific terms)
            const isHighlight = i % 3 === 1 ||
              word.toLowerCase().includes('software') ||
              word.toLowerCase().includes('consulting') ||
              word.toLowerCase().includes('ai') ||
              word.toLowerCase().includes('digital');

            return (
              <span key={`${word}-${i}`} className={isHighlight ? "text-primary" : ""}>
                {word}{' '}
              </span>
            );
          })}
        </h2>
        <p className="text-base leading-relaxed text-body-color dark:text-[#8890AD] sm:text-lg sm:leading-relaxed max-w-2xl">
          {paragraph}
        </p>
      </div>
    </div>
  );
};

export default SectionTitle;
