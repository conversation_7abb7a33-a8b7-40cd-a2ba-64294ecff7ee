import { <PERSON>, CardBody, Input, Textare<PERSON>, <PERSON><PERSON>, <PERSON> } from "@heroui/react";

const Contact = () => {
  return (
    <section id="contact" className="relative py-20 md:py-[120px]">
      <div className="absolute left-0 top-0 -z-[1] h-full w-full dark:bg-dark"></div>
      <div className="absolute left-0 top-0 -z-[1] h-1/2 w-full bg-gradient-to-b from-gray-50 to-[#E9F9FF] dark:from-dark-2 dark:to-dark-700 lg:h-[45%] xl:h-1/2"></div>
      <div className="container px-4">
        <div className="-mx-4 flex flex-wrap items-center">
          <div className="w-full px-4 lg:w-7/12 xl:w-8/12">
            <div className="ud-contact-content-wrapper">
              <div className="ud-contact-title mb-12 lg:mb-[150px]">
                <Chip
                  color="primary"
                  variant="flat"
                  className="mb-6 uppercase tracking-wider font-semibold"
                >
                  CONTACT US
                </Chip>
                <h2 className="max-w-[460px] text-4xl font-bold leading-tight text-dark dark:text-white">
                  Let's discuss your <span className="text-primary">AI solution</span> needs
                </h2>
              </div>
              <div className="mb-12 flex flex-wrap justify-between gap-4 sm:gap-6 lg:gap-8 lg:mb-0">
                <Card className="w-full md:w-[48%] hover:shadow-lg transition-all duration-150">
                  <CardBody className="flex flex-row p-4 sm:p-6">
                  <div className="mr-6 flex h-16 w-16 items-center justify-center rounded-lg bg-primary/10 text-primary dark:bg-primary/20">
                    <svg
                      width="29"
                      height="35"
                      viewBox="0 0 29 35"
                      className="fill-current"
                    >
                      <path d="M14.5 0.710938C6.89844 0.710938 0.664062 6.72656 0.664062 14.0547C0.664062 19.9062 9.03125 29.5859 12.6406 33.5234C13.1328 34.0703 13.7891 34.3437 14.5 34.3437C15.2109 34.3437 15.8672 34.0703 16.3594 33.5234C19.9688 29.6406 28.3359 19.9062 28.3359 14.0547C28.3359 6.67188 22.1016 0.710938 14.5 0.710938ZM14.9375 32.2109C14.6641 32.4844 14.2812 32.4844 14.0625 32.2109C11.3828 29.3125 2.57812 19.3594 2.57812 14.0547C2.57812 7.71094 7.9375 2.625 14.5 2.625C21.0625 2.625 26.4219 7.76562 26.4219 14.0547C26.4219 19.3594 17.6172 29.2578 14.9375 32.2109Z" />
                      <path d="M14.5 8.58594C11.2734 8.58594 8.59375 11.2109 8.59375 14.4922C8.59375 17.7188 11.2187 20.3984 14.5 20.3984C17.7812 20.3984 20.4062 17.7734 20.4062 14.4922C20.4062 11.2109 17.7266 8.58594 14.5 8.58594ZM14.5 18.4297C12.3125 18.4297 10.5078 16.625 10.5078 14.4375C10.5078 12.25 12.3125 10.4453 14.5 10.4453C16.6875 10.4453 18.4922 12.25 18.4922 14.4375C18.4922 16.625 16.6875 18.4297 14.5 18.4297Z" />
                    </svg>
                  </div>
                    <div>
                      <h3 className="mb-3 text-lg font-semibold text-dark dark:text-white">
                        Our Location
                      </h3>
                      <p className="text-base leading-relaxed text-body-color dark:text-[#8890AD]">
                        7901 4th St N STE 300, St. Petersburg, FL 33702
                      </p>
                    </div>
                  </CardBody>
                </Card>
                <Card className="w-full md:w-[48%] hover:shadow-lg transition-all duration-150">
                  <CardBody className="flex flex-row p-4 sm:p-6">
                  <div className="mr-2 flex-shrink-0 flex h-10 w-10 sm:h-12 sm:w-12 md:h-16 md:w-16 items-center justify-center rounded-lg bg-primary/10 text-primary dark:bg-primary/20">
                    <svg
                      width="20"
                      height="16"
                      viewBox="0 0 34 25"
                      className="fill-current"
                    >
                      <path d="M30.5156 0.960938H3.17188C1.42188 0.960938 0 2.38281 0 4.13281V20.9219C0 22.6719 1.42188 24.0938 3.17188 24.0938H30.5156C32.2656 24.0938 33.6875 22.6719 33.6875 20.9219V4.13281C33.6875 2.38281 32.2656 0.960938 30.5156 0.960938ZM30.5156 2.875C30.7891 2.875 31.0078 2.92969 31.2266 3.09375L17.6094 11.3516C17.1172 11.625 16.5703 11.625 16.0781 11.3516L2.46094 3.09375C2.67969 2.98438 2.89844 2.875 3.17188 2.875H30.5156ZM30.5156 22.125H3.17188C2.51562 22.125 1.91406 21.5781 1.91406 20.8672V5.00781L15.0391 12.9922C15.5859 13.3203 16.1875 13.4844 16.7891 13.4844C17.3906 13.4844 17.9922 13.3203 18.5391 12.9922L31.6641 5.00781V20.8672C31.7734 21.5781 31.1719 22.125 30.5156 22.125Z" />
                    </svg>
                  </div>
                  <div className="flex-1 min-w-0 w-[calc(100%-60px)]">
                    <h3 className="mb-2 text-base sm:text-lg font-semibold text-dark dark:text-white">
                      How Can We Help?
                    </h3>
                    <div className="overflow-hidden">
                      <p className="mb-2 text-xs sm:text-sm md:text-base leading-relaxed text-body-color dark:text-[#8890AD] break-all">
                        <EMAIL>
                      </p>
                      <p className="mb-2 text-xs sm:text-sm md:text-base leading-relaxed text-body-color dark:text-[#8890AD] break-all">
                       <EMAIL>
                      </p>
                    </div>
                    </div>
                  </CardBody>
                </Card>
              </div>
            </div>
          </div>
          <div className="w-full px-4 lg:w-5/12 xl:w-4/12">
            <Card className="wow fadeInUp shadow-lg hover:shadow-xl transition-all duration-300" data-wow-delay=".2s">
              <CardBody className="p-6 sm:p-8 md:p-10 lg:p-8 2xl:p-10">
                <h3 className="mb-8 text-2xl font-bold text-dark dark:text-white md:text-[28px] md:leading-[1.42]">
                  Send us a Message
                </h3>
                <form name="form" id="form" action="https://formbold.com/s/ozZpr" method="POST" className="space-y-6">
                  <Input
                    type="text"
                    name="name"
                    label="Full Name"
                    placeholder="Enter your name"
                    isRequired
                    variant="flat"
                    size="lg"
                    classNames={{
                      input: "text-dark dark:text-white",
                      inputWrapper: "bg-[#f8f8f8] dark:bg-[#2C303B] border-transparent focus-within:border-primary"
                    }}
                  />
                  <Input
                    type="email"
                    name="email"
                    label="Email"
                    placeholder="Enter your email"
                    isRequired
                    variant="flat"
                    size="lg"
                    classNames={{
                      input: "text-dark dark:text-white",
                      inputWrapper: "bg-[#f8f8f8] dark:bg-[#2C303B] border-transparent focus-within:border-primary"
                    }}
                  />
                  <Textarea
                    name="message"
                    label="Message"
                    placeholder="Type your message here"
                    isRequired
                    variant="flat"
                    size="lg"
                    minRows={4}
                    classNames={{
                      input: "text-dark dark:text-white",
                      inputWrapper: "bg-[#f8f8f8] dark:bg-[#2C303B] border-transparent focus-within:border-primary"
                    }}
                  />
                  <Button
                    type="submit"
                    color="primary"
                    size="lg"
                    className="w-full font-medium shadow-md hover:shadow-lg"
                  >
                    Send Message
                  </Button>
                </form>
              </CardBody>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
