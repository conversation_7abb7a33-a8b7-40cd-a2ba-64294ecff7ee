import axios from "axios";
import React from "react";
import OfferList from "./OfferList";
import { Price } from "@/types/price";
import { Card, CardBody, Button, Chip, Badge } from "@heroui/react";

const PricingBox = ({ product }: { product: Price }) => {
  // POST request
  const handleSubscription = async (e: any) => {
    e.preventDefault();
    const { data } = await axios.post(
      "/api/payment",
      {
        priceId: product.id,
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    );
    window.location.assign(data);
  };

  return (
    <div className="w-full px-4 md:w-1/2 lg:w-1/3">
      <Card
        className="relative z-10 mb-10 h-full hover:shadow-lg transition-all duration-150"
        data-wow-delay=".1s"
      >
        <CardBody className="px-8 py-10 sm:p-12 lg:px-6 lg:py-10 xl:p-14 flex flex-col h-full">
          {product.nickname === "Premium" && (
            <Chip
              color="primary"
              className="absolute right-[-20px] top-[60px] -rotate-90 font-medium"
            >
              Recommended
            </Chip>
          )}
        <div className="mb-3 flex items-center gap-3">
          <span className="text-xl font-medium text-dark dark:text-white">
            {product.nickname}
          </span>
          {product.nickname === "Premium" && (
            <Badge color="success" variant="flat" size="sm">Popular</Badge>
          )}
        </div>
        <h2 className="mb-8 text-4xl font-semibold text-dark dark:text-white xl:text-[42px] xl:leading-[1.21]">
          <span className="text-xl font-medium">$ </span>
          <span className="-ml-1 -tracking-[2px]">
            {(product.unit_amount).toLocaleString("en-US", {
              currency: "USD",
            })}
          </span>
          <span className="text-base font-normal text-body-color dark:text-dark-6">
            {" "}
            Per Month
          </span>
        </h2>

        <div className="mb-8 flex-grow">
          <h3 className="mb-4 text-lg font-medium text-dark dark:text-white">
            Features
          </h3>
          <div className="space-y-2">
            {product?.offers.map((offer, i) => (
              <OfferList key={i} text={offer} />
            ))}
          </div>
        </div>
          <div className="w-full mt-auto">
            <Button
              onPress={handleSubscription}
              color="primary"
              size="lg"
              className="w-full font-medium hover:shadow-lg transform hover:-translate-y-1"
            >
              Purchase Now
            </Button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default PricingBox;
