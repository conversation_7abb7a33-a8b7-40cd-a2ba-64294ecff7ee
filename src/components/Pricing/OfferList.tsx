const OfferList = ({ text }: { text: string }) => {
  return (
    <div className="flex items-center space-x-2.5">
      <span className="flex h-5 w-5 items-center justify-center rounded-full bg-primary/10 text-primary">
        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path 
            d="M5.86875 11.0063L2.14375 7.28125L3.0875 6.3375L5.86875 9.11875L10.9125 4.07499L11.8563 5.01874L5.86875 11.0063Z" 
            fill="currentColor"
          />
        </svg>
      </span>
      <p className="text-base font-medium text-body-color dark:text-dark-6">{text}</p>
    </div>
  );
};

export default OfferList;
