"use client";
import { useEffect, useState } from "react";
import SectionTitle from "../Common/SectionTitle";
import { Accordion, AccordionItem, Button, Divider } from "@heroui/react";
import { QuestionMarkCircleIcon } from "@heroicons/react/24/outline";
import { GetQuoteServicesCTA } from "../CTAForms/ProjectCTAButtons";

const Faq = () => {
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const faqSection = document.getElementById("faq");
      if (faqSection) {
        const sectionTop = faqSection.offsetTop;
        if (scrollPosition > sectionTop - 600) {
          setIsVisible(true);
        }
      }
    };
    
    window.addEventListener("scroll", handleScroll);
    // Initial check
    handleScroll();
    
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <section id="faq" className="relative z-20 overflow-hidden bg-gradient-to-b from-white to-blue-50 pb-12 pt-20 dark:bg-dark dark:from-dark dark:to-dark/90 lg:pb-[70px] lg:pt-[120px]">
      <div className="container">
        <SectionTitle
          subtitle="FAQ"
          title="Questions About Our AI Solutions"
          paragraph="Get answers to the most common questions about our AI services, implementation process, and how we can transform your business."
          width="700px"
          center
        />

        <div className={`mt-[60px] lg:mt-20 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="max-w-4xl mx-auto">
            <Accordion
              variant="splitted"
              selectionMode="multiple"
              className="gap-4"
              itemClasses={{
                base: "px-6 py-4 bg-white dark:bg-dark-2 shadow-md hover:shadow-lg transition-all duration-200 rounded-xl border border-gray-100 dark:border-dark-3",
                title: "font-semibold text-lg text-dark dark:text-white",
                trigger: "py-4 px-0",
                content: "text-body-color dark:text-dark-6 pb-4",
                indicator: "text-primary"
              }}
            >
              <AccordionItem
                key="ai-solutions"
                aria-label="What AI solutions does Digital Wave Systems offer?"
                title="What AI solutions does Digital Wave Systems offer?"
                startContent={
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 text-primary mr-4">
                    <QuestionMarkCircleIcon className="h-6 w-6" />
                  </div>
                }
              >
                We provide a comprehensive range of AI solutions including machine learning integration, natural language processing, computer vision systems, predictive analytics, and custom AI model development. Our solutions are tailored to your specific business needs and can be deployed across various industries including healthcare, finance, retail, and manufacturing.
              </AccordionItem>

              <AccordionItem
                key="business-operations"
                aria-label="How can AI integration improve my business operations?"
                title="How can AI integration improve my business operations?"
                startContent={
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 text-primary mr-4">
                    <QuestionMarkCircleIcon className="h-6 w-6" />
                  </div>
                }
              >
                AI integration can transform your business by automating repetitive tasks, extracting actionable insights from your data, enhancing customer experiences through personalization, improving decision-making with predictive analytics, and optimizing operations. Our clients typically see efficiency improvements of 30-40% in targeted processes within the first six months of implementation.
              </AccordionItem>

              <AccordionItem
                key="cybersecurity"
                aria-label="What does your cybersecurity service include?"
                title="What does your cybersecurity service include?"
                startContent={
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 text-primary mr-4">
                    <QuestionMarkCircleIcon className="h-6 w-6" />
                  </div>
                }
              >
                Our cybersecurity services combine AI-powered threat detection with traditional security measures. We provide real-time monitoring, vulnerability assessments, penetration testing, incident response planning, and employee security training. Our AI systems continuously learn from global threat intelligence to proactively identify and mitigate potential security risks before they impact your business.
              </AccordionItem>

              <AccordionItem
                key="development-process"
                aria-label="What is your software development process?"
                title="What is your software development process?"
                startContent={
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 text-primary mr-4">
                    <QuestionMarkCircleIcon className="h-6 w-6" />
                  </div>
                }
              >
                Our software development follows an agile methodology with a focus on continuous delivery. We begin with thorough requirements gathering, create detailed specifications, develop in short sprints with regular client feedback, perform comprehensive testing, and provide ongoing support after deployment. Our development teams are cross-functional, including UX/UI designers, front-end and back-end developers, QA specialists, and DevOps engineers.
              </AccordionItem>

              <AccordionItem
                key="implementation-timeline"
                aria-label="How long does it take to implement an AI solution?"
                title="How long does it take to implement an AI solution?"
                startContent={
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 text-primary mr-4">
                    <QuestionMarkCircleIcon className="h-6 w-6" />
                  </div>
                }
              >
                Implementation timelines vary based on project complexity and scope. A basic AI integration can be completed in 4-6 weeks, while more comprehensive enterprise solutions typically take 3-6 months. We begin with a proof of concept to demonstrate value quickly, then scale systematically. Throughout the process, we ensure minimal disruption to your existing operations and provide comprehensive training for your team.
              </AccordionItem>

              <AccordionItem
                key="differentiation"
                aria-label="What makes Digital Wave Systems different from other AI service providers?"
                title="What makes Digital Wave Systems different from other AI service providers?"
                startContent={
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10 text-primary mr-4">
                    <QuestionMarkCircleIcon className="h-6 w-6" />
                  </div>
                }
              >
                Our differentiation comes from our end-to-end capabilities, industry expertise, and focus on business outcomes rather than just technology. We combine deep technical knowledge with business strategy to ensure AI implementations deliver measurable ROI. Our solutions are highly scalable, and we provide ongoing optimization and support. Additionally, our proprietary AI frameworks accelerate development while maintaining flexibility for customization to your specific needs.
              </AccordionItem>
            </Accordion>
          </div>
        </div>
        
        {/* Divider for visual separation */}
        <Divider className="my-12 max-w-4xl mx-auto" />

        {/* Call-to-action button */}
        <div className={`mt-12 text-center transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <GetQuoteServicesCTA
            variant="primary"
            size="lg"
            className="font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            source="faq-section"
            analyticsEvent="faq_ask_more"
            ctaText="Ask Us More"
          />
        </div>
      </div>

      {/* Enhanced decorative elements */}
      <div>
        <span className="absolute left-4 top-4 -z-[1] opacity-30">
          <svg
            width="48"
            height="134"
            viewBox="0 0 48 134"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              cx="45.6673"
              cy="132"
              r="1.66667"
              transform="rotate(180 45.6673 132)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="117.333"
              r="1.66667"
              transform="rotate(180 45.6673 117.333)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="102.667"
              r="1.66667"
              transform="rotate(180 45.6673 102.667)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="88.0001"
              r="1.66667"
              transform="rotate(180 45.6673 88.0001)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="73.3335"
              r="1.66667"
              transform="rotate(180 45.6673 73.3335)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="45.0001"
              r="1.66667"
              transform="rotate(180 45.6673 45.0001)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="16.0001"
              r="1.66667"
              transform="rotate(180 45.6673 16.0001)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="59.0001"
              r="1.66667"
              transform="rotate(180 45.6673 59.0001)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="30.6668"
              r="1.66667"
              transform="rotate(180 45.6673 30.6668)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="1.66683"
              r="1.66667"
              transform="rotate(180 45.6673 1.66683)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="31.0013"
              cy="132"
              r="1.66667"
              transform="rotate(180 31.0013 132)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="31.0013"
              cy="117.333"
              r="1.66667"
              transform="rotate(180 31.0013 117.333)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="31.0013"
              cy="102.667"
              r="1.66667"
              transform="rotate(180 31.0013 102.667)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="31.0013"
              cy="88.0001"
              r="1.66667"
              transform="rotate(180 31.0013 88.0001)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="31.0013"
              cy="73.3335"
              r="1.66667"
              transform="rotate(180 31.0013 73.3335)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="31.0013"
              cy="45.0001"
              r="1.66667"
              transform="rotate(180 31.0013 45.0001)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="31.0013"
              cy="16.0001"
              r="1.66667"
              transform="rotate(180 31.0013 16.0001)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="31.0013"
              cy="59.0001"
              r="1.66667"
              transform="rotate(180 31.0013 59.0001)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="31.0013"
              cy="30.6668"
              r="1.66667"
              transform="rotate(180 31.0013 30.6668)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="31.0013"
              cy="1.66683"
              r="1.66667"
              transform="rotate(180 31.0013 1.66683)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="16.3333"
              cy="132"
              r="1.66667"
              transform="rotate(180 16.3333 132)"
              fill="#4A6CF7"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="4s" repeatCount="indefinite" />
            </circle>
            {/* ... keep other circles with same animation pattern ... */}
          </svg>
        </span>
        <span className="absolute bottom-4 right-4 -z-[1] opacity-30">
          <svg
            width="48"
            height="134"
            viewBox="0 0 48 134"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              cx="45.6673"
              cy="132"
              r="1.66667"
              transform="rotate(180 45.6673 132)"
              fill="#3758F9"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="5s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="117.333"
              r="1.66667"
              transform="rotate(180 45.6673 117.333)"
              fill="#3758F9"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="5s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="102.667"
              r="1.66667"
              transform="rotate(180 45.6673 102.667)"
              fill="#3758F9"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="5s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="88.0001"
              r="1.66667"
              transform="rotate(180 45.6673 88.0001)"
              fill="#3758F9"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="5s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="73.3333"
              r="1.66667"
              transform="rotate(180 45.6673 73.3333)"
              fill="#3758F9"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="5s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="45.0001"
              r="1.66667"
              transform="rotate(180 45.6673 45.0001)"
              fill="#3758F9"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="5s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="16.0001"
              r="1.66667"
              transform="rotate(180 45.6673 16.0001)"
              fill="#3758F9"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="5s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="59.0001"
              r="1.66667"
              transform="rotate(180 45.6673 59.0001)"
              fill="#3758F9"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="5s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="30.6668"
              r="1.66667"
              transform="rotate(180 45.6673 30.6668)"
              fill="#3758F9"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="5s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="45.6673"
              cy="1.66683"
              r="1.66667"
              transform="rotate(180 45.6673 1.66683)"
              fill="#3758F9"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="5s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="31.0006"
              cy="132"
              r="1.66667"
              transform="rotate(180 31.0006 132)"
              fill="#3758F9"
            >
              <animate attributeName="opacity" values="0.3;0.6;0.3" dur="5s" repeatCount="indefinite" />
            </circle>
            {/* ... keep other circles with same animation pattern ... */}
          </svg>
        </span>
      </div>
      
      {/* Animated particles */}
      <div className="absolute inset-0 overflow-hidden -z-10 pointer-events-none">
        {Array(8).fill(0).map((_, index) => (
          <div key={index} 
               className="absolute rounded-full bg-blue-400/10 backdrop-blur-sm"
               style={{
                 width: `${Math.random() * 8 + 4}px`,
                 height: `${Math.random() * 8 + 4}px`,
                 top: `${Math.random() * 100}%`,
                 left: `${Math.random() * 100}%`,
                 animation: `float ${Math.random() * 10 + 15}s linear infinite`,
                 animationDelay: `${Math.random() * 5}s`
               }}
          ></div>
        ))}
      </div>
      
      {/* Add animations */}
      <style>{`
        @keyframes float {
          0% { transform: translateY(0) translateX(0); opacity: 0.7; }
          25% { transform: translateY(-30px) translateX(15px); opacity: 1; }
          50% { transform: translateY(-10px) translateX(30px); opacity: 0.7; }
          75% { transform: translateY(-25px) translateX(15px); opacity: 1; }
          100% { transform: translateY(0) translateX(0); opacity: 0.7; }
        }
      `}</style>
    </section>
  );
};

export default Faq;
