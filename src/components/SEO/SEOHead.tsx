"use client";

import { usePathname } from 'next/navigation';
import HreflangLinks from './HreflangLinks';
import MetaRobots from './MetaRobots';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  canonicalUrl?: string;
  imageUrl?: string;
  locale?: 'en' | 'es';
  structuredData?: object;
  // Robots configuration
  index?: boolean;
  follow?: boolean;
  noarchive?: boolean;
  nosnippet?: boolean;
  noimageindex?: boolean;
  customRobotsDirectives?: string[];
  // Google Search Console verification
  googleVerification?: string;
  // Additional meta tags
  additionalMeta?: Array<{
    name?: string;
    property?: string;
    content: string;
  }>;
}

/**
 * Comprehensive SEO Head component
 * Includes all necessary SEO meta tags, structured data, and internationalization support
 */
const SEOHead = ({
  title,
  description,
  keywords,
  canonicalUrl,
  imageUrl,
  locale = 'en',
  structuredData,
  index = true,
  follow = true,
  noarchive = false,
  nosnippet = false,
  noimageindex = false,
  customRobotsDirectives = [],
  googleVerification,
  additionalMeta = []
}: SEOHeadProps) => {
  const pathname = usePathname();
  const baseUrl = 'https://digitalwavesystems.com.co';
  const currentUrl = canonicalUrl || `${baseUrl}${pathname}`;

  return (
    <>
      {/* Basic Meta Tags */}
      {title && <title>{title}</title>}
      {description && <meta name="description" content={description} />}
      {keywords && <meta name="keywords" content={keywords} />}
      
      {/* Language and Content Type */}
      <meta httpEquiv="content-language" content={locale === 'es' ? 'es-ES' : 'en-US'} />
      <meta httpEquiv="content-type" content="text/html; charset=UTF-8" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={currentUrl} />
      
      {/* Robots Meta Tag */}
      <MetaRobots
        index={index}
        follow={follow}
        noarchive={noarchive}
        nosnippet={nosnippet}
        noimageindex={noimageindex}
        customDirectives={customRobotsDirectives}
      />
      
      {/* Hreflang Links for Internationalization */}
      <HreflangLinks />
      
      {/* Open Graph Meta Tags */}
      {title && <meta property="og:title" content={title} />}
      {description && <meta property="og:description" content={description} />}
      <meta property="og:url" content={currentUrl} />
      <meta property="og:type" content="website" />
      <meta property="og:site_name" content="Digital Wave Systems" />
      <meta property="og:locale" content={locale === 'es' ? 'es_ES' : 'en_US'} />
      {imageUrl && <meta property="og:image" content={imageUrl} />}
      {imageUrl && <meta property="og:image:width" content="1200" />}
      {imageUrl && <meta property="og:image:height" content="630" />}
      {imageUrl && title && <meta property="og:image:alt" content={title} />}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@digitalwavesys" />
      <meta name="twitter:creator" content="@digitalwavesys" />
      {title && <meta name="twitter:title" content={title} />}
      {description && <meta name="twitter:description" content={description} />}
      {imageUrl && <meta name="twitter:image" content={imageUrl} />}
      
      {/* Google Search Console Verification */}
      {googleVerification && (
        <meta name="google-site-verification" content={googleVerification} />
      )}
      
      {/* Additional Meta Tags */}
      {additionalMeta.map((meta, index) => (
        <meta
          key={index}
          {...(meta.name ? { name: meta.name } : {})}
          {...(meta.property ? { property: meta.property } : {})}
          content={meta.content}
        />
      ))}
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      )}
      
      {/* Additional SEO Meta Tags */}
      <meta name="author" content="Digital Wave Systems" />
      <meta name="publisher" content="Digital Wave Systems" />
      <meta name="category" content="Technology" />
      <meta name="coverage" content="Worldwide" />
      <meta name="distribution" content="Global" />
      <meta name="rating" content="General" />
      
      {/* Prevent duplicate content issues */}
      <meta name="revisit-after" content="7 days" />
      
      {/* Mobile and Responsive */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* Security Headers */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-Frame-Options" content="DENY" />
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
      
      {/* DNS Prefetch for Performance */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
    </>
  );
};

export default SEOHead;
