"use client";

import { generateRobotsMetaTag } from '@/utils/seo';

interface MetaRobotsProps {
  index?: boolean;
  follow?: boolean;
  noarchive?: boolean;
  nosnippet?: boolean;
  noimageindex?: boolean;
  customDirectives?: string[];
}

/**
 * MetaRobots component for adding proper robots meta tags
 * This component provides fine-grained control over search engine crawling behavior
 */
const MetaRobots = ({
  index = true,
  follow = true,
  noarchive = false,
  nosnippet = false,
  noimageindex = false,
  customDirectives = []
}: MetaRobotsProps) => {
  const robotsContent = generateRobotsMetaTag(index, follow, noarchive, nosnippet, noimageindex);
  const finalContent = customDirectives.length > 0 
    ? `${robotsContent}, ${customDirectives.join(', ')}`
    : robotsContent;

  return (
    <meta name="robots" content={finalContent} />
  );
};

export default MetaRobots;
