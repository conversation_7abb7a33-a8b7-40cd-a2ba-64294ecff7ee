"use client";

import { usePathname } from 'next/navigation';
import { generateHreflangLinks } from '@/utils/seo';

/**
 * HreflangLinks component for adding proper hreflang meta tags
 * This component should be included in the head section of pages
 * to help search engines understand language and regional targeting
 */
const HreflangLinks = () => {
  const pathname = usePathname();
  const hreflangLinks = generateHreflangLinks(pathname);

  return (
    <>
      {hreflangLinks.map((link, index) => (
        <link
          key={index}
          rel={link.rel}
          hrefLang={link.hreflang}
          href={link.href}
        />
      ))}
    </>
  );
};

export default HreflangLinks;
