"use client";
import { useEffect, useState } from "react";
import { GetConsultationCTA, BookConsultationCTA } from "../CTAForms/EnhancedCTAButton";

const CallToAction = () => {
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <section className="relative z-10 overflow-hidden bg-gradient-to-b from-primary to-primary/90 py-20 lg:py-[115px]">
      <div className="container mx-auto">
        <div className="relative overflow-hidden rounded-3xl backdrop-blur-sm bg-white/5 border border-white/10 p-8 md:p-10 lg:p-14 shadow-2xl">
          <div className="-mx-4 flex flex-wrap items-stretch">
            <div className="w-full px-4">
              <div className={`mx-auto max-w-[700px] text-center transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
                <h2 className="mb-5 text-3xl font-bold text-white md:text-[42px] md:leading-[1.2]">
                  <span className="relative inline-block after:absolute after:bottom-0 after:left-0 after:h-[2px] after:w-full after:bg-gradient-to-r after:from-blue-400 after:to-blue-500/40 md:after:h-[3px]">Ready to Accelerate</span>
                  <span className="block mt-2 text-transparent bg-clip-text bg-gradient-to-r from-white via-blue-200 to-white">
                    Your Digital Transformation?
                  </span>
                </h2>
                <p className="mx-auto mb-8 max-w-[580px] text-lg leading-relaxed text-white/90">
                  Partner with Digital Wave Systems to architect and deliver enterprise-grade software solutions that drive measurable business outcomes and competitive advantage.
                </p>
                <div className="flex flex-col sm:flex-row justify-center items-center gap-5">
                  <div className={`transform transition-all duration-1000 delay-300 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
                    <GetConsultationCTA
                      size="lg"
                      className="bg-white text-primary hover:bg-blue-50 font-semibold min-w-[200px] shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                      ctaText="Schedule Consultation"
                      source="main-cta-section"
                      analyticsEvent="cta_main_consultation"
                    />
                  </div>
                  <div className={`transform transition-all duration-1000 delay-500 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
                    <BookConsultationCTA
                      variant="outline"
                      size="lg"
                      className="bg-blue-600/30 backdrop-blur-sm border-blue-500/20 text-white hover:bg-blue-600/50 font-medium min-w-[200px] transform hover:-translate-y-1 hover:shadow-lg"
                      ctaText="Book Strategy Call"
                      consultationType="strategy-session"
                      source="main-cta-section"
                      analyticsEvent="cta_main_strategy"
                    />
                  </div>
                </div>
                
                {/* Trust badges */}
                <div className={`flex flex-wrap justify-center gap-6 items-center mt-10 transition-all duration-1000 delay-700 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
                  <div className="flex items-center gap-2 bg-white/5 backdrop-blur-sm px-4 py-2 rounded-full">
                    <div className="text-blue-300">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                      </svg>
                    </div>
                    <span className="text-white/90 text-sm">SOC 2 Compliant</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white/5 backdrop-blur-sm px-4 py-2 rounded-full">
                    <div className="text-blue-300">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M9 12l2 2 4-4"></path>
                        <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                        <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                        <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"></path>
                        <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"></path>
                      </svg>
                    </div>
                    <span className="text-white/90 text-sm">Agile Methodology</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white/5 backdrop-blur-sm px-4 py-2 rounded-full">
                    <div className="text-blue-300">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12,6 12,12 16,14"></polyline>
                      </svg>
                    </div>
                    <span className="text-white/90 text-sm">24/7 Support</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Animated background elements */}
      <div>
        <span className="absolute left-0 top-0 -z-10 opacity-30">
          <svg
            width="495"
            height="470"
            viewBox="0 0 495 470"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              cx="55"
              cy="442"
              r="138"
              stroke="white"
              strokeOpacity="0.04"
              strokeWidth="50"
            >
              <animate attributeName="r" values="138;148;138" dur="6s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="446"
              r="39"
              stroke="white"
              strokeOpacity="0.04"
              strokeWidth="20"
            >
              <animate attributeName="r" values="39;45;39" dur="4s" repeatCount="indefinite" />
            </circle>
            <path
              d="M245.406 137.609L233.985 94.9852L276.609 106.406L245.406 137.609Z"
              stroke="white"
              strokeOpacity="0.08"
              strokeWidth="12"
            >
              <animate attributeName="stroke-opacity" values="0.08;0.14;0.08" dur="3s" repeatCount="indefinite" />
            </path>
          </svg>
        </span>
        <span className="absolute bottom-0 right-0 -z-10 opacity-30">
          <svg
            width="493"
            height="470"
            viewBox="0 0 493 470"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              cx="462"
              cy="5"
              r="138"
              stroke="white"
              strokeOpacity="0.04"
              strokeWidth="50"
            >
              <animate attributeName="r" values="138;150;138" dur="7s" repeatCount="indefinite" />
            </circle>
            <circle
              cx="49"
              cy="470"
              r="39"
              stroke="white"
              strokeOpacity="0.04"
              strokeWidth="20"
            >
              <animate attributeName="r" values="39;46;39" dur="5s" repeatCount="indefinite" />
            </circle>
            <path
              d="M222.393 226.701L272.808 213.192L259.299 263.607L222.393 226.701Z"
              stroke="white"
              strokeOpacity="0.06"
              strokeWidth="13"
            >
              <animate attributeName="stroke-opacity" values="0.06;0.12;0.06" dur="4s" repeatCount="indefinite" />
            </path>
          </svg>
        </span>
      </div>
      
      {/* Animated particles */}
      <div className="absolute inset-0 overflow-hidden -z-10">
        {Array(8).fill(0).map((_, index) => (
          <div key={index} 
               className="absolute rounded-full bg-blue-400/20 backdrop-blur-sm"
               style={{
                 width: `${Math.random() * 10 + 4}px`,
                 height: `${Math.random() * 10 + 4}px`,
                 top: `${Math.random() * 100}%`,
                 left: `${Math.random() * 100}%`,
                 animation: `float ${Math.random() * 10 + 15}s linear infinite`,
                 animationDelay: `${Math.random() * 5}s`
               }}
          ></div>
        ))}
      </div>
      
      {/* Add animations */}
      <style jsx>{`
        @keyframes float {
          0% { transform: translateY(0) translateX(0); opacity: 0.7; }
          25% { transform: translateY(-30px) translateX(15px); opacity: 1; }
          50% { transform: translateY(-10px) translateX(30px); opacity: 0.7; }
          75% { transform: translateY(-25px) translateX(15px); opacity: 1; }
          100% { transform: translateY(0) translateX(0); opacity: 0.7; }
        }
      `}</style>
    </section>
  );
};

export default CallToAction;
