import Image from "next/image";
import Link from "next/link";

const PopularArticle = (props: {
  coverImage: string;
  title: string;
  slug: string;
}) => {
  const { coverImage, title, slug } = props;
  return (
    <div className="w-full">
      <div
        className="wow fadeInUp group flex w-full items-center gap-4 rounded-lg border border-divider bg-content2 p-3 transition-all duration-200 hover:bg-content3 hover:shadow-md"
        data-wow-delay=".1s"
      >
        <div className="overflow-hidden rounded-lg h-[72px] w-[72px] flex-shrink-0 border border-divider">
          <Link href={`/blogs/${slug}`} aria-label="blog cover" className="block h-full">
            <Image
              src={coverImage!}
              alt={title || "Blog article"}
              width={80}
              height={80}
              className="h-full w-full object-cover object-center transition-transform duration-300 group-hover:scale-110"
            />
          </Link>
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="mb-2">
            <Link
              href={`/blogs/${slug}`}
              className="mb-1 inline-block text-base font-medium leading-snug text-foreground hover:text-primary transition-colors duration-200 line-clamp-2"
            >
              {title}
            </Link>
          </h4>
          <div className="flex items-center">
            <span className="inline-flex items-center rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
              Article
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PopularArticle;
