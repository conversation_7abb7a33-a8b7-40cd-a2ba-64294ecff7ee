"use client";

import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  But<PERSON>
} from '@heroui/react';
import {
  CalendarIcon,
  ClockIcon,
  UserIcon,
  ShareIcon,
  BookmarkIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import OptimizedImage from '../Common/OptimizedImage';

interface EnhancedBlogPostProps {
  title: string;
  content: string;
  author: string;
  authorImage?: string;
  date: string;
  coverImage: string;
  readTime?: string;
  tags?: string[];
  excerpt?: string;
}

const EnhancedBlogPost: React.FC<EnhancedBlogPostProps> = ({
  title,
  content,
  author,
  authorImage,
  date,
  coverImage,
  readTime = "5 min read",
  tags = [],
  excerpt
}) => {
  const formattedDate = format(new Date(date), 'MMMM dd, yyyy');

  return (
    <article className="max-w-4xl mx-auto">
      {/* Hero Section */}
      <div className="relative mb-8">
        <div className="relative h-[400px] md:h-[500px] rounded-2xl overflow-hidden">
          <OptimizedImage
            src={coverImage}
            alt={title}
            className="w-full h-full object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
          
          {/* Article Meta Overlay */}
          <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
            <div className="flex flex-wrap gap-2 mb-4">
              {tags.map((tag, index) => (
                <Chip
                  key={index}
                  size="sm"
                  variant="flat"
                  className="bg-white/20 text-white backdrop-blur-sm"
                >
                  {tag}
                </Chip>
              ))}
            </div>
            
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 leading-tight">
              {title}
            </h1>
            
            {excerpt && (
              <p className="text-lg md:text-xl text-white/90 mb-6 max-w-3xl">
                {excerpt}
              </p>
            )}
            
            <div className="flex items-center gap-6 text-white/80">
              <div className="flex items-center gap-2">
                <Avatar
                  src={authorImage}
                  name={author}
                  size="sm"
                  className="border-2 border-white/20"
                />
                <span className="font-medium">{author}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <CalendarIcon className="h-4 w-4" />
                <span>{formattedDate}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <ClockIcon className="h-4 w-4" />
                <span>{readTime}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Article Actions */}
      <div className="flex justify-between items-center mb-8">
        <div className="flex items-center gap-4">
          <Button
            variant="flat"
            size="sm"
            startContent={<ShareIcon className="h-4 w-4" />}
            className="text-body-color dark:text-dark-6"
          >
            Share
          </Button>
          <Button
            variant="flat"
            size="sm"
            startContent={<BookmarkIcon className="h-4 w-4" />}
            className="text-body-color dark:text-dark-6"
          >
            Save
          </Button>
        </div>
      </div>

      {/* Article Content */}
      <Card className="mb-8">
        <CardBody className="p-8 lg:p-12">
          <div 
            className="prose prose-lg max-w-none dark:prose-invert
              prose-headings:text-dark dark:prose-headings:text-white
              prose-headings:font-bold prose-headings:tracking-tight
              prose-h1:text-4xl prose-h1:mb-6
              prose-h2:text-3xl prose-h2:mb-5 prose-h2:mt-8
              prose-h3:text-2xl prose-h3:mb-4 prose-h3:mt-6
              prose-h4:text-xl prose-h4:mb-3 prose-h4:mt-5
              prose-p:text-body-color dark:prose-p:text-dark-6
              prose-p:leading-relaxed prose-p:mb-6
              prose-a:text-primary prose-a:no-underline hover:prose-a:underline
              prose-strong:text-dark dark:prose-strong:text-white
              prose-strong:font-semibold
              prose-code:text-primary prose-code:bg-primary/10 
              prose-code:px-2 prose-code:py-1 prose-code:rounded
              prose-code:text-sm prose-code:font-medium
              prose-pre:bg-dark prose-pre:border prose-pre:border-dark-3
              prose-pre:rounded-xl prose-pre:p-6 prose-pre:overflow-x-auto
              prose-blockquote:border-l-4 prose-blockquote:border-primary
              prose-blockquote:bg-primary/5 prose-blockquote:p-6
              prose-blockquote:rounded-r-lg prose-blockquote:my-8
              prose-blockquote:text-dark dark:prose-blockquote:text-white
              prose-ul:space-y-2 prose-ol:space-y-2
              prose-li:text-body-color dark:prose-li:text-dark-6
              prose-img:rounded-xl prose-img:shadow-lg
              prose-img:border prose-img:border-gray-200 dark:prose-img:border-dark-3
              prose-table:border prose-table:border-gray-200 dark:prose-table:border-dark-3
              prose-table:rounded-lg prose-table:overflow-hidden
              prose-th:bg-gray-50 dark:prose-th:bg-dark-2
              prose-th:text-dark dark:prose-th:text-white
              prose-th:font-semibold prose-th:p-4
              prose-td:p-4 prose-td:border-t prose-td:border-gray-200 dark:prose-td:border-dark-3
              prose-hr:border-gray-200 dark:prose-hr:border-dark-3 prose-hr:my-8"
            dangerouslySetInnerHTML={{ __html: content }}
          />
        </CardBody>
      </Card>

      {/* Author Bio */}
      <Card className="mb-8">
        <CardBody className="p-6">
          <div className="flex items-start gap-4">
            <Avatar
              src={authorImage}
              name={author}
              size="lg"
              className="flex-shrink-0"
            />
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-dark dark:text-white mb-2">
                About {author}
              </h3>
              <p className="text-body-color dark:text-dark-6 mb-4">
                Senior Software Engineer and Technical Writer at Digital Wave Systems. 
                Passionate about sharing knowledge and helping developers build better software.
              </p>
              <div className="flex gap-2">
                <Button size="sm" variant="flat" color="primary">
                  Follow
                </Button>
                <Button size="sm" variant="flat">
                  View Profile
                </Button>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Article Navigation */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <Button
          variant="flat"
          className="flex-1 justify-start"
          startContent={<span>←</span>}
        >
          Previous Article
        </Button>
        <Button
          variant="flat"
          className="flex-1 justify-end"
          endContent={<span>→</span>}
        >
          Next Article
        </Button>
      </div>
    </article>
  );
};

export default EnhancedBlogPost;
