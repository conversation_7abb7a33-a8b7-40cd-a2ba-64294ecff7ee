"use client";

import React from 'react';
import { Card, CardBody, Divider } from '@heroui/react';
import Newsletter from './Newsletter';
import PopularArticle from './PopularArticle';

interface BlogLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  showNewsletter?: boolean;
  popularPosts?: Array<{
    coverImage: string;
    title: string;
    slug: string;
  }>;
}

const BlogLayout: React.FC<BlogLayoutProps> = ({
  children,
  sidebar,
  showNewsletter = true,
  popularPosts = []
}) => {
  return (
    <section className="pb-10 pt-20 bg-gradient-to-br from-white to-blue-50 dark:from-dark dark:to-dark-800 lg:pb-20 lg:pt-[120px]">
      <div className="container">
        <div className="-mx-4 flex flex-wrap justify-center">
          <div className="w-full px-4">
            <div className="-mx-4 flex flex-wrap">
              {/* Main Content */}
              <div className="w-full px-4 lg:w-8/12">
                {children}
              </div>

              {/* Sidebar */}
              <div className="w-full px-4 lg:w-4/12">
                <div className="space-y-8">
                  {/* Custom Sidebar Content */}
                  {sidebar}

                  {/* Popular Articles */}
                  {popularPosts.length > 0 && (
                    <Card className="rounded-xl border border-blue-200/10 bg-white/50 dark:bg-dark-2/40 backdrop-blur-sm shadow-md">
                      <CardBody className="p-6">
                        <div className="-mx-4 mb-8 flex flex-wrap">
                          <div className="w-full px-4">
                            <h2 className="relative pb-5 text-2xl font-semibold text-dark dark:text-white sm:text-[28px]">
                              More Articles
                              <span className="mt-2 block h-[2px] w-20 bg-primary"></span>
                            </h2>
                          </div>
                          <div className="w-full px-4">
                            <div className="grid gap-4">
                              {popularPosts.slice(0, 3).map((post, i) => (
                                <PopularArticle
                                  key={i}
                                  coverImage={post.coverImage}
                                  title={post.title.slice(0, 50)}
                                  slug={post.slug}
                                />
                              ))}
                            </div>
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  )}

                  {/* Newsletter Signup */}
                  {showNewsletter && (
                    <Card className="rounded-xl border border-blue-200/10 bg-white/50 dark:bg-dark-2/40 backdrop-blur-sm shadow-md">
                      <CardBody className="p-6">
                        <Newsletter />
                      </CardBody>
                    </Card>
                  )}

                  {/* Categories */}
                  <Card className="rounded-xl border border-blue-200/10 bg-white/50 dark:bg-dark-2/40 backdrop-blur-sm shadow-md">
                    <CardBody className="p-6">
                      <h3 className="text-xl font-semibold text-dark dark:text-white mb-6">
                        Categories
                      </h3>
                      <div className="space-y-3">
                        {[
                          { name: 'Software Development', count: 12 },
                          { name: 'AI & Machine Learning', count: 8 },
                          { name: 'Cloud Computing', count: 6 },
                          { name: 'Cybersecurity', count: 5 },
                          { name: 'DevOps', count: 4 },
                          { name: 'Enterprise Solutions', count: 3 }
                        ].map((category, index) => (
                          <div key={index} className="flex items-center justify-between">
                            <span className="text-body-color dark:text-dark-6 hover:text-primary cursor-pointer transition-colors duration-200">
                              {category.name}
                            </span>
                            <span className="text-sm text-body-color dark:text-dark-6 bg-gray-100 dark:bg-dark-3 px-2 py-1 rounded">
                              {category.count}
                            </span>
                          </div>
                        ))}
                      </div>
                    </CardBody>
                  </Card>

                  {/* Tags */}
                  <Card className="rounded-xl border border-blue-200/10 bg-white/50 dark:bg-dark-2/40 backdrop-blur-sm shadow-md">
                    <CardBody className="p-6">
                      <h3 className="text-xl font-semibold text-dark dark:text-white mb-6">
                        Popular Tags
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {[
                          'React', 'Next.js', 'TypeScript', 'Node.js', 'Python',
                          'AI', 'Machine Learning', 'Cloud', 'AWS', 'Docker',
                          'Kubernetes', 'DevOps', 'Security', 'API', 'Database'
                        ].map((tag, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 text-sm bg-gray-100 dark:bg-dark-3 text-body-color dark:text-dark-6 rounded-full hover:bg-primary hover:text-white cursor-pointer transition-all duration-200"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </CardBody>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BlogLayout;
