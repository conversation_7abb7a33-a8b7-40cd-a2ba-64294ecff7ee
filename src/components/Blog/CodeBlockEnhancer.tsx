"use client";

import { useEffect } from "react";

export default function CodeBlockEnhancer() {
  useEffect(() => {
    // Add language labels and copy buttons to code blocks
    const enhanceCodeBlocks = () => {
      const codeBlocks = document.querySelectorAll('pre[class*="language-"]');
      
      codeBlocks.forEach((pre) => {
        // Only process blocks that haven't been enhanced yet
        if (pre.parentElement?.querySelector('.code-block-header')) return;
        
        // Get the language class
        const languageClass = Array.from(pre.classList).find(className => 
          className.startsWith('language-')
        );
        
        const language = languageClass 
          ? languageClass.replace('language-', '') 
          : 'code';
          
        // Create the header element
        const header = document.createElement('div');
        header.className = 'code-block-header';
        header.innerHTML = `<span>${language}</span>`;
        
        // Create a wrapper div if needed
        const wrapper = document.createElement('div');
        wrapper.className = 'relative';
        
        // Insert wrapper before pre and move pre into it
        pre.parentNode?.insertBefore(wrapper, pre);
        wrapper.appendChild(header);
        wrapper.appendChild(pre);
        
        // Add copy button
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-to-clipboard-button';
        copyButton.textContent = 'Copy';
        
        pre.appendChild(copyButton);
        
        copyButton.addEventListener('click', () => {
          const code = pre.querySelector('code')?.textContent || '';
          
          // Use a more robust approach to copying text
          const copyToClipboard = (text: string) => {
            // First try using the clipboard API
            if (navigator.clipboard && window.isSecureContext) {
              return navigator.clipboard.writeText(text)
                .then(() => true)
                .catch(() => false);
            } else {
              // Fallback to document.execCommand for older browsers
              try {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                const success = document.execCommand('copy');
                document.body.removeChild(textArea);
                return Promise.resolve(success);
              } catch (error) {
                return Promise.resolve(false);
              }
            }
          };
          
          copyToClipboard(code).then(
            (success) => {
              if (success) {
                copyButton.textContent = 'Copied!';
                copyButton.classList.add('copied');
                
                setTimeout(() => {
                  copyButton.textContent = 'Copy';
                  copyButton.classList.remove('copied');
                }, 2000);
              } else {
                copyButton.textContent = 'Failed to copy';
                setTimeout(() => {
                  copyButton.textContent = 'Copy';
                }, 2000);
              }
            }
          );
        });
      });
    };

    // Run on initial load
    enhanceCodeBlocks();
    
    // Run when content might have changed
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length) {
          enhanceCodeBlocks();
        }
      });
    });
    
    observer.observe(document.body, { childList: true, subtree: true });
    
    return () => {
      // Cleanup
      observer.disconnect();
    };
  }, []);

  return null; // This component doesn't render anything
} 