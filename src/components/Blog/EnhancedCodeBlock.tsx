"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, CardBody, Chip } from '@heroui/react';
import { 
  ClipboardIcon, 
  CheckIcon,
  DocumentDuplicateIcon 
} from '@heroicons/react/24/outline';

interface EnhancedCodeBlockProps {
  code: string;
  language?: string;
  filename?: string;
  showLineNumbers?: boolean;
  highlightLines?: number[];
  className?: string;
}

const EnhancedCodeBlock: React.FC<EnhancedCodeBlockProps> = ({
  code,
  language = 'text',
  filename,
  showLineNumbers = true,
  highlightLines = [],
  className = ''
}) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  const lines = code.split('\n');

  const getLanguageColor = (lang: string) => {
    const colors: Record<string, string> = {
      javascript: 'warning',
      typescript: 'primary',
      python: 'success',
      java: 'danger',
      css: 'secondary',
      html: 'warning',
      json: 'default',
      bash: 'default',
      shell: 'default',
      sql: 'primary',
      yaml: 'secondary',
      xml: 'warning',
      markdown: 'default'
    };
    return colors[lang.toLowerCase()] || 'default';
  };

  return (
    <Card className={`my-6 overflow-hidden ${className}`}>
      {/* Code Block Header */}
      {(filename || language) && (
        <div className="flex items-center justify-between px-4 py-3 bg-gray-50 dark:bg-dark-2 border-b border-gray-200 dark:border-dark-3">
          <div className="flex items-center gap-3">
            {filename && (
              <span className="text-sm font-medium text-dark dark:text-white">
                {filename}
              </span>
            )}
            {language && (
              <Chip 
                size="sm" 
                variant="flat" 
                color={getLanguageColor(language) as any}
                className="text-xs"
              >
                {language.toUpperCase()}
              </Chip>
            )}
          </div>
          
          <Button
            size="sm"
            variant="flat"
            isIconOnly
            onPress={handleCopy}
            className="text-body-color dark:text-dark-6 hover:text-primary"
            aria-label="Copy code"
          >
            {copied ? (
              <CheckIcon className="h-4 w-4 text-success" />
            ) : (
              <DocumentDuplicateIcon className="h-4 w-4" />
            )}
          </Button>
        </div>
      )}

      {/* Code Content */}
      <CardBody className="p-0">
        <div className="relative">
          <pre className="overflow-x-auto p-6 bg-dark text-white text-sm leading-relaxed">
            <code className={`language-${language}`}>
              {showLineNumbers ? (
                <div className="table w-full">
                  {lines.map((line, index) => {
                    const lineNumber = index + 1;
                    const isHighlighted = highlightLines.includes(lineNumber);
                    
                    return (
                      <div 
                        key={index} 
                        className={`table-row ${isHighlighted ? 'bg-primary/20' : ''}`}
                      >
                        <div className="table-cell w-12 pr-4 text-right text-gray-500 select-none border-r border-gray-700">
                          {lineNumber}
                        </div>
                        <div className="table-cell pl-4">
                          {line || '\u00A0'}
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                code
              )}
            </code>
          </pre>
          
          {/* Copy button overlay for code without header */}
          {!filename && !language && (
            <Button
              size="sm"
              variant="flat"
              isIconOnly
              onPress={handleCopy}
              className="absolute top-3 right-3 text-white/70 hover:text-white bg-black/20 backdrop-blur-sm"
              aria-label="Copy code"
            >
              {copied ? (
                <CheckIcon className="h-4 w-4 text-success" />
              ) : (
                <ClipboardIcon className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>
      </CardBody>
    </Card>
  );
};

// Wrapper component for markdown code blocks
export const MarkdownCodeBlock: React.FC<{
  children: string;
  className?: string;
}> = ({ children, className = '' }) => {
  // Extract language from className (e.g., "language-javascript")
  const language = className.replace('language-', '');
  
  return (
    <EnhancedCodeBlock
      code={children}
      language={language}
      showLineNumbers={true}
    />
  );
};

export default EnhancedCodeBlock;
