import SectionTitle from "../Common/SectionTitle";
import SingleBlog from "./SingleBlog";
import { But<PERSON>, Divider, Spacer } from "@heroui/react";
import Link from "next/link";

const HomeBlogSection = ({ posts }: any) => {
  return (
    <section className="bg-gradient-to-b from-white to-gray-50 pb-16 pt-20 dark:bg-dark dark:from-dark dark:to-dark-2 lg:pb-24 lg:pt-[120px]">
      <div className="container mx-auto">
        <div className="mb-16 lg:mb-20">
          <SectionTitle
            subtitle="Latest Insights"
            title="Expert Insights on Software Development & AI"
            paragraph="Stay updated with the latest trends, best practices, and innovations in software consulting, AI integration, and digital transformation."
            width="700px"
            center
          />
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {posts.slice(0, 3).map((blog: any, index: number) => (
            <div key={blog.id || `blog-${index}`} className="h-full">
              <SingleBlog blog={blog} />
            </div>
          ))}
        </div>

        <Spacer y={12} />
        <Divider className="max-w-xs mx-auto" />
        <Spacer y={8} />

        {/* Call to Action */}
        <div className="text-center">
          <Button
            as={Link}
            href="/blogs"
            color="primary"
            size="lg"
            className="font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1"
          >
            View All Articles
          </Button>
        </div>
      </div>
    </section>
  );
};

export default HomeBlogSection;
