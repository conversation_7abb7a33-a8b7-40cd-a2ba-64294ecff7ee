import { Blog } from "@/types/blog";
import Image from "next/image";
import Link from "next/link";
import { Card, CardBody, Chip } from "@heroui/react";
import { CalendarIcon, ClockIcon } from "@heroicons/react/24/outline";
import { format } from "date-fns";

const SingleBlog = ({ blog }: { blog: Blog }) => {
  const { title, coverImage, excerpt, slug, date } = blog;
  const formattedDate = date ? format(new Date(date), 'MMM dd, yyyy') : '';

  return (
    <div className="wow fadeInUp group mb-10 h-full" data-wow-delay=".1s">
      <Card className="flex flex-col h-full hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 border border-gray-100 dark:border-dark-3 bg-white dark:bg-dark-2">
        <div className="relative overflow-hidden rounded-t-xl h-[240px]">
          <Link href={`/blogs/${slug}`} aria-label="blog cover" className="block h-full">
            <Image
              src={coverImage!}
              alt={title || "Blog post"}
              className="w-full h-full object-cover transition-all duration-300 group-hover:scale-105"
              width={408}
              height={272}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-primary/90 via-primary/40 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>

            <div className="absolute inset-0 flex items-center justify-center opacity-0 transition-all duration-300 group-hover:opacity-100">
              <Chip
                color="primary"
                variant="solid"
                className="text-white font-medium shadow-lg transform translate-y-4 group-hover:translate-y-0 transition-all duration-300"
              >
                Read Article
              </Chip>
            </div>
          </Link>
        </div>
        <CardBody className="p-8 flex-grow flex flex-col">
          <div className="space-y-4 flex-grow">
            <h3 className="text-xl font-bold text-dark dark:text-white leading-tight">
              <Link
                href={`/blogs/${slug}`}
                className="hover:text-primary dark:hover:text-primary transition-colors duration-200 line-clamp-2"
              >
                {title}
              </Link>
            </h3>
            <p className="text-body-color dark:text-dark-6 leading-relaxed line-clamp-3 flex-grow">
              {excerpt}
            </p>
          </div>

          {formattedDate && (
            <div className="flex items-center gap-2 text-sm text-body-color dark:text-dark-6 mb-4">
              <CalendarIcon className="h-4 w-4" />
              <span>{formattedDate}</span>
              <ClockIcon className="h-4 w-4 ml-2" />
              <span>5 min read</span>
            </div>
          )}

          <div className="mt-8 pt-6 border-t border-gray-100 dark:border-dark-3 flex items-center justify-between">
            <Link
              href={`/blogs/${slug}`}
              className="inline-flex items-center gap-2 text-primary hover:text-primary-dark font-medium transition-all duration-200 group/link"
            >
              <span>Read More</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 transition-transform duration-200 group-hover/link:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>

            <Chip
              color="primary"
              variant="flat"
              size="sm"
              className="font-medium"
            >
              Insights
            </Chip>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default SingleBlog;
