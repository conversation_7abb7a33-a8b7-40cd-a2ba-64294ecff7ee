const Newsletter = () => {
  return (
    <div
      className="wow fadeInUp relative overflow-hidden rounded-xl bg-gradient-to-br from-primary to-primary-600 px-8 py-8 text-center"
      data-wow-delay=".1s"
    >
      <h3 className="mb-2 text-2xl font-semibold leading-tight text-primary-foreground">
        Contact Us
      </h3>
      <p className="mb-5 text-base text-primary-foreground/80">
        Tell us about your needs and we'll connect with you.
      </p>
      <form name="form" id="form" action="https://formbold.com/s/ozZpr" method="POST">
        <div className="mb-4">
          <input
            type="text"
            name="name"
            placeholder="Your Company Name"
            className="h-[50px] w-full rounded-lg border border-primary-foreground/20 bg-primary-foreground/10 px-4 text-base text-primary-foreground outline-none placeholder:text-primary-foreground/60 focus:border-primary-foreground focus-visible:shadow-none transition-all duration-200 hover:bg-primary-foreground/20"
          />
        </div>
        <div className="mb-4">
          <input
            type="email"
            name="email"
            placeholder="Your Email Address"
            className="h-[50px] w-full rounded-lg border border-primary-foreground/20 bg-primary-foreground/10 px-4 text-base text-primary-foreground outline-none placeholder:text-primary-foreground/60 focus:border-primary-foreground focus-visible:shadow-none transition-all duration-200 hover:bg-primary-foreground/20"
          />
        </div>
        <div className="mb-5">
          <textarea
            name="message"
            rows={3}
            placeholder="Tell us about your needs"
            className="h-[120px] w-full rounded-lg border border-primary-foreground/20 bg-primary-foreground/10 p-4 text-base text-primary-foreground outline-none placeholder:text-primary-foreground/60 focus:border-primary-foreground focus-visible:shadow-none transition-all duration-200 hover:bg-primary-foreground/20"
          ></textarea>
        </div>
        <div className="mb-3">
          <button
            type="submit"
            className="h-[50px] w-full cursor-pointer rounded-lg bg-primary-foreground text-center text-sm font-medium text-primary transition-all duration-200 ease-in-out hover:bg-primary-foreground/90 hover:shadow-lg"
          >
            Send Message
          </button>
        </div>
      </form>
      <p className="text-sm text-primary-foreground/80">
        We respect your privacy and won't share your information
      </p>

      <div>
        <span className="absolute right-0 top-0">
          <svg
            width="46"
            height="46"
            viewBox="0 0 46 46"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              cx="1.39737"
              cy="44.6026"
              r="1.39737"
              transform="rotate(-90 1.39737 44.6026)"
              fill="white"
              fillOpacity="0.44"
            />
            <circle
              cx="1.39737"
              cy="7.9913"
              r="1.39737"
              transform="rotate(-90 1.39737 7.9913)"
              fill="white"
              fillOpacity="0.44"
            />
            <circle
              cx="13.6943"
              cy="44.6026"
              r="1.39737"
              transform="rotate(-90 13.6943 44.6026)"
              fill="white"
              fillOpacity="0.44"
            />
            <circle
              cx="13.6943"
              cy="7.9913"
              r="1.39737"
              transform="rotate(-90 13.6943 7.9913)"
              fill="white"
              fillOpacity="0.44"
            />
            <circle
              cx="25.9911"
              cy="44.6026"
              r="1.39737"
              transform="rotate(-90 25.9911 44.6026)"
              fill="white"
              fillOpacity="0.44"
            />
            <circle
              cx="25.9911"
              cy="7.9913"
              r="1.39737"
              transform="rotate(-90 25.9911 7.9913)"
              fill="white"
              fillOpacity="0.44"
            />
            <circle
              cx="38.288"
              cy="44.6026"
              r="1.39737"
              transform="rotate(-90 38.288 44.6026)"
              fill="white"
              fillOpacity="0.44"
            />
            <circle
              cx="38.288"
              cy="7.9913"
              r="1.39737"
              transform="rotate(-90 38.288 7.9913)"
              fill="white"
              fillOpacity="0.44"
            />
            <circle
              cx="1.39737"
              cy="32.3058"
              r="1.39737"
              transform="rotate(-90 1.39737 32.3058)"
              fill="white"
              fillOpacity="0.44"
            />
            <circle
              cx="13.6943"
              cy="32.3058"
              r="1.39737"
              transform="rotate(-90 13.6943 32.3058)"
              fill="white"
              fillOpacity="0.44"
            />
            <circle
              cx="25.9911"
              cy="32.3058"
              r="1.39737"
              transform="rotate(-90 25.9911 32.3058)"
              fill="white"
              fillOpacity="0.44"
            />
            <circle
              cx="38.288"
              cy="32.3058"
              r="1.39737"
              transform="rotate(-90 38.288 32.3058)"
              fill="white"
              fillOpacity="0.44"
            />
            <circle
              cx="1.39737"
              cy="20.0086"
              r="1.39737"
              transform="rotate(-90 1.39737 20.0086)"
              fill="white"
              fillOpacity="0.44"
            />
            <circle
              cx="13.6943"
              cy="20.0086"
              r="1.39737"
              transform="rotate(-90 13.6943 20.0086)"
              fill="white"
              fillOpacity="0.44"
            />
            <circle
              cx="25.9911"
              cy="20.0086"
              r="1.39737"
              transform="rotate(-90 25.9911 20.0086)"
              fill="white"
              fillOpacity="0.44"
            />
            <circle
              cx="38.288"
              cy="20.0086"
              r="1.39737"
              transform="rotate(-90 38.288 20.0086)"
              fill="white"
              fillOpacity="0.44"
            />
          </svg>
        </span>
      </div>
    </div>
  );
};

export default Newsletter;
