"use client";
import React, { useState } from 'react';
import { Button, Modal, ModalContent, ModalBody, useDisclosure } from '@heroui/react';
import { 
  RocketLaunchIcon,
  DocumentTextIcon,
  BuildingOfficeIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import StartProjectForm from './StartProjectForm';
import GetQuoteForm from './GetQuoteForm';

interface ProjectCTAButtonProps {
  // Button appearance
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  
  // CTA behavior
  formType: 'start-project' | 'get-quote';
  ctaText?: string;
  
  // Context
  source?: string;
  
  // Customization
  icon?: React.ReactNode;
  showIcon?: boolean;
  fullWidth?: boolean;
  
  // Callbacks
  onSuccess?: (data: any) => void;
  onClick?: () => void;
  
  // Analytics
  trackingId?: string;
  analyticsEvent?: string;
}

const ProjectCTAButton: React.FC<ProjectCTAButtonProps> = ({
  variant = 'primary',
  size = 'md',
  className = '',
  formType,
  ctaText,
  source = 'cta-button',
  icon,
  showIcon = true,
  fullWidth = false,
  onSuccess,
  onClick,
  trackingId,
  analyticsEvent
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [isLoading, setIsLoading] = useState(false);

  // Default CTA configurations
  const ctaConfigs = {
    'start-project': {
      text: 'Start Your Project',
      icon: <RocketLaunchIcon className="h-5 w-5" />,
      modalTitle: 'Start Your Software Project',
      description: 'High-intent project inquiry form',
      analyticsCategory: 'High Intent Lead'
    },
    'get-quote': {
      text: 'Get Development Quote',
      icon: <DocumentTextIcon className="h-5 w-5" />,
      modalTitle: 'Get Your Development Quote',
      description: 'Lead generation form',
      analyticsCategory: 'Lead Generation'
    }
  };

  const config = ctaConfigs[formType];
  const buttonText = ctaText || config.text;
  const buttonIcon = icon || (showIcon ? config.icon : null);

  const handleClick = () => {
    // Track analytics if configured
    const eventName = analyticsEvent || `${formType.replace('-', '_')}_clicked`;
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', eventName, {
        event_category: 'CTA',
        event_label: formType,
        custom_parameter_1: source,
        value: 1
      });
    }

    // Custom click handler
    if (onClick) {
      onClick();
      return;
    }

    // Open modal for form
    onOpen();
  };

  const handleFormSuccess = (data: any) => {
    onClose();
    onSuccess?.(data);
    
    // Track conversion
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'conversion', {
        event_category: config.analyticsCategory,
        event_label: `${formType}_success`,
        value: 1
      });
    }
  };

  const renderForm = () => {
    switch (formType) {
      case 'start-project':
        return (
          <StartProjectForm
            onSuccess={handleFormSuccess}
            onClose={onClose}
            source={source}
          />
        );
      
      case 'get-quote':
        return (
          <GetQuoteForm
            onSuccess={handleFormSuccess}
            onClose={onClose}
            source={source}
          />
        );
      
      default:
        return null;
    }
  };

  // Button variant mapping
  const getButtonProps = () => {
    const baseProps = {
      size,
      className: `${fullWidth ? 'w-full' : ''} ${className}`,
      onPress: handleClick,
      isLoading,
      startContent: buttonIcon
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseProps,
          color: 'primary' as const,
          variant: 'solid' as const
        };
      case 'secondary':
        return {
          ...baseProps,
          color: 'secondary' as const,
          variant: 'solid' as const
        };
      case 'outline':
        return {
          ...baseProps,
          color: 'primary' as const,
          variant: 'bordered' as const
        };
      case 'ghost':
        return {
          ...baseProps,
          color: 'primary' as const,
          variant: 'light' as const
        };
      default:
        return {
          ...baseProps,
          color: 'primary' as const,
          variant: 'solid' as const
        };
    }
  };

  return (
    <>
      <Button {...getButtonProps()}>
        {buttonText}
      </Button>

      <Modal 
        isOpen={isOpen} 
        onClose={onClose}
        size={formType === 'start-project' ? '4xl' : '2xl'}
        scrollBehavior="inside"
        classNames={{
          base: "max-h-[95vh]",
          body: "p-0"
        }}
      >
        <ModalContent>
          <ModalBody>
            {renderForm()}
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

// Preset CTA components for Start Project (High-Intent)
export const StartProjectCTA: React.FC<Omit<ProjectCTAButtonProps, 'formType'>> = (props) => (
  <ProjectCTAButton {...props} formType="start-project" />
);

export const StartProjectHeroCTA: React.FC<Omit<ProjectCTAButtonProps, 'formType' | 'className' | 'size'>> = (props) => (
  <ProjectCTAButton 
    {...props} 
    formType="start-project"
    size="lg"
    className="bg-white text-primary hover:bg-blue-50 font-semibold px-8 py-3 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200"
  />
);

export const StartProjectServicesCTA: React.FC<Omit<ProjectCTAButtonProps, 'formType'>> = (props) => (
  <ProjectCTAButton 
    {...props} 
    formType="start-project"
    ctaText="Start Your Project"
    analyticsEvent="services_start_project"
  />
);

// Preset CTA components for Get Quote (Lead Generation)
export const GetQuoteCTA: React.FC<Omit<ProjectCTAButtonProps, 'formType'>> = (props) => (
  <ProjectCTAButton {...props} formType="get-quote" />
);

export const GetQuoteHeroCTA: React.FC<Omit<ProjectCTAButtonProps, 'formType' | 'variant'>> = (props) => (
  <ProjectCTAButton 
    {...props} 
    formType="get-quote"
    variant="outline"
    ctaText="Get Development Quote"
    analyticsEvent="hero_get_quote"
  />
);

export const GetQuoteServicesCTA: React.FC<Omit<ProjectCTAButtonProps, 'formType'>> = (props) => (
  <ProjectCTAButton 
    {...props} 
    formType="get-quote"
    ctaText="Get Quote"
    analyticsEvent="services_get_quote"
  />
);

// Service-specific variations
export const CustomSoftwareProjectCTA: React.FC<Omit<ProjectCTAButtonProps, 'formType' | 'source'>> = (props) => (
  <ProjectCTAButton 
    {...props} 
    formType="start-project"
    source="custom-software-service"
    analyticsEvent="custom_software_start_project"
  />
);

export const EnterpriseProjectCTA: React.FC<Omit<ProjectCTAButtonProps, 'formType' | 'source'>> = (props) => (
  <ProjectCTAButton 
    {...props} 
    formType="start-project"
    source="enterprise-service"
    analyticsEvent="enterprise_start_project"
    icon={<BuildingOfficeIcon className="h-5 w-5" />}
  />
);

export const AIMLProjectCTA: React.FC<Omit<ProjectCTAButtonProps, 'formType' | 'source'>> = (props) => (
  <ProjectCTAButton 
    {...props} 
    formType="start-project"
    source="ai-ml-service"
    analyticsEvent="ai_ml_start_project"
  />
);

// Budget-focused variations
export const HighBudgetProjectCTA: React.FC<Omit<ProjectCTAButtonProps, 'formType'>> = (props) => (
  <ProjectCTAButton 
    {...props} 
    formType="start-project"
    ctaText="Start Enterprise Project"
    icon={<CurrencyDollarIcon className="h-5 w-5" />}
    analyticsEvent="high_budget_project"
  />
);

export const FlexibleBudgetQuoteCTA: React.FC<Omit<ProjectCTAButtonProps, 'formType'>> = (props) => (
  <ProjectCTAButton 
    {...props} 
    formType="get-quote"
    ctaText="Explore Options"
    analyticsEvent="flexible_budget_quote"
  />
);

// Urgency-focused variations
export const UrgentProjectCTA: React.FC<Omit<ProjectCTAButtonProps, 'formType' | 'className'>> = (props) => (
  <ProjectCTAButton 
    {...props} 
    formType="start-project"
    ctaText="Start Urgent Project"
    className={`${props.className || ''} animate-pulse`}
    analyticsEvent="urgent_project"
  />
);

export const ExploreQuoteCTA: React.FC<Omit<ProjectCTAButtonProps, 'formType'>> = (props) => (
  <ProjectCTAButton 
    {...props} 
    formType="get-quote"
    ctaText="Explore Solutions"
    variant="ghost"
    analyticsEvent="explore_quote"
  />
);

export default ProjectCTAButton;
