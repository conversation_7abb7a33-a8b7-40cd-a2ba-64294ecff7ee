"use client";
import React, { useState } from 'react';
import { 
  Card, 
  CardBody, 
  Input, 
  Button, 
  RadioGroup,
  Radio,
  Checkbox,
  Select,
  SelectItem,
  Chip
} from '@heroui/react';
import { toast } from 'react-hot-toast';
import { PhoneIcon, VideoCameraIcon, ClockIcon } from '@heroicons/react/24/outline';

interface QuickQuoteFormProps {
  onSuccess?: (data: any) => void;
  onClose?: () => void;
  source?: string;
}

interface QuoteFormData {
  name: string;
  email: string;
  phone: string;
  company: string;
  consultationType: 'video' | 'phone';
  serviceInterest: string;
  urgency: 'asap' | 'this-week' | 'this-month' | 'exploring';
  privacyConsent: boolean;
}

const QuickQuoteForm: React.FC<QuickQuoteFormProps> = ({
  onSuccess,
  onClose,
  source = 'quick-quote-form'
}) => {
  const [formData, setFormData] = useState<QuoteFormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    consultationType: 'video',
    serviceInterest: '',
    urgency: 'this-week',
    privacyConsent: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCalendly, setShowCalendly] = useState(false);

  const serviceOptions = [
    { key: 'custom-software', label: 'Custom Software Development' },
    { key: 'ai-ml', label: 'AI & Machine Learning' },
    { key: 'mobile-app', label: 'Mobile App Development' },
    { key: 'web-app', label: 'Web Application' },
    { key: 'cybersecurity', label: 'Cybersecurity Solutions' },
    { key: 'cloud-devops', label: 'Cloud & DevOps' },
    { key: 'enterprise', label: 'Enterprise Solutions' },
    { key: 'not-sure', label: 'Not Sure - Need Guidance' }
  ];

  const urgencyOptions = [
    { key: 'asap', label: 'ASAP - Urgent', color: 'danger' },
    { key: 'this-week', label: 'This Week', color: 'warning' },
    { key: 'this-month', label: 'This Month', color: 'primary' },
    { key: 'exploring', label: 'Just Exploring', color: 'default' }
  ];

  const handleInputChange = (field: keyof QuoteFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Show/hide Calendly based on consultation type
    if (field === 'consultationType') {
      setShowCalendly(value === 'video');
    }
  };

  const validateForm = (): boolean => {
    return !!(
      formData.name.trim() &&
      formData.email.trim() &&
      formData.consultationType &&
      formData.privacyConsent
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fill in all required fields and accept the privacy policy');
      return;
    }

    setIsSubmitting(true);

    try {
      // For video calls, we'll handle Calendly integration separately
      if (formData.consultationType === 'video') {
        // Show Calendly widget and capture lead info
        await handleVideoCallLead();
      } else {
        // For phone calls, trigger immediate callback workflow
        await handlePhoneCallLead();
      }
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error('Something went wrong. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleVideoCallLead = async () => {
    const payload = {
      ...formData,
      consultationType: 'video',
      leadSource: source,
      pageUrl: window.location.href,
      timestamp: new Date().toISOString(),
      intent: 'schedule_video_consultation'
    };

    const response = await fetch('/api/n8n-webhooks/quick-quote', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });

    const result = await response.json();

    if (result.success) {
      toast.success('Great! Please select a time that works for you below.');
      setShowCalendly(true);
      
      // Track conversion
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('event', 'video_consultation_requested', {
          event_category: 'Lead Generation',
          event_label: formData.serviceInterest,
          value: 1
        });
      }
    } else {
      throw new Error(result.message || 'Failed to process request');
    }
  };

  const handlePhoneCallLead = async () => {
    const payload = {
      ...formData,
      consultationType: 'phone',
      leadSource: source,
      pageUrl: window.location.href,
      timestamp: new Date().toISOString(),
      intent: 'request_immediate_callback',
      callbackRequested: true
    };

    const response = await fetch('/api/n8n-webhooks/quick-quote', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });

    const result = await response.json();

    if (result.success) {
      const callbackTime = getCallbackTimeEstimate(formData.urgency);
      toast.success(`Perfect! We'll call you ${callbackTime}. Keep your phone handy!`);
      
      onSuccess?.(result);
      
      // Track conversion
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('event', 'phone_callback_requested', {
          event_category: 'Lead Generation',
          event_label: formData.serviceInterest,
          urgency: formData.urgency,
          value: 1
        });
      }

      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        company: '',
        consultationType: 'video',
        serviceInterest: '',
        urgency: 'this-week',
        privacyConsent: false
      });
    } else {
      throw new Error(result.message || 'Failed to process request');
    }
  };

  const getCallbackTimeEstimate = (urgency: string): string => {
    switch (urgency) {
      case 'asap':
        return 'within 15 minutes';
      case 'this-week':
        return 'within 2 hours';
      case 'this-month':
        return 'within 24 hours';
      default:
        return 'within 48 hours';
    }
  };

  const CalendlyWidget = () => (
    <div className="mt-6">
      <Card>
        <CardBody className="p-6">
          <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">
            Schedule Your Video Consultation
          </h3>
          <div className="bg-white dark:bg-dark rounded-lg" style={{ height: '600px' }}>
            {/* Calendly Inline Widget */}
            <iframe
              src={`https://calendly.com/your-calendly-link?hide_event_type_details=1&hide_gdpr_banner=1&prefill_name=${encodeURIComponent(formData.name)}&prefill_email=${encodeURIComponent(formData.email)}`}
              width="100%"
              height="100%"
              frameBorder="0"
              title="Schedule Consultation"
            />
          </div>
          <p className="text-sm text-body-color dark:text-dark-6 mt-4">
            Can't find a suitable time? <Button
              variant="light"
              size="sm"
              onPress={() => setShowCalendly(false)}
              className="text-primary p-0 h-auto min-w-0"
            >
              Request a phone call instead
            </Button>
          </p>
        </CardBody>
      </Card>
    </div>
  );

  if (showCalendly && formData.consultationType === 'video') {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardBody className="p-6">
          <CalendlyWidget />
          {onClose && (
            <Button
              variant="light"
              onPress={onClose}
              className="absolute top-4 right-4"
            >
              ×
            </Button>
          )}
        </CardBody>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardBody className="p-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-dark dark:text-white mb-2">
            Get Your Development Quote
          </h2>
          <p className="text-body-color dark:text-dark-6">
            Let's discuss your project! Choose how you'd like to connect with our team.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Full Name"
              placeholder="Enter your name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              isRequired
              variant="flat"
            />
            
            <Input
              label="Email Address"
              placeholder="Enter your email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              isRequired
              variant="flat"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Phone Number"
              placeholder="Enter your phone"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              variant="flat"
              description={formData.consultationType === 'phone' ? 'Required for phone calls' : 'Optional'}
              isRequired={formData.consultationType === 'phone'}
            />
            
            <Input
              label="Company Name"
              placeholder="Enter company name"
              value={formData.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
              variant="flat"
            />
          </div>

          {/* Service Interest */}
          <Select
            label="What type of development are you interested in?"
            placeholder="Select a service"
            selectedKeys={formData.serviceInterest ? [formData.serviceInterest] : []}
            onSelectionChange={(keys) => handleInputChange('serviceInterest', Array.from(keys)[0])}
            variant="flat"
          >
            {serviceOptions.map((service) => (
              <SelectItem key={service.key} value={service.key}>
                {service.label}
              </SelectItem>
            ))}
          </Select>

          {/* Consultation Type */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-dark dark:text-white">
              How would you like to connect?
            </h3>
            
            <RadioGroup
              value={formData.consultationType}
              onValueChange={(value) => handleInputChange('consultationType', value)}
              orientation="horizontal"
              className="gap-6"
            >
              <Radio value="video" description="Schedule a video call at your convenience">
                <div className="flex items-center gap-2">
                  <VideoCameraIcon className="h-5 w-5 text-primary" />
                  <span className="font-medium">Video Call</span>
                  <Chip size="sm" color="success" variant="flat">Recommended</Chip>
                </div>
              </Radio>
              <Radio value="phone" description="Get an immediate callback">
                <div className="flex items-center gap-2">
                  <PhoneIcon className="h-5 w-5 text-primary" />
                  <span className="font-medium">Phone Call</span>
                  <Chip size="sm" color="warning" variant="flat">Fast</Chip>
                </div>
              </Radio>
            </RadioGroup>
          </div>

          {/* Urgency Level */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-dark dark:text-white">
              When do you need this?
            </h3>
            <RadioGroup
              value={formData.urgency}
              onValueChange={(value) => handleInputChange('urgency', value)}
              orientation="horizontal"
              className="gap-4"
            >
              {urgencyOptions.map((option) => (
                <Radio key={option.key} value={option.key}>
                  <div className="flex items-center gap-2">
                    <ClockIcon className="h-4 w-4" />
                    <span>{option.label}</span>
                  </div>
                </Radio>
              ))}
            </RadioGroup>
          </div>

          {/* Phone Call Urgency Notice */}
          {formData.consultationType === 'phone' && (
            <Card className="border-primary/20 bg-primary/5">
              <CardBody className="p-4">
                <div className="flex items-center gap-2 text-primary">
                  <PhoneIcon className="h-5 w-5" />
                  <span className="font-medium">
                    We'll call you {getCallbackTimeEstimate(formData.urgency)}
                  </span>
                </div>
                <p className="text-sm text-body-color dark:text-dark-6 mt-1">
                  Make sure your phone is available and ready to discuss your project!
                </p>
              </CardBody>
            </Card>
          )}

          {/* Privacy Consent */}
          <div className="space-y-3">
            <Checkbox
              isSelected={formData.privacyConsent}
              onValueChange={(checked) => handleInputChange('privacyConsent', checked)}
              isRequired
            >
              <span className="text-sm">
                I agree to the <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a> and 
                consent to being contacted about my development needs.
              </span>
            </Checkbox>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            {onClose && (
              <Button
                variant="bordered"
                onPress={onClose}
                isDisabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
            
            <Button
              color="primary"
              type="submit"
              isLoading={isSubmitting}
              isDisabled={!validateForm()}
              size="lg"
              className="min-w-[200px]"
            >
              {formData.consultationType === 'video' 
                ? 'Schedule Video Call' 
                : 'Request Phone Call'
              }
            </Button>
          </div>
        </form>

        {onClose && (
          <Button
            variant="light"
            onPress={onClose}
            className="absolute top-4 right-4"
          >
            ×
          </Button>
        )}
      </CardBody>
    </Card>
  );
};

export default QuickQuoteForm;
