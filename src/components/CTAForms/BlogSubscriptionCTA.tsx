"use client";
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ontent, ModalBody, useDisclosure } from '@heroui/react';
import { NewspaperIcon } from '@heroicons/react/24/outline';
import BlogSubscriptionForm from './BlogSubscriptionForm';

interface BlogSubscriptionCTAProps {
  // Button appearance
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  
  // CTA behavior
  ctaText?: string;
  
  // Context
  source?: string;
  analyticsEvent?: string;
  
  // Customization
  icon?: React.ReactNode;
  showIcon?: boolean;
  fullWidth?: boolean;
  
  // Additional props
  [key: string]: any;
}

const BlogSubscriptionCTA: React.FC<BlogSubscriptionCTAProps> = ({
  variant = 'primary',
  size = 'md',
  className = '',
  ctaText = 'Subscribe to Updates',
  source = 'blog-subscription',
  analyticsEvent = 'blog_subscription_open',
  icon,
  showIcon = true,
  fullWidth = false,
  ...props
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  const handleClick = () => {
    // Track analytics event for modal open
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', analyticsEvent, {
        event_category: 'Blog Subscription',
        event_label: source,
        value: 1
      });
    }

    onOpen();
  };

  // Map variant to HeroUI button props
  const getButtonProps = () => {
    const baseProps = {
      size,
      className: `font-medium transition-all duration-200 ${fullWidth ? 'w-full' : ''} ${className}`,
      onClick: handleClick,
      ...props
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseProps,
          color: 'primary' as const,
          variant: 'solid' as const,
        };
      case 'secondary':
        return {
          ...baseProps,
          color: 'secondary' as const,
          variant: 'solid' as const,
        };
      case 'outline':
        return {
          ...baseProps,
          variant: 'bordered' as const,
          color: 'primary' as const,
        };
      case 'ghost':
        return {
          ...baseProps,
          variant: 'light' as const,
          color: 'primary' as const,
        };
      default:
        return {
          ...baseProps,
          color: 'primary' as const,
          variant: 'solid' as const,
        };
    }
  };

  const buttonProps = getButtonProps();

  return (
    <>
      <Button
        {...buttonProps}
        startContent={
          showIcon ? (
            icon || <NewspaperIcon className="h-4 w-4" />
          ) : undefined
        }
      >
        {ctaText}
      </Button>

      <Modal 
        isOpen={isOpen} 
        onClose={onClose}
        size="2xl"
        scrollBehavior="inside"
        classNames={{
          base: "bg-background",
          backdrop: "bg-black/50 backdrop-blur-sm",
          body: "p-0",
        }}
      >
        <ModalContent>
          <ModalBody className="p-6">
            <BlogSubscriptionForm 
              onClose={onClose}
              source={source}
              analyticsEvent={`${analyticsEvent}_submit`}
            />
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default BlogSubscriptionCTA;
