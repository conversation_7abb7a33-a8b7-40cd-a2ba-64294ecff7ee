"use client";
import React, { useState } from 'react';
import { 
  Card, 
  CardBody, 
  Input, 
  Textarea, 
  Button, 
  Select, 
  SelectItem, 
  Chip,
  Checkbox,
  RadioGroup,
  Radio
} from '@heroui/react';
import { toast } from 'react-hot-toast';

interface LeadCaptureFormProps {
  variant?: 'modal' | 'inline' | 'sidebar';
  serviceType?: string;
  source?: string;
  onSuccess?: (data: any) => void;
  onClose?: () => void;
}

interface FormData {
  // Contact Information
  name: string;
  email: string;
  phone: string;
  company: string;
  
  // Project Details
  serviceType: string;
  projectDescription: string;
  timeline: string;
  budgetRange: string;
  
  // Company Information
  companySize: string;
  industry: string;
  
  // Lead Context
  urgencyLevel: string;
  preferredContactMethod: string;
  additionalNotes: string;
  
  // Consent
  marketingConsent: boolean;
  privacyConsent: boolean;
}

const LeadCaptureForm: React.FC<LeadCaptureFormProps> = ({
  variant = 'inline',
  serviceType = '',
  source = 'website',
  onSuccess,
  onClose
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    serviceType: serviceType,
    projectDescription: '',
    timeline: '',
    budgetRange: '',
    companySize: '',
    industry: '',
    urgencyLevel: 'medium',
    preferredContactMethod: 'email',
    additionalNotes: '',
    marketingConsent: false,
    privacyConsent: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;

  const serviceTypes = [
    { key: 'custom-software', label: 'Custom Software Development' },
    { key: 'ai-ml', label: 'AI & Machine Learning' },
    { key: 'cybersecurity', label: 'Cybersecurity Solutions' },
    { key: 'cloud-devops', label: 'Cloud & DevOps' },
    { key: 'enterprise-consulting', label: 'Enterprise Consulting' },
    { key: 'general-inquiry', label: 'General Inquiry' }
  ];

  const timelines = [
    { key: 'immediate', label: 'Immediate (ASAP)' },
    { key: '1-month', label: 'Within 1 month' },
    { key: '3-months', label: 'Within 3 months' },
    { key: '6-months', label: 'Within 6 months' },
    { key: '1-year+', label: '1 year or more' }
  ];

  const budgetRanges = [
    { key: 'under-25k', label: 'Under $25K' },
    { key: '25k-50k', label: '$25K - $50K' },
    { key: '50k-100k', label: '$50K - $100K' },
    { key: '100k-250k', label: '$100K - $250K' },
    { key: '250k-500k', label: '$250K - $500K' },
    { key: '500k+', label: '$500K+' }
  ];

  const companySizes = [
    { key: 'startup', label: 'Startup (1-10 employees)' },
    { key: 'small', label: 'Small (11-50 employees)' },
    { key: 'medium', label: 'Medium (51-200 employees)' },
    { key: 'large', label: 'Large (201-1000 employees)' },
    { key: 'enterprise', label: 'Enterprise (1000+ employees)' }
  ];

  const industries = [
    { key: 'technology', label: 'Technology' },
    { key: 'healthcare', label: 'Healthcare' },
    { key: 'finance', label: 'Finance & Banking' },
    { key: 'retail', label: 'Retail & E-commerce' },
    { key: 'manufacturing', label: 'Manufacturing' },
    { key: 'education', label: 'Education' },
    { key: 'government', label: 'Government' },
    { key: 'other', label: 'Other' }
  ];

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.name && formData.email && formData.serviceType);
      case 2:
        return !!(formData.timeline && formData.budgetRange);
      case 3:
        return formData.privacyConsent;
      default:
        return true;
    }
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    } else {
      toast.error('Please fill in all required fields');
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateStep(currentStep)) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);

    try {
      const payload = {
        ...formData,
        leadSource: source,
        pageUrl: window.location.href,
        utmParams: {
          source: new URLSearchParams(window.location.search).get('utm_source'),
          medium: new URLSearchParams(window.location.search).get('utm_medium'),
          campaign: new URLSearchParams(window.location.search).get('utm_campaign'),
          term: new URLSearchParams(window.location.search).get('utm_term'),
          content: new URLSearchParams(window.location.search).get('utm_content')
        }
      };

      const response = await fetch('/api/n8n-webhooks/lead-capture', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Thank you! We\'ll be in touch soon.');
        onSuccess?.(result);
        
        // Reset form
        setFormData({
          name: '',
          email: '',
          phone: '',
          company: '',
          serviceType: serviceType,
          projectDescription: '',
          timeline: '',
          budgetRange: '',
          companySize: '',
          industry: '',
          urgencyLevel: 'medium',
          preferredContactMethod: 'email',
          additionalNotes: '',
          marketingConsent: false,
          privacyConsent: false
        });
        setCurrentStep(1);
      } else {
        toast.error(result.message || 'Something went wrong. Please try again.');
      }
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error('Network error. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStep1 = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">
        Contact Information
      </h3>
      
      <Input
        label="Full Name"
        placeholder="Enter your full name"
        value={formData.name}
        onChange={(e) => handleInputChange('name', e.target.value)}
        isRequired
        variant="flat"
      />
      
      <Input
        label="Email Address"
        placeholder="Enter your email"
        type="email"
        value={formData.email}
        onChange={(e) => handleInputChange('email', e.target.value)}
        isRequired
        variant="flat"
      />
      
      <Input
        label="Phone Number"
        placeholder="Enter your phone number"
        value={formData.phone}
        onChange={(e) => handleInputChange('phone', e.target.value)}
        variant="flat"
      />
      
      <Input
        label="Company Name"
        placeholder="Enter your company name"
        value={formData.company}
        onChange={(e) => handleInputChange('company', e.target.value)}
        variant="flat"
      />
      
      <Select
        label="Service Interest"
        placeholder="Select a service"
        selectedKeys={formData.serviceType ? [formData.serviceType] : []}
        onSelectionChange={(keys) => handleInputChange('serviceType', Array.from(keys)[0])}
        isRequired
        variant="flat"
      >
        {serviceTypes.map((service) => (
          <SelectItem key={service.key} value={service.key}>
            {service.label}
          </SelectItem>
        ))}
      </Select>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">
        Project Details
      </h3>
      
      <Textarea
        label="Project Description"
        placeholder="Briefly describe your project or requirements"
        value={formData.projectDescription}
        onChange={(e) => handleInputChange('projectDescription', e.target.value)}
        variant="flat"
        minRows={3}
      />
      
      <Select
        label="Timeline"
        placeholder="When do you need this completed?"
        selectedKeys={formData.timeline ? [formData.timeline] : []}
        onSelectionChange={(keys) => handleInputChange('timeline', Array.from(keys)[0])}
        isRequired
        variant="flat"
      >
        {timelines.map((timeline) => (
          <SelectItem key={timeline.key} value={timeline.key}>
            {timeline.label}
          </SelectItem>
        ))}
      </Select>
      
      <Select
        label="Budget Range"
        placeholder="What's your estimated budget?"
        selectedKeys={formData.budgetRange ? [formData.budgetRange] : []}
        onSelectionChange={(keys) => handleInputChange('budgetRange', Array.from(keys)[0])}
        isRequired
        variant="flat"
      >
        {budgetRanges.map((budget) => (
          <SelectItem key={budget.key} value={budget.key}>
            {budget.label}
          </SelectItem>
        ))}
      </Select>
      
      <Select
        label="Company Size"
        placeholder="How many employees?"
        selectedKeys={formData.companySize ? [formData.companySize] : []}
        onSelectionChange={(keys) => handleInputChange('companySize', Array.from(keys)[0])}
        variant="flat"
      >
        {companySizes.map((size) => (
          <SelectItem key={size.key} value={size.key}>
            {size.label}
          </SelectItem>
        ))}
      </Select>
      
      <Select
        label="Industry"
        placeholder="Select your industry"
        selectedKeys={formData.industry ? [formData.industry] : []}
        onSelectionChange={(keys) => handleInputChange('industry', Array.from(keys)[0])}
        variant="flat"
      >
        {industries.map((industry) => (
          <SelectItem key={industry.key} value={industry.key}>
            {industry.label}
          </SelectItem>
        ))}
      </Select>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">
        Preferences & Consent
      </h3>
      
      <RadioGroup
        label="Urgency Level"
        value={formData.urgencyLevel}
        onValueChange={(value) => handleInputChange('urgencyLevel', value)}
        orientation="horizontal"
      >
        <Radio value="low">Low</Radio>
        <Radio value="medium">Medium</Radio>
        <Radio value="high">High</Radio>
        <Radio value="urgent">Urgent</Radio>
      </RadioGroup>
      
      <RadioGroup
        label="Preferred Contact Method"
        value={formData.preferredContactMethod}
        onValueChange={(value) => handleInputChange('preferredContactMethod', value)}
        orientation="horizontal"
      >
        <Radio value="email">Email</Radio>
        <Radio value="phone">Phone</Radio>
        <Radio value="video_call">Video Call</Radio>
      </RadioGroup>
      
      <Textarea
        label="Additional Notes"
        placeholder="Any additional information you'd like to share?"
        value={formData.additionalNotes}
        onChange={(e) => handleInputChange('additionalNotes', e.target.value)}
        variant="flat"
        minRows={2}
      />
      
      <div className="space-y-3">
        <Checkbox
          isSelected={formData.privacyConsent}
          onValueChange={(checked) => handleInputChange('privacyConsent', checked)}
          isRequired
        >
          <span className="text-sm">
            I agree to the <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a> and 
            <a href="/terms" className="text-primary hover:underline ml-1">Terms of Service</a>
          </span>
        </Checkbox>
        
        <Checkbox
          isSelected={formData.marketingConsent}
          onValueChange={(checked) => handleInputChange('marketingConsent', checked)}
        >
          <span className="text-sm">
            I would like to receive marketing communications and updates about Digital Wave Systems services
          </span>
        </Checkbox>
      </div>
    </div>
  );

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardBody className="p-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-dark dark:text-white mb-2">
            Get Your Free Consultation
          </h2>
          <p className="text-body-color dark:text-dark-6">
            Tell us about your project and we'll provide a customized solution proposal.
          </p>
          
          {/* Progress indicator */}
          <div className="flex items-center mt-4 space-x-2">
            {Array.from({ length: totalSteps }, (_, i) => (
              <div key={i} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  i + 1 <= currentStep 
                    ? 'bg-primary text-white' 
                    : 'bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                }`}>
                  {i + 1}
                </div>
                {i < totalSteps - 1 && (
                  <div className={`w-8 h-1 mx-2 ${
                    i + 1 < currentStep ? 'bg-primary' : 'bg-gray-200 dark:bg-gray-700'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep3()}

          <div className="flex justify-between mt-6">
            {currentStep > 1 && (
              <Button
                variant="bordered"
                onPress={handlePrevious}
                isDisabled={isSubmitting}
              >
                Previous
              </Button>
            )}
            
            <div className="ml-auto">
              {currentStep < totalSteps ? (
                <Button
                  color="primary"
                  onPress={handleNext}
                  isDisabled={!validateStep(currentStep)}
                >
                  Next
                </Button>
              ) : (
                <Button
                  color="primary"
                  type="submit"
                  isLoading={isSubmitting}
                  isDisabled={!validateStep(currentStep)}
                >
                  Submit Request
                </Button>
              )}
            </div>
          </div>
        </form>

        {variant === 'modal' && onClose && (
          <Button
            variant="light"
            onPress={onClose}
            className="absolute top-4 right-4"
          >
            ×
          </Button>
        )}
      </CardBody>
    </Card>
  );
};

export default LeadCaptureForm;
