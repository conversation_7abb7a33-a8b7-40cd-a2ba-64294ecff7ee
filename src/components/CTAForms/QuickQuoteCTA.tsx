"use client";
import React, { useState } from 'react';
import { Button, Modal, ModalContent, ModalBody, useDisclosure } from '@heroui/react';
import { 
  DocumentTextIcon,
  PhoneIcon,
  VideoCameraIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import QuickQuoteForm from './QuickQuoteForm';

interface QuickQuoteCTAProps {
  // Button appearance
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  
  // CTA customization
  ctaText?: string;
  showIcon?: boolean;
  fullWidth?: boolean;
  
  // Context
  source?: string;
  
  // Callbacks
  onSuccess?: (data: any) => void;
  onClick?: () => void;
  
  // Analytics
  trackingId?: string;
  analyticsEvent?: string;
}

const QuickQuoteCTA: React.FC<QuickQuoteCTAProps> = ({
  variant = 'primary',
  size = 'md',
  className = '',
  ctaText = 'Get Development Quote',
  showIcon = true,
  fullWidth = false,
  source = 'quick-quote-cta',
  onSuccess,
  onClick,
  trackingId,
  analyticsEvent = 'quick_quote_clicked'
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = () => {
    // Track analytics
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', analyticsEvent, {
        event_category: 'CTA',
        event_label: 'quick_quote',
        custom_parameter_1: source,
        value: 1
      });
    }

    // Custom click handler
    if (onClick) {
      onClick();
      return;
    }

    // Open modal
    onOpen();
  };

  const handleFormSuccess = (data: any) => {
    onClose();
    onSuccess?.(data);
    
    // Track conversion
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'conversion', {
        event_category: 'Lead Generation',
        event_label: `quick_quote_${data.consultationType}`,
        value: 1
      });
    }
  };

  // Button variant mapping
  const getButtonProps = () => {
    const baseProps = {
      size,
      className: `${fullWidth ? 'w-full' : ''} ${className}`,
      onPress: handleClick,
      isLoading,
      startContent: showIcon ? <DocumentTextIcon className="h-5 w-5" /> : undefined
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseProps,
          color: 'primary' as const,
          variant: 'solid' as const
        };
      case 'secondary':
        return {
          ...baseProps,
          color: 'secondary' as const,
          variant: 'solid' as const
        };
      case 'outline':
        return {
          ...baseProps,
          color: 'primary' as const,
          variant: 'bordered' as const
        };
      case 'ghost':
        return {
          ...baseProps,
          color: 'primary' as const,
          variant: 'light' as const
        };
      default:
        return {
          ...baseProps,
          color: 'primary' as const,
          variant: 'solid' as const
        };
    }
  };

  return (
    <>
      <Button {...getButtonProps()}>
        {ctaText}
      </Button>

      <Modal 
        isOpen={isOpen} 
        onClose={onClose}
        size="2xl"
        scrollBehavior="inside"
        classNames={{
          base: "max-h-[95vh]",
          body: "p-0"
        }}
      >
        <ModalContent>
          <ModalBody>
            <QuickQuoteForm
              onSuccess={handleFormSuccess}
              onClose={onClose}
              source={source}
            />
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

// Preset variations for common use cases
export const GetQuoteButton: React.FC<Omit<QuickQuoteCTAProps, 'ctaText'>> = (props) => (
  <QuickQuoteCTA {...props} ctaText="Get Development Quote" />
);

export const TalkToExpertButton: React.FC<Omit<QuickQuoteCTAProps, 'ctaText' | 'showIcon'>> = (props) => (
  <QuickQuoteCTA 
    {...props} 
    ctaText="Talk to an Expert" 
    showIcon={true}
    analyticsEvent="talk_to_expert_clicked"
  />
);

export const StartProjectButton: React.FC<Omit<QuickQuoteCTAProps, 'ctaText'>> = (props) => (
  <QuickQuoteCTA 
    {...props} 
    ctaText="Start Your Project" 
    analyticsEvent="start_project_clicked"
  />
);

export const ScheduleCallButton: React.FC<Omit<QuickQuoteCTAProps, 'ctaText' | 'showIcon'>> = (props) => (
  <QuickQuoteCTA 
    {...props} 
    ctaText="Schedule a Call" 
    showIcon={true}
    analyticsEvent="schedule_call_clicked"
  />
);

// Urgency-focused variations
export const UrgentQuoteButton: React.FC<Omit<QuickQuoteCTAProps, 'ctaText' | 'variant'>> = (props) => (
  <QuickQuoteCTA 
    {...props} 
    ctaText="Get Urgent Quote" 
    variant="primary"
    className={`${props.className || ''} animate-pulse`}
    analyticsEvent="urgent_quote_clicked"
  />
);

export const CallNowButton: React.FC<Omit<QuickQuoteCTAProps, 'ctaText' | 'showIcon'>> = (props) => (
  <QuickQuoteCTA 
    {...props} 
    ctaText="Call Me Now" 
    showIcon={true}
    analyticsEvent="call_now_clicked"
  />
);

// Service-specific variations
export const CustomSoftwareQuoteButton: React.FC<Omit<QuickQuoteCTAProps, 'source'>> = (props) => (
  <QuickQuoteCTA 
    {...props} 
    source="custom-software-cta"
    analyticsEvent="custom_software_quote_clicked"
  />
);

export const AIMLQuoteButton: React.FC<Omit<QuickQuoteCTAProps, 'source'>> = (props) => (
  <QuickQuoteCTA 
    {...props} 
    source="ai-ml-cta"
    analyticsEvent="ai_ml_quote_clicked"
  />
);

export const MobileAppQuoteButton: React.FC<Omit<QuickQuoteCTAProps, 'source'>> = (props) => (
  <QuickQuoteCTA 
    {...props} 
    source="mobile-app-cta"
    analyticsEvent="mobile_app_quote_clicked"
  />
);

// Hero section specific button with enhanced styling (maintaining HeroUI design)
export const HeroQuoteButton: React.FC<Omit<QuickQuoteCTAProps, 'className' | 'size'>> = (props) => (
  <QuickQuoteCTA
    {...props}
    size="lg"
    className="bg-white text-primary hover:bg-blue-50 font-semibold px-8 py-3 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200"
    source="hero-section"
    analyticsEvent="hero_quote_clicked"
  />
);

// Services page specific button
export const ServicesQuoteButton: React.FC<Omit<QuickQuoteCTAProps, 'variant'>> = (props) => (
  <QuickQuoteCTA 
    {...props} 
    variant="outline"
    source="services-page"
    analyticsEvent="services_quote_clicked"
  />
);

export default QuickQuoteCTA;
