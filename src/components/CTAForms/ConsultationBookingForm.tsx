"use client";
import React, { useState } from 'react';
import { 
  Card, 
  CardBody, 
  Input, 
  Textarea, 
  Button, 
  Select, 
  SelectItem, 
  RadioGroup,
  Radio,
  Checkbox,
  DatePicker,
  TimeInput
} from '@heroui/react';
import { toast } from 'react-hot-toast';
import { CalendarIcon, ClockIcon, VideoIcon, PhoneIcon } from '@heroicons/react/24/outline';

interface ConsultationBookingFormProps {
  consultationType?: 'discovery' | 'technical-review' | 'strategy-session' | 'demo';
  serviceArea?: string;
  onSuccess?: (data: any) => void;
  onClose?: () => void;
}

interface BookingFormData {
  // Contact Information
  name: string;
  email: string;
  phone: string;
  company: string;
  jobTitle: string;
  
  // Consultation Details
  consultationType: string;
  serviceArea: string;
  preferredDate: string;
  preferredTime: string;
  timezone: string;
  duration: string;
  
  // Project Context
  projectDescription: string;
  currentChallenges: string;
  desiredOutcomes: string;
  timeline: string;
  budgetRange: string;
  
  // Meeting Preferences
  meetingType: string;
  meetingPlatform: string;
  
  // Additional Information
  additionalNotes: string;
  hearAboutUs: string;
  urgencyLevel: string;
  
  // Consent
  privacyConsent: boolean;
}

const ConsultationBookingForm: React.FC<ConsultationBookingFormProps> = ({
  consultationType = 'discovery',
  serviceArea = '',
  onSuccess,
  onClose
}) => {
  const [formData, setFormData] = useState<BookingFormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    jobTitle: '',
    consultationType: consultationType,
    serviceArea: serviceArea,
    preferredDate: '',
    preferredTime: '',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    duration: '30min',
    projectDescription: '',
    currentChallenges: '',
    desiredOutcomes: '',
    timeline: '',
    budgetRange: '',
    meetingType: 'video',
    meetingPlatform: 'zoom',
    additionalNotes: '',
    hearAboutUs: '',
    urgencyLevel: 'medium',
    privacyConsent: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const consultationTypes = [
    { key: 'discovery', label: 'Discovery Call (15-30 min)', description: 'Initial discussion about your needs' },
    { key: 'technical-review', label: 'Technical Review (45-60 min)', description: 'Deep dive into technical requirements' },
    { key: 'strategy-session', label: 'Strategy Session (60 min)', description: 'Comprehensive planning session' },
    { key: 'demo', label: 'Solution Demo (30-45 min)', description: 'See our solutions in action' }
  ];

  const serviceAreas = [
    { key: 'custom-software', label: 'Custom Software Development' },
    { key: 'ai-ml', label: 'AI & Machine Learning' },
    { key: 'cybersecurity', label: 'Cybersecurity Solutions' },
    { key: 'cloud-devops', label: 'Cloud & DevOps' },
    { key: 'enterprise-consulting', label: 'Enterprise Consulting' }
  ];

  const durations = [
    { key: '15min', label: '15 minutes' },
    { key: '30min', label: '30 minutes' },
    { key: '45min', label: '45 minutes' },
    { key: '60min', label: '60 minutes' }
  ];

  const meetingPlatforms = [
    { key: 'zoom', label: 'Zoom' },
    { key: 'teams', label: 'Microsoft Teams' },
    { key: 'google-meet', label: 'Google Meet' },
    { key: 'phone', label: 'Phone Call' }
  ];

  const timelines = [
    { key: 'immediate', label: 'Immediate (ASAP)' },
    { key: '1-month', label: 'Within 1 month' },
    { key: '3-months', label: 'Within 3 months' },
    { key: '6-months', label: 'Within 6 months' },
    { key: '1-year+', label: '1 year or more' }
  ];

  const budgetRanges = [
    { key: 'under-25k', label: 'Under $25K' },
    { key: '25k-50k', label: '$25K - $50K' },
    { key: '50k-100k', label: '$50K - $100K' },
    { key: '100k-250k', label: '$100K - $250K' },
    { key: '250k-500k', label: '$250K - $500K' },
    { key: '500k+', label: '$500K+' }
  ];

  const handleInputChange = (field: keyof BookingFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = (): boolean => {
    const requiredFields = ['name', 'email', 'consultationType', 'serviceArea', 'meetingType'];
    return requiredFields.every(field => formData[field as keyof BookingFormData]) && formData.privacyConsent;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fill in all required fields and accept the privacy policy');
      return;
    }

    setIsSubmitting(true);

    try {
      const payload = {
        ...formData,
        pageUrl: window.location.href,
        leadSource: 'consultation_booking_form'
      };

      const response = await fetch('/api/n8n-webhooks/consultation-booking', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Consultation request submitted successfully!');
        onSuccess?.(result);
        
        // Reset form
        setFormData({
          name: '',
          email: '',
          phone: '',
          company: '',
          jobTitle: '',
          consultationType: consultationType,
          serviceArea: serviceArea,
          preferredDate: '',
          preferredTime: '',
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          duration: '30min',
          projectDescription: '',
          currentChallenges: '',
          desiredOutcomes: '',
          timeline: '',
          budgetRange: '',
          meetingType: 'video',
          meetingPlatform: 'zoom',
          additionalNotes: '',
          hearAboutUs: '',
          urgencyLevel: 'medium',
          privacyConsent: false
        });
      } else {
        toast.error(result.message || 'Something went wrong. Please try again.');
      }
    } catch (error) {
      console.error('Booking submission error:', error);
      toast.error('Network error. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardBody className="p-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-dark dark:text-white mb-2">
            Book Your Consultation
          </h2>
          <p className="text-body-color dark:text-dark-6">
            Schedule a personalized consultation to discuss your project requirements and explore how we can help.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Full Name"
              placeholder="Enter your full name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              isRequired
              variant="flat"
            />
            
            <Input
              label="Email Address"
              placeholder="Enter your email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              isRequired
              variant="flat"
            />
            
            <Input
              label="Phone Number"
              placeholder="Enter your phone number"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              variant="flat"
            />
            
            <Input
              label="Company Name"
              placeholder="Enter your company name"
              value={formData.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
              variant="flat"
            />
          </div>

          <Input
            label="Job Title"
            placeholder="Enter your job title"
            value={formData.jobTitle}
            onChange={(e) => handleInputChange('jobTitle', e.target.value)}
            variant="flat"
          />

          {/* Consultation Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Select
              label="Consultation Type"
              placeholder="Select consultation type"
              selectedKeys={formData.consultationType ? [formData.consultationType] : []}
              onSelectionChange={(keys) => handleInputChange('consultationType', Array.from(keys)[0])}
              isRequired
              variant="flat"
            >
              {consultationTypes.map((type) => (
                <SelectItem key={type.key} value={type.key} description={type.description}>
                  {type.label}
                </SelectItem>
              ))}
            </Select>
            
            <Select
              label="Service Area"
              placeholder="Select service area"
              selectedKeys={formData.serviceArea ? [formData.serviceArea] : []}
              onSelectionChange={(keys) => handleInputChange('serviceArea', Array.from(keys)[0])}
              isRequired
              variant="flat"
            >
              {serviceAreas.map((service) => (
                <SelectItem key={service.key} value={service.key}>
                  {service.label}
                </SelectItem>
              ))}
            </Select>
          </div>

          {/* Meeting Preferences */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-dark dark:text-white">
              Meeting Preferences
            </h3>
            
            <RadioGroup
              label="Meeting Type"
              value={formData.meetingType}
              onValueChange={(value) => handleInputChange('meetingType', value)}
              orientation="horizontal"
              isRequired
            >
              <Radio value="video" description="Video conference call">
                <div className="flex items-center gap-2">
                  <VideoIcon className="h-4 w-4" />
                  Video Call
                </div>
              </Radio>
              <Radio value="phone" description="Traditional phone call">
                <div className="flex items-center gap-2">
                  <PhoneIcon className="h-4 w-4" />
                  Phone Call
                </div>
              </Radio>
              <Radio value="in-person" description="Face-to-face meeting">
                <div className="flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4" />
                  In Person
                </div>
              </Radio>
            </RadioGroup>

            {formData.meetingType === 'video' && (
              <Select
                label="Preferred Platform"
                placeholder="Select video platform"
                selectedKeys={formData.meetingPlatform ? [formData.meetingPlatform] : []}
                onSelectionChange={(keys) => handleInputChange('meetingPlatform', Array.from(keys)[0])}
                variant="flat"
              >
                {meetingPlatforms.map((platform) => (
                  <SelectItem key={platform.key} value={platform.key}>
                    {platform.label}
                  </SelectItem>
                ))}
              </Select>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                label="Preferred Date"
                placeholder="YYYY-MM-DD"
                type="date"
                value={formData.preferredDate}
                onChange={(e) => handleInputChange('preferredDate', e.target.value)}
                variant="flat"
                startContent={<CalendarIcon className="h-4 w-4 text-gray-400" />}
              />
              
              <Input
                label="Preferred Time"
                placeholder="HH:MM"
                type="time"
                value={formData.preferredTime}
                onChange={(e) => handleInputChange('preferredTime', e.target.value)}
                variant="flat"
                startContent={<ClockIcon className="h-4 w-4 text-gray-400" />}
              />
              
              <Select
                label="Duration"
                placeholder="Select duration"
                selectedKeys={formData.duration ? [formData.duration] : []}
                onSelectionChange={(keys) => handleInputChange('duration', Array.from(keys)[0])}
                variant="flat"
              >
                {durations.map((duration) => (
                  <SelectItem key={duration.key} value={duration.key}>
                    {duration.label}
                  </SelectItem>
                ))}
              </Select>
            </div>
          </div>

          {/* Project Context */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-dark dark:text-white">
              Project Context
            </h3>
            
            <Textarea
              label="Project Description"
              placeholder="Briefly describe your project or requirements"
              value={formData.projectDescription}
              onChange={(e) => handleInputChange('projectDescription', e.target.value)}
              variant="flat"
              minRows={3}
            />
            
            <Textarea
              label="Current Challenges"
              placeholder="What challenges are you facing that we can help solve?"
              value={formData.currentChallenges}
              onChange={(e) => handleInputChange('currentChallenges', e.target.value)}
              variant="flat"
              minRows={2}
            />
            
            <Textarea
              label="Desired Outcomes"
              placeholder="What outcomes are you hoping to achieve?"
              value={formData.desiredOutcomes}
              onChange={(e) => handleInputChange('desiredOutcomes', e.target.value)}
              variant="flat"
              minRows={2}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Timeline"
                placeholder="When do you need this completed?"
                selectedKeys={formData.timeline ? [formData.timeline] : []}
                onSelectionChange={(keys) => handleInputChange('timeline', Array.from(keys)[0])}
                variant="flat"
              >
                {timelines.map((timeline) => (
                  <SelectItem key={timeline.key} value={timeline.key}>
                    {timeline.label}
                  </SelectItem>
                ))}
              </Select>
              
              <Select
                label="Budget Range"
                placeholder="What's your estimated budget?"
                selectedKeys={formData.budgetRange ? [formData.budgetRange] : []}
                onSelectionChange={(keys) => handleInputChange('budgetRange', Array.from(keys)[0])}
                variant="flat"
              >
                {budgetRanges.map((budget) => (
                  <SelectItem key={budget.key} value={budget.key}>
                    {budget.label}
                  </SelectItem>
                ))}
              </Select>
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <RadioGroup
              label="Urgency Level"
              value={formData.urgencyLevel}
              onValueChange={(value) => handleInputChange('urgencyLevel', value)}
              orientation="horizontal"
            >
              <Radio value="low">Low</Radio>
              <Radio value="medium">Medium</Radio>
              <Radio value="high">High</Radio>
              <Radio value="urgent">Urgent</Radio>
            </RadioGroup>

            <Textarea
              label="Additional Notes"
              placeholder="Any additional information you'd like to share before our consultation?"
              value={formData.additionalNotes}
              onChange={(e) => handleInputChange('additionalNotes', e.target.value)}
              variant="flat"
              minRows={2}
            />

            <Input
              label="How did you hear about us?"
              placeholder="e.g., Google search, referral, social media"
              value={formData.hearAboutUs}
              onChange={(e) => handleInputChange('hearAboutUs', e.target.value)}
              variant="flat"
            />
          </div>

          {/* Consent */}
          <div className="space-y-3">
            <Checkbox
              isSelected={formData.privacyConsent}
              onValueChange={(checked) => handleInputChange('privacyConsent', checked)}
              isRequired
            >
              <span className="text-sm">
                I agree to the <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a> and 
                <a href="/terms" className="text-primary hover:underline ml-1">Terms of Service</a>
              </span>
            </Checkbox>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            {onClose && (
              <Button
                variant="bordered"
                onPress={onClose}
                isDisabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
            
            <Button
              color="primary"
              type="submit"
              isLoading={isSubmitting}
              isDisabled={!validateForm()}
              size="lg"
            >
              Book Consultation
            </Button>
          </div>
        </form>
      </CardBody>
    </Card>
  );
};

export default ConsultationBookingForm;
