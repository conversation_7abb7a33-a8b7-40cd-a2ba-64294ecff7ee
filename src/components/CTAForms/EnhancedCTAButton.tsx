"use client";
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalBody, useDisclosure } from '@heroui/react';
import { 
  CalendarIcon, 
  DocumentTextIcon, 
  ChatBubbleLeftRightIcon,
  CpuChipIcon,
  ShieldCheckIcon,
  CloudIcon,
  BuildingOfficeIcon,
  CodeBracketIcon
} from '@heroicons/react/24/outline';
import LeadCaptureForm from './LeadCaptureForm';
import ConsultationBookingForm from './ConsultationBookingForm';

interface EnhancedCTAButtonProps {
  // Button appearance
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  
  // CTA behavior
  ctaType: 'lead-capture' | 'consultation' | 'estimation' | 'demo';
  ctaText?: string;
  
  // Context for form pre-filling
  serviceType?: string;
  consultationType?: 'discovery' | 'technical-review' | 'strategy-session' | 'demo';
  source?: string;
  
  // Customization
  icon?: React.ReactNode;
  showIcon?: boolean;
  fullWidth?: boolean;
  
  // Callbacks
  onSuccess?: (data: any) => void;
  onClick?: () => void;
  
  // Analytics
  trackingId?: string;
  analyticsEvent?: string;
}

const EnhancedCTAButton: React.FC<EnhancedCTAButtonProps> = ({
  variant = 'primary',
  size = 'md',
  className = '',
  ctaType,
  ctaText,
  serviceType = '',
  consultationType = 'discovery',
  source = 'cta-button',
  icon,
  showIcon = true,
  fullWidth = false,
  onSuccess,
  onClick,
  trackingId,
  analyticsEvent
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [isLoading, setIsLoading] = useState(false);

  // Default CTA configurations
  const ctaConfigs = {
    'lead-capture': {
      text: 'Get Free Consultation',
      icon: <ChatBubbleLeftRightIcon className="h-5 w-5" />,
      modalTitle: 'Request Free Consultation',
      description: 'Tell us about your project and get expert advice'
    },
    'consultation': {
      text: 'Book Consultation',
      icon: <CalendarIcon className="h-5 w-5" />,
      modalTitle: 'Schedule Your Consultation',
      description: 'Choose a time that works for you'
    },
    'estimation': {
      text: 'Get Project Estimate',
      icon: <DocumentTextIcon className="h-5 w-5" />,
      modalTitle: 'Project Estimation Request',
      description: 'Get a detailed estimate for your project'
    },
    'demo': {
      text: 'Request Demo',
      icon: <CpuChipIcon className="h-5 w-5" />,
      modalTitle: 'Schedule Product Demo',
      description: 'See our solutions in action'
    }
  };

  // Service-specific icons
  const serviceIcons = {
    'custom-software': <CodeBracketIcon className="h-4 w-4" />,
    'ai-ml': <CpuChipIcon className="h-4 w-4" />,
    'cybersecurity': <ShieldCheckIcon className="h-4 w-4" />,
    'cloud-devops': <CloudIcon className="h-4 w-4" />,
    'enterprise-consulting': <BuildingOfficeIcon className="h-4 w-4" />
  };

  const config = ctaConfigs[ctaType];
  const buttonText = ctaText || config.text;
  const buttonIcon = icon || (showIcon ? config.icon : null);

  const handleClick = () => {
    // Track analytics if configured
    if (analyticsEvent && typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', analyticsEvent, {
        event_category: 'CTA',
        event_label: ctaType,
        custom_parameter_1: serviceType,
        custom_parameter_2: source
      });
    }

    // Custom click handler
    if (onClick) {
      onClick();
      return;
    }

    // Open modal for form
    onOpen();
  };

  const handleFormSuccess = (data: any) => {
    onClose();
    onSuccess?.(data);
    
    // Track conversion
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'conversion', {
        event_category: 'CTA',
        event_label: `${ctaType}_success`,
        value: 1
      });
    }
  };

  const renderForm = () => {
    switch (ctaType) {
      case 'lead-capture':
        return (
          <LeadCaptureForm
            variant="modal"
            serviceType={serviceType}
            source={source}
            onSuccess={handleFormSuccess}
            onClose={onClose}
          />
        );
      
      case 'consultation':
      case 'demo':
        return (
          <ConsultationBookingForm
            consultationType={ctaType === 'demo' ? 'demo' : consultationType}
            serviceArea={serviceType}
            onSuccess={handleFormSuccess}
            onClose={onClose}
          />
        );
      
      case 'estimation':
        // For now, redirect to lead capture with estimation context
        return (
          <LeadCaptureForm
            variant="modal"
            serviceType={serviceType}
            source={`${source}_estimation`}
            onSuccess={handleFormSuccess}
            onClose={onClose}
          />
        );
      
      default:
        return null;
    }
  };

  // Button variant mapping
  const getButtonProps = () => {
    const baseProps = {
      size,
      className: `${fullWidth ? 'w-full' : ''} ${className}`,
      onPress: handleClick,
      isLoading,
      startContent: buttonIcon,
      endContent: serviceType && serviceIcons[serviceType as keyof typeof serviceIcons]
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseProps,
          color: 'primary' as const,
          variant: 'solid' as const
        };
      case 'secondary':
        return {
          ...baseProps,
          color: 'secondary' as const,
          variant: 'solid' as const
        };
      case 'outline':
        return {
          ...baseProps,
          color: 'primary' as const,
          variant: 'bordered' as const
        };
      case 'ghost':
        return {
          ...baseProps,
          color: 'primary' as const,
          variant: 'light' as const
        };
      default:
        return {
          ...baseProps,
          color: 'primary' as const,
          variant: 'solid' as const
        };
    }
  };

  return (
    <>
      <Button {...getButtonProps()}>
        {buttonText}
      </Button>

      <Modal 
        isOpen={isOpen} 
        onClose={onClose}
        size="2xl"
        scrollBehavior="inside"
        classNames={{
          base: "max-h-[90vh]",
          body: "p-0"
        }}
      >
        <ModalContent>
          <ModalBody>
            {renderForm()}
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

// Preset CTA components for common use cases
export const GetConsultationCTA: React.FC<Omit<EnhancedCTAButtonProps, 'ctaType'>> = (props) => (
  <EnhancedCTAButton {...props} ctaType="lead-capture" />
);

export const BookConsultationCTA: React.FC<Omit<EnhancedCTAButtonProps, 'ctaType'>> = (props) => (
  <EnhancedCTAButton {...props} ctaType="consultation" />
);

export const GetEstimateCTA: React.FC<Omit<EnhancedCTAButtonProps, 'ctaType'>> = (props) => (
  <EnhancedCTAButton {...props} ctaType="estimation" />
);

export const RequestDemoCTA: React.FC<Omit<EnhancedCTAButtonProps, 'ctaType'>> = (props) => (
  <EnhancedCTAButton {...props} ctaType="demo" />
);

// Service-specific CTA components
export const CustomSoftwareCTA: React.FC<Omit<EnhancedCTAButtonProps, 'serviceType'>> = (props) => (
  <EnhancedCTAButton {...props} serviceType="custom-software" />
);

export const AIMachineLearningCTA: React.FC<Omit<EnhancedCTAButtonProps, 'serviceType'>> = (props) => (
  <EnhancedCTAButton {...props} serviceType="ai-ml" />
);

export const CybersecurityCTA: React.FC<Omit<EnhancedCTAButtonProps, 'serviceType'>> = (props) => (
  <EnhancedCTAButton {...props} serviceType="cybersecurity" />
);

export const CloudDevOpsCTA: React.FC<Omit<EnhancedCTAButtonProps, 'serviceType'>> = (props) => (
  <EnhancedCTAButton {...props} serviceType="cloud-devops" />
);

export const EnterpriseConsultingCTA: React.FC<Omit<EnhancedCTAButtonProps, 'serviceType'>> = (props) => (
  <EnhancedCTAButton {...props} serviceType="enterprise-consulting" />
);

export default EnhancedCTAButton;
