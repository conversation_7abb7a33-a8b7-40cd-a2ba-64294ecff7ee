import { Price } from "@/types/price";

export const pricingData: Price[] = [
  {
    id: "price_1NQk5TLtGdPVhGLecVfQ7mn0",
    unit_amount: 15,
    nickname: "Basic",
    offers: [
      "1 service",
      "Unlimited use",
      "Support ",
      "Comercial and personal use",
    ],
  },
  {
    id: "price_1NQk55LtGdPVhGLefU8AHqHr",
    unit_amount: 25,
    nickname: "Premium",
    offers: [
      "3 services",
      "Unlimited use",
      "Support ",
      "Comercial and personal use",
    ],
  },
  {
    id: "price_1NQk4eLtGdPVhGLeZsZDsCNz",
    unit_amount: 35,
    nickname: "Business",
    offers: [
      "All services",
      "Unlimited use",
      "Support ",
      "Comercial and personal use",
    ],
  },
];
