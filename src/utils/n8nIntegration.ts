// Utility functions for n8n integration

export interface N8nWebhookConfig {
  url: string;
  secret?: string;
  timeout?: number;
  retries?: number;
}

export interface N8nResponse {
  success: boolean;
  data?: any;
  error?: string;
}

// Send data to n8n webhook with retry logic
export async function sendToN8nWebhook(
  config: N8nWebhookConfig,
  payload: any,
  options: {
    retries?: number;
    timeout?: number;
  } = {}
): Promise<N8nResponse> {
  const maxRetries = options.retries || config.retries || 3;
  const timeout = options.timeout || config.timeout || 10000;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      const response = await fetch(config.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(config.secret && { 'Authorization': `Bearer ${config.secret}` })
        },
        body: JSON.stringify(payload),
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok) {
        const data = await response.json();
        return { success: true, data };
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error(`n8n webhook attempt ${attempt} failed:`, error);
      
      if (attempt === maxRetries) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
      
      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
  
  return { success: false, error: 'Max retries exceeded' };
}

// Validate webhook payload structure
export function validateWebhookPayload(payload: any, requiredFields: string[]): {
  isValid: boolean;
  missingFields: string[];
} {
  const missingFields: string[] = [];
  
  for (const field of requiredFields) {
    if (!(field in payload) || payload[field] === null || payload[field] === undefined) {
      missingFields.push(field);
    }
  }
  
  return {
    isValid: missingFields.length === 0,
    missingFields
  };
}

// Generate unique tracking ID
export function generateTrackingId(prefix: string = 'track'): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}_${timestamp}_${random}`;
}

// Sanitize data for n8n webhook
export function sanitizePayload(payload: any): any {
  const sanitized = { ...payload };
  
  // Remove sensitive fields
  const sensitiveFields = ['password', 'token', 'secret', 'key'];
  sensitiveFields.forEach(field => {
    if (field in sanitized) {
      delete sanitized[field];
    }
  });
  
  // Trim string values
  Object.keys(sanitized).forEach(key => {
    if (typeof sanitized[key] === 'string') {
      sanitized[key] = sanitized[key].trim();
    }
  });
  
  return sanitized;
}

// Get n8n webhook URLs from environment variables
export function getN8nWebhookUrls(): {
  leadCapture?: string;
  consultation?: string;
  estimation?: string;
  payment?: string;
  support?: string;
} {
  return {
    leadCapture: process.env.N8N_LEAD_CAPTURE_WEBHOOK_URL,
    consultation: process.env.N8N_CONSULTATION_WEBHOOK_URL,
    estimation: process.env.N8N_ESTIMATION_WEBHOOK_URL,
    payment: process.env.N8N_PAYMENT_WEBHOOK_URL,
    support: process.env.N8N_SUPPORT_WEBHOOK_URL
  };
}

// Create standardized n8n payload
export function createN8nPayload(
  type: 'lead' | 'consultation' | 'estimation' | 'payment' | 'support',
  data: any,
  metadata: {
    source?: string;
    userId?: string;
    sessionId?: string;
    pageUrl?: string;
    userAgent?: string;
    ipAddress?: string;
  } = {}
): any {
  const basePayload = {
    type,
    timestamp: new Date().toISOString(),
    trackingId: generateTrackingId(type),
    source: metadata.source || 'website',
    metadata: {
      userAgent: metadata.userAgent,
      ipAddress: metadata.ipAddress,
      pageUrl: metadata.pageUrl,
      sessionId: metadata.sessionId,
      userId: metadata.userId
    },
    data: sanitizePayload(data)
  };
  
  return basePayload;
}

// Log n8n webhook activity
export function logWebhookActivity(
  type: string,
  success: boolean,
  payload: any,
  response?: any,
  error?: string
): void {
  const logEntry = {
    timestamp: new Date().toISOString(),
    type,
    success,
    payloadSize: JSON.stringify(payload).length,
    response: success ? 'success' : error,
    trackingId: payload.trackingId
  };
  
  // In production, you might want to send this to a logging service
  console.log('n8n Webhook Activity:', logEntry);
}

// Retry configuration for different webhook types
export const WEBHOOK_RETRY_CONFIG: { [key: string]: { retries: number; timeout: number } } = {
  lead: { retries: 3, timeout: 10000 },
  consultation: { retries: 3, timeout: 15000 },
  estimation: { retries: 2, timeout: 20000 },
  payment: { retries: 5, timeout: 30000 },
  support: { retries: 3, timeout: 10000 }
};

// Helper function to send webhook with proper configuration
export async function sendWebhookByType(
  type: 'lead' | 'consultation' | 'estimation' | 'payment' | 'support',
  data: any,
  metadata: any = {}
): Promise<N8nResponse> {
  const webhookUrls = getN8nWebhookUrls();
  const webhookUrl = webhookUrls[type === 'lead' ? 'leadCapture' : type];
  
  if (!webhookUrl) {
    console.warn(`No webhook URL configured for type: ${type}`);
    return { success: false, error: 'Webhook URL not configured' };
  }
  
  const config: N8nWebhookConfig = {
    url: webhookUrl,
    secret: process.env.N8N_WEBHOOK_SECRET,
    ...WEBHOOK_RETRY_CONFIG[type]
  };
  
  const payload = createN8nPayload(type, data, metadata);
  
  const result = await sendToN8nWebhook(config, payload);
  
  // Log the activity
  logWebhookActivity(type, result.success, payload, result.data, result.error);
  
  return result;
}

// Environment variable validation
export function validateN8nEnvironment(): {
  isValid: boolean;
  missingVars: string[];
  warnings: string[];
} {
  const requiredVars = ['N8N_WEBHOOK_SECRET'];
  const optionalVars = [
    'N8N_LEAD_CAPTURE_WEBHOOK_URL',
    'N8N_CONSULTATION_WEBHOOK_URL',
    'N8N_ESTIMATION_WEBHOOK_URL',
    'N8N_PAYMENT_WEBHOOK_URL',
    'N8N_SUPPORT_WEBHOOK_URL'
  ];
  
  const missingVars: string[] = [];
  const warnings: string[] = [];
  
  // Check required variables
  requiredVars.forEach(varName => {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  });
  
  // Check optional variables
  optionalVars.forEach(varName => {
    if (!process.env[varName]) {
      warnings.push(`Optional environment variable ${varName} is not set`);
    }
  });
  
  return {
    isValid: missingVars.length === 0,
    missingVars,
    warnings
  };
}

// Rate limiting helper (simple in-memory implementation)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  identifier: string,
  maxRequests: number = 10,
  windowMs: number = 60000
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const key = identifier;
  
  const current = rateLimitMap.get(key);
  
  if (!current || now > current.resetTime) {
    // Reset or initialize
    const resetTime = now + windowMs;
    rateLimitMap.set(key, { count: 1, resetTime });
    return { allowed: true, remaining: maxRequests - 1, resetTime };
  }
  
  if (current.count >= maxRequests) {
    return { allowed: false, remaining: 0, resetTime: current.resetTime };
  }
  
  current.count++;
  rateLimitMap.set(key, current);
  
  return { allowed: true, remaining: maxRequests - current.count, resetTime: current.resetTime };
}
