import { Metadata } from 'next';

// SEO Keywords for software consulting/development/AI B2B industry
export const SEO_KEYWORDS = {
  primary: [
    'software consulting',
    'custom software development',
    'enterprise software solutions',
    'software development company',
    'digital transformation',
    'AI integration services',
    'machine learning consulting',
    'cloud architecture',
    'DevOps consulting',
    'cybersecurity solutions'
  ],
  secondary: [
    'full-stack development',
    'web application development',
    'mobile app development',
    'API development',
    'microservices architecture',
    'legacy system modernization',
    'software architecture design',
    'agile development',
    'enterprise consulting',
    'technology roadmap'
  ],
  location: [
    'Colombia',
    'Latin America',
    'South America',
    'remote software development',
    'nearshore development'
  ]
};

// Generate comprehensive meta description
export const generateMetaDescription = (
  service: string,
  benefits: string[],
  location?: string
): string => {
  const locationText = location ? ` in ${location}` : '';
  const benefitsText = benefits.slice(0, 2).join(' and ');
  
  return `Professional ${service} services${locationText}. ${benefitsText}. Expert software consulting with proven results for enterprise clients.`;
};

// Generate SEO-optimized title
export const generateSEOTitle = (
  pageTitle: string,
  service?: string,
  companyName: string = 'Digital Wave Systems'
): string => {
  const serviceText = service ? ` | ${service}` : '';
  return `${pageTitle}${serviceText} | ${companyName}`;
};

// Generate keywords string
export const generateKeywords = (
  pageType: 'home' | 'services' | 'blog' | 'about' | 'contact',
  specificKeywords: string[] = []
): string => {
  const baseKeywords = SEO_KEYWORDS.primary.slice(0, 5);
  const typeKeywords = pageType === 'services' 
    ? SEO_KEYWORDS.secondary.slice(0, 5)
    : SEO_KEYWORDS.primary.slice(5);
  
  return [...baseKeywords, ...typeKeywords, ...specificKeywords].join(', ');
};

// Structured data for organization
export const getOrganizationStructuredData = () => ({
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Digital Wave Systems",
  "alternateName": "DWS",
  "url": "https://digitalwavesystems.com.co",
  "logo": "https://digitalwavesystems.com.co/images/logo/Logo.svg",
  "description": "Leading software development company delivering custom software solutions, enterprise applications, and AI integration services.",
  "foundingDate": "2020",
  "address": {
    "@type": "PostalAddress",
    "addressCountry": "CO",
    "addressRegion": "Colombia"
  },
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "+57-XXX-XXX-XXXX",
    "contactType": "customer service",
    "availableLanguage": ["English", "Spanish"]
  },
  "sameAs": [
    "https://linkedin.com/company/digitalwavesystems",
    "https://github.com/digitalwavesystems"
  ],
  "serviceArea": {
    "@type": "Place",
    "name": "Global"
  },
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Software Development Services",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Custom Software Development",
          "description": "End-to-end custom software solutions tailored to business needs"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "AI Integration Services",
          "description": "Machine learning and artificial intelligence integration for business automation"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Cloud Architecture & DevOps",
          "description": "Scalable cloud solutions and DevOps practices for optimal infrastructure"
        }
      }
    ]
  }
});

// Structured data for services
export const getServiceStructuredData = (
  serviceName: string,
  description: string,
  features: string[]
) => ({
  "@context": "https://schema.org",
  "@type": "Service",
  "name": serviceName,
  "description": description,
  "provider": {
    "@type": "Organization",
    "name": "Digital Wave Systems",
    "url": "https://digitalwavesystems.com.co"
  },
  "serviceType": "Software Development",
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": serviceName,
    "itemListElement": features.map((feature, index) => ({
      "@type": "Offer",
      "itemOffered": {
        "@type": "Service",
        "name": feature,
        "position": index + 1
      }
    }))
  },
  "areaServed": {
    "@type": "Place",
    "name": "Global"
  },
  "availableChannel": {
    "@type": "ServiceChannel",
    "serviceUrl": "https://digitalwavesystems.com.co/contact",
    "serviceSmsNumber": "+57-XXX-XXX-XXXX"
  }
});

// Blog post structured data
export const getBlogPostStructuredData = (
  title: string,
  description: string,
  author: string,
  publishDate: string,
  imageUrl?: string
) => ({
  "@context": "https://schema.org",
  "@type": "BlogPosting",
  "headline": title,
  "description": description,
  "author": {
    "@type": "Person",
    "name": author
  },
  "publisher": {
    "@type": "Organization",
    "name": "Digital Wave Systems",
    "logo": {
      "@type": "ImageObject",
      "url": "https://digitalwavesystems.com.co/images/logo/Logo.svg"
    }
  },
  "datePublished": publishDate,
  "dateModified": publishDate,
  "image": imageUrl ?? "https://digitalwavesystems.com.co/images/logo/Logo.svg",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://digitalwavesystems.com.co"
  }
});

// Generate comprehensive metadata with i18n support
export const generatePageMetadata = (
  title: string,
  description: string,
  keywords: string,
  canonicalUrl: string,
  imageUrl?: string,
  type: 'website' | 'article' = 'website',
  locale: 'en' | 'es' = 'en'
): Metadata => {
  const baseUrl = 'https://digitalwavesystems.com.co';
  const currentPath = canonicalUrl.replace(baseUrl, '');

  // Generate hreflang alternates
  const alternates: Record<string, string> = {
    canonical: canonicalUrl,
  };

  // Add language alternates
  const languages = {
    'en': currentPath.startsWith('/es/') ? currentPath.replace('/es/', '/') : currentPath,
    'es': currentPath.startsWith('/es/') ? currentPath : `/es${currentPath}`,
    'x-default': currentPath.startsWith('/es/') ? currentPath.replace('/es/', '/') : currentPath
  };

  Object.entries(languages).forEach(([lang, path]) => {
    const cleanPath = path === '/' ? '' : path;
    alternates[lang] = `${baseUrl}${cleanPath}`;
  });

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type,
      url: canonicalUrl,
      siteName: 'Digital Wave Systems',
      images: [
        {
          url: imageUrl ?? '/images/logo/Logo.svg',
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: locale === 'es' ? 'es_ES' : 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [imageUrl ?? '/images/logo/Logo.svg'],
      creator: '@digitalwavesys',
      site: '@digitalwavesys',
    },
    alternates,
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
      // Prevent duplicate content issues between language versions
      noarchive: false,
      nosnippet: false,
      noimageindex: false,
      nocache: false,
    },
    verification: {
      google: 'your-google-verification-code',
    },
    category: 'Technology',
    // Add language information
    other: {
      'content-language': locale === 'es' ? 'es-ES' : 'en-US',
    },
  };
};

// Generate metadata specifically for Spanish pages
export const generateSpanishPageMetadata = (
  title: string,
  description: string,
  keywords: string,
  canonicalUrl: string,
  imageUrl?: string,
  type: 'website' | 'article' = 'website'
): Metadata => {
  return generatePageMetadata(title, description, keywords, canonicalUrl, imageUrl, type, 'es');
};

// Generate robots meta tag for specific indexing control
export const generateRobotsMetaTag = (
  index: boolean = true,
  follow: boolean = true,
  noarchive: boolean = false,
  nosnippet: boolean = false,
  noimageindex: boolean = false
): string => {
  const directives: string[] = [];

  directives.push(index ? 'index' : 'noindex');
  directives.push(follow ? 'follow' : 'nofollow');

  if (noarchive) directives.push('noarchive');
  if (nosnippet) directives.push('nosnippet');
  if (noimageindex) directives.push('noimageindex');

  return directives.join(', ');
};

// Generate hreflang links for manual implementation
export const generateHreflangLinks = (currentPath: string): Array<{rel: string, hreflang: string, href: string}> => {
  const baseUrl = 'https://digitalwavesystems.com.co';
  const cleanPath = currentPath === '/' ? '' : currentPath;

  const links = [
    {
      rel: 'alternate',
      hreflang: 'en',
      href: `${baseUrl}${cleanPath.startsWith('/es/') ? cleanPath.replace('/es/', '/') : cleanPath}`
    },
    {
      rel: 'alternate',
      hreflang: 'es',
      href: `${baseUrl}${cleanPath.startsWith('/es/') ? cleanPath : '/es' + cleanPath}`
    },
    {
      rel: 'alternate',
      hreflang: 'x-default',
      href: `${baseUrl}${cleanPath.startsWith('/es/') ? cleanPath.replace('/es/', '/') : cleanPath}`
    }
  ];

  return links;
};
