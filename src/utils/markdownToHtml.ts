import { remark } from "remark";
import remarkRehype from "remark-rehype";
import rehypeStringify from "rehype-stringify";
import rehypePrism from "rehype-prism-plus";

export default async function markdownToHtml(markdown: string) {
  const result = await remark()
    .use(remarkRehype, { allowDangerousHtml: true })
    .use(rehypePrism, {
      showLineNumbers: true,
      ignoreMissing: true,
      alias: {
        js: ["javascript"],
        sh: ["bash", "shell"],
        ts: ["typescript"],
        jsx: ["javascript"],
        tsx: ["typescript"]
      }
    })
    .use(rehypeStringify, { allowDangerousHtml: true })
    .process(markdown);

  // Enhanced HTML processing with professional HeroUI styling
  let html = result.toString();

  // Add professional HeroUI classes to elements
  html = html
    // Headings with proper hierarchy and spacing
    .replace(/<h1>/g, '<h1 class="text-4xl lg:text-5xl font-bold text-foreground mb-8 mt-12 leading-tight">')
    .replace(/<h2>/g, '<h2 class="text-3xl lg:text-4xl font-bold text-foreground mb-6 mt-10 leading-tight border-b border-divider pb-3">')
    .replace(/<h3>/g, '<h3 class="text-2xl lg:text-3xl font-semibold text-foreground mb-5 mt-8 leading-tight">')
    .replace(/<h4>/g, '<h4 class="text-xl lg:text-2xl font-semibold text-foreground mb-4 mt-6 leading-tight">')
    .replace(/<h5>/g, '<h5 class="text-lg lg:text-xl font-semibold text-foreground mb-3 mt-5 leading-tight">')
    .replace(/<h6>/g, '<h6 class="text-base lg:text-lg font-semibold text-foreground mb-3 mt-4 leading-tight">')

    // Paragraphs with proper spacing and readability
    .replace(/<p>/g, '<p class="text-default-700 leading-relaxed mb-6 text-base lg:text-lg">')

    // Links with HeroUI primary color and hover effects
    .replace(/<a /g, '<a class="text-primary font-medium hover:text-primary-600 transition-colors duration-200 underline decoration-primary/30 hover:decoration-primary" ')

    // Strong and emphasis elements
    .replace(/<strong>/g, '<strong class="text-foreground font-semibold">')
    .replace(/<em>/g, '<em class="text-default-600 italic">')

    // Inline code with HeroUI styling
    .replace(/<code>/g, '<code class="text-primary bg-primary/10 px-2 py-1 rounded-md text-sm font-mono border border-primary/20">')

    // Blockquotes with professional styling
    .replace(/<blockquote>/g, '<blockquote class="border-l-4 border-primary bg-content2 p-6 rounded-r-xl my-8 text-foreground shadow-sm">')

    // Lists with proper spacing
    .replace(/<ul>/g, '<ul class="space-y-3 mb-8 pl-6">')
    .replace(/<ol>/g, '<ol class="space-y-3 mb-8 pl-6">')
    .replace(/<li>/g, '<li class="text-default-700 leading-relaxed relative before:content-[\'•\'] before:text-primary before:font-bold before:absolute before:-left-4 before:top-0">')

    // Images with professional styling
    .replace(/<img /g, '<img class="rounded-xl shadow-lg border border-divider my-8 w-full max-w-4xl mx-auto" ')

    // Tables with HeroUI styling
    .replace(/<table>/g, '<div class="overflow-x-auto my-8"><table class="min-w-full border border-divider rounded-lg overflow-hidden shadow-sm">')
    .replace(/<\/table>/g, '</table></div>')
    .replace(/<thead>/g, '<thead class="bg-content2">')
    .replace(/<th>/g, '<th class="text-foreground font-semibold p-4 text-left border-b border-divider">')
    .replace(/<td>/g, '<td class="p-4 border-b border-divider text-default-700">')
    .replace(/<tr>/g, '<tr class="hover:bg-content2/50 transition-colors">')

    // Horizontal rules
    .replace(/<hr>/g, '<hr class="border-divider my-12">')

    // Pre and code blocks (for syntax highlighting)
    .replace(/<pre>/g, '<pre class="bg-content1 border border-divider rounded-xl p-6 my-8 overflow-x-auto shadow-sm">')
    .replace(/<pre([^>]*)><code([^>]*)>/g, '<pre$1><code$2 class="text-sm font-mono leading-relaxed">');

  return html;
}
