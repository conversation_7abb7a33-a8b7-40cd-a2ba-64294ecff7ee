import fs from 'fs';
import path from 'path';

export interface LegalDocument {
  title: string;
  content: string;
  lastUpdated: string;
  effectiveDate: string;
}

export function getLegalDocument(slug: string): LegalDocument {
  const fullPath = path.join(process.cwd(), 'legal', `${slug}.md`);
  
  if (!fs.existsSync(fullPath)) {
    throw new Error(`Legal document not found: ${slug}`);
  }
  
  const fileContents = fs.readFileSync(fullPath, 'utf8');
  
  // Extract metadata from the markdown
  const lines = fileContents.split('\n');
  let title = '';
  let effectiveDate = '';
  let lastUpdated = '';
  let contentStartIndex = 0;
  
  // Find title (first # heading)
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.startsWith('# ') && !title) {
      title = line.substring(2).trim();
      contentStartIndex = i;
      break;
    }
  }
  
  // Find dates in the first few lines
  for (let i = 0; i < Math.min(10, lines.length); i++) {
    const line = lines[i].trim();
    if (line.includes('Effective Date:')) {
      effectiveDate = line.split('Effective Date:')[1]?.trim().replace(/\*\*/g, '') || '';
    }
    if (line.includes('Last Updated:')) {
      lastUpdated = line.split('Last Updated:')[1]?.trim().replace(/\*\*/g, '') || '';
    }
  }
  
  // Get content starting from the title
  const content = lines.slice(contentStartIndex).join('\n');
  
  return {
    title: title || 'Legal Document',
    content,
    lastUpdated: lastUpdated || 'January 3, 2025',
    effectiveDate: effectiveDate || 'January 3, 2025'
  };
}

export function generateTableOfContents(content: string) {
  const lines = content.split('\n');
  const toc: Array<{ id: string; title: string; level: number }> = [];
  
  for (const line of lines) {
    const trimmed = line.trim();
    if (trimmed.startsWith('#')) {
      const hashMatch = /^#+/.exec(trimmed);
      const level = hashMatch?.[0].length ?? 1;
      const title = trimmed.replace(/^#+\s*/, '').replace(/\{[^}]*\}$/, '').trim();
      
      if (title && level <= 3) { // Only include h1, h2, h3
        const id = title
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .replace(/^(-)|(-$)/g, '');
        
        toc.push({ id, title, level });
      }
    }
  }
  
  return toc;
}

export function markdownToJSX(content: string): string {
  // Basic markdown to HTML conversion
  let html = content;
  
  // Headers
  html = html.replace(/^### (.*$)/gim, '<h3 className="text-xl font-semibold text-foreground mb-4 mt-8">$1</h3>');
  html = html.replace(/^## (.*$)/gim, '<h2 className="text-2xl font-bold text-foreground mb-6 mt-10">$1</h2>');
  html = html.replace(/^# (.*$)/gim, '<h1 className="text-3xl font-bold text-foreground mb-8">$1</h1>');
  
  // Bold and italic
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong className="font-semibold">$1</strong>');
  html = html.replace(/\*(.*?)\*/g, '<em className="italic">$1</em>');
  
  // Links
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" className="text-primary hover:underline">$1</a>');
  
  // Lists
  html = html.replace(/^\* (.*$)/gim, '<li className="mb-2">$1</li>');
  html = html.replace(/^- (.*$)/gim, '<li className="mb-2">$1</li>');
  
  // Wrap consecutive list items in ul
  html = html.replace(/(<li[^>]*>.*?<\/li>\s*)+/gs, '<ul className="list-disc list-inside mb-6 space-y-2">$&</ul>');
  
  // Paragraphs
  html = html.replace(/^(?!<[uh]|<li|<strong|<em)(.+)$/gim, '<p className="mb-4 text-foreground-700 leading-relaxed">$1</p>');
  
  // Code blocks
  html = html.replace(/```([^`]+)```/g, '<pre className="bg-content2 p-4 rounded-lg mb-6 overflow-x-auto"><code>$1</code></pre>');
  
  // Inline code
  html = html.replace(/`([^`]+)`/g, '<code className="bg-content2 px-2 py-1 rounded text-sm">$1</code>');
  
  // Horizontal rules
  html = html.replace(/^---$/gim, '<hr className="my-8 border-divider" />');
  
  // Clean up extra whitespace
  html = html.replace(/\n\s*\n/g, '\n');
  
  return html;
}
