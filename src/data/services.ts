import {
  CodeBracketIcon,
  CpuChipIcon,
  ShieldCheckIcon,
  CloudIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";

export interface ServiceData {
  id: string;
  icon: React.ComponentType<any>;
  title: string;
  description: string;
  shortDescription: string;
  features: string[];
  technologies: string[];
  benefits: string[];
  process: string[];
  timeline: string;
  pricing: string;
  ctaText: string;
  ctaLink: string;
  caseStudy?: {
    title: string;
    description: string;
    results: string[];
  };
}

export const servicesData: ServiceData[] = [
  {
    id: "custom-software",
    icon: CodeBracketIcon,
    title: "Custom Software Development",
    shortDescription: "End-to-end custom software solutions tailored to your business needs. From web applications to mobile apps and enterprise systems.",
    description: "Transform your business with bespoke software solutions designed specifically for your unique requirements. Our expert development team creates scalable, secure, and user-friendly applications that drive efficiency and growth. From initial concept to deployment and ongoing support, we deliver software that perfectly aligns with your business objectives.",
    features: [
      "Full-stack web development with modern frameworks",
      "Native and cross-platform mobile app development",
      "Enterprise software solutions and integrations",
      "RESTful and GraphQL API development",
      "Legacy system modernization and migration",
      "Database design and optimization",
      "Third-party service integrations",
      "Performance optimization and scaling"
    ],
    technologies: ["React", "Next.js", "Node.js", "Python", "Java", "Swift", "Kotlin", "PostgreSQL", "MongoDB"],
    benefits: [
      "Increased operational efficiency by 40-60%",
      "Reduced manual processes and human errors",
      "Improved customer experience and satisfaction",
      "Scalable solutions that grow with your business",
      "Competitive advantage through custom features",
      "Better data insights and reporting capabilities"
    ],
    process: [
      "Requirements analysis and technical feasibility study",
      "System architecture design and technology selection",
      "Agile development with regular sprint reviews",
      "Comprehensive testing and quality assurance",
      "Deployment, training, and ongoing support"
    ],
    timeline: "3-12 months depending on complexity and scope",
    pricing: "Starting from $25,000 for MVP solutions, enterprise projects from $100,000+",
    ctaText: "Start Your Project",
    ctaLink: "/contact",
    caseStudy: {
      title: "E-commerce Platform Transformation",
      description: "Developed a custom e-commerce platform for a retail client, replacing their legacy system with a modern, scalable solution.",
      results: [
        "300% increase in online sales within 6 months",
        "50% reduction in page load times",
        "99.9% uptime with improved reliability",
        "Seamless integration with existing inventory systems"
      ]
    }
  },
  {
    id: "ai-ml",
    icon: CpuChipIcon,
    title: "AI & Machine Learning Integration",
    shortDescription: "Transform your business with intelligent automation, predictive analytics, and machine learning solutions that drive real results.",
    description: "Harness the power of artificial intelligence to automate processes, gain predictive insights, and create intelligent user experiences. Our AI/ML solutions are designed to solve real business problems, from customer service automation to predictive maintenance and data-driven decision making.",
    features: [
      "Custom machine learning model development",
      "Natural language processing and chatbots",
      "Computer vision and image recognition",
      "Predictive analytics and forecasting",
      "AI-powered automation workflows",
      "Recommendation systems",
      "Anomaly detection and fraud prevention",
      "Large Language Model (LLM) integration"
    ],
    technologies: ["TensorFlow", "PyTorch", "OpenAI", "Scikit-learn", "Pandas", "Hugging Face", "AWS SageMaker", "Azure ML"],
    benefits: [
      "Automated decision-making processes",
      "Improved accuracy in predictions and insights",
      "Reduced operational costs through automation",
      "Enhanced customer experience with personalization",
      "Faster data processing and analysis",
      "Competitive advantage through AI capabilities"
    ],
    process: [
      "Data assessment and AI readiness evaluation",
      "Model design and algorithm selection",
      "Data preparation and feature engineering",
      "Model training, validation, and optimization",
      "Integration, deployment, and monitoring"
    ],
    timeline: "2-8 months for initial implementation, ongoing optimization",
    pricing: "POC starting from $15,000, production systems from $50,000+",
    ctaText: "Explore AI Solutions",
    ctaLink: "/contact",
    caseStudy: {
      title: "Predictive Maintenance System",
      description: "Implemented an AI-powered predictive maintenance solution for a manufacturing client to reduce equipment downtime.",
      results: [
        "70% reduction in unexpected equipment failures",
        "30% decrease in maintenance costs",
        "Improved equipment lifespan by 25%",
        "Real-time monitoring and alerts system"
      ]
    }
  },
  {
    id: "cybersecurity",
    icon: ShieldCheckIcon,
    title: "Cybersecurity Solutions",
    shortDescription: "Comprehensive security solutions to protect your digital assets, ensure compliance, and maintain customer trust.",
    description: "Protect your business from evolving cyber threats with our comprehensive security solutions. We provide end-to-end cybersecurity services including security assessments, implementation of security frameworks, compliance management, and ongoing monitoring to ensure your digital assets remain secure.",
    features: [
      "Security audits and vulnerability assessments",
      "Penetration testing and ethical hacking",
      "Security architecture design and implementation",
      "Identity and access management (IAM)",
      "Data encryption and protection strategies",
      "Compliance management (GDPR, HIPAA, SOC 2)",
      "Security monitoring and incident response",
      "Employee security training and awareness"
    ],
    technologies: ["OWASP", "Nessus", "Metasploit", "Wireshark", "Splunk", "AWS Security", "Azure Security", "Okta"],
    benefits: [
      "Protection against data breaches and cyber attacks",
      "Compliance with industry regulations",
      "Reduced risk of financial and reputational damage",
      "Improved customer trust and confidence",
      "Business continuity and disaster recovery",
      "Competitive advantage through security excellence"
    ],
    process: [
      "Security assessment and risk analysis",
      "Security strategy and framework design",
      "Implementation of security controls and measures",
      "Testing and validation of security systems",
      "Ongoing monitoring and incident response"
    ],
    timeline: "1-6 months for initial implementation, ongoing monitoring",
    pricing: "Security audits from $10,000, comprehensive solutions from $30,000+",
    ctaText: "Secure Your Business",
    ctaLink: "/contact"
  },
  {
    id: "cloud-devops",
    icon: CloudIcon,
    title: "Cloud Architecture & DevOps",
    shortDescription: "Scalable cloud solutions and DevOps practices to optimize your infrastructure, reduce costs, and accelerate deployment cycles.",
    description: "Modernize your infrastructure with cloud-native solutions and DevOps practices that enable rapid, reliable software delivery. We help organizations migrate to the cloud, implement CI/CD pipelines, and establish DevOps cultures that accelerate innovation while maintaining security and reliability.",
    features: [
      "Cloud migration strategies and execution",
      "Infrastructure as Code (IaC) implementation",
      "CI/CD pipeline design and automation",
      "Container orchestration with Kubernetes",
      "Microservices architecture design",
      "Performance monitoring and optimization",
      "Auto-scaling and load balancing",
      "Disaster recovery and backup strategies"
    ],
    technologies: ["AWS", "Azure", "GCP", "Docker", "Kubernetes", "Terraform", "Jenkins", "GitLab CI", "Prometheus"],
    benefits: [
      "Reduced infrastructure costs by 30-50%",
      "Faster deployment cycles and time-to-market",
      "Improved system reliability and uptime",
      "Enhanced scalability and flexibility",
      "Better resource utilization and efficiency",
      "Simplified maintenance and operations"
    ],
    process: [
      "Infrastructure assessment and cloud readiness evaluation",
      "Migration strategy and architecture design",
      "Implementation of cloud infrastructure and DevOps tools",
      "Testing, optimization, and performance tuning",
      "Training and knowledge transfer to your team"
    ],
    timeline: "2-6 months for migration, ongoing optimization",
    pricing: "Cloud assessments from $8,000, full migrations from $40,000+",
    ctaText: "Optimize Infrastructure",
    ctaLink: "/contact"
  },
  {
    id: "enterprise-consulting",
    icon: BuildingOfficeIcon,
    title: "Enterprise Software Consulting",
    shortDescription: "Strategic consulting for large-scale enterprise transformations, system integrations, and technology roadmap planning.",
    description: "Navigate complex enterprise technology challenges with our strategic consulting services. We help large organizations plan and execute digital transformations, integrate disparate systems, and develop technology roadmaps that align with business objectives and drive sustainable growth.",
    features: [
      "Digital transformation strategy and planning",
      "Enterprise architecture design and governance",
      "Technology roadmap development",
      "System integration and API strategy",
      "Change management and adoption planning",
      "Vendor evaluation and selection",
      "Performance optimization and scaling",
      "Compliance and risk management"
    ],
    technologies: ["Microservices", "API Gateway", "Event Streaming", "CQRS", "DDD", "Enterprise Service Bus", "BPMN"],
    benefits: [
      "Aligned technology strategy with business goals",
      "Improved operational efficiency and productivity",
      "Reduced technical debt and maintenance costs",
      "Enhanced system integration and data flow",
      "Better decision-making through improved analytics",
      "Increased agility and responsiveness to market changes"
    ],
    process: [
      "Current state assessment and gap analysis",
      "Future state vision and strategy development",
      "Roadmap planning and prioritization",
      "Implementation planning and execution support",
      "Change management and success measurement"
    ],
    timeline: "3-18 months depending on scope and complexity",
    pricing: "Strategic consulting from $20,000, full transformations from $200,000+",
    ctaText: "Transform Your Enterprise",
    ctaLink: "/contact"
  },
  {
    id: "talent-acquisition",
    icon: UserGroupIcon,
    title: "Talent Acquisition & HR Management",
    shortDescription: "Comprehensive talent acquisition and HR management services for technical positions. From sourcing to onboarding, we handle the complete recruitment lifecycle.",
    description: "Streamline your hiring process with our specialized talent acquisition and HR management services. We focus on sourcing, evaluating, and onboarding top technical talent while ensuring compliance and cultural fit. Our comprehensive approach reduces time-to-hire and improves retention rates.",
    features: [
      "Technical talent search and recruitment",
      "Interview management and coordination",
      "Technical assessment and evaluation",
      "HR compliance and documentation",
      "Onboarding process management",
      "Salary benchmarking and negotiation",
      "Background verification and reference checks",
      "Retention strategy development"
    ],
    technologies: ["ATS Systems", "LinkedIn Recruiter", "HackerRank", "Codility", "GitHub", "Stack Overflow", "AngelList", "Glassdoor"],
    benefits: [
      "Reduced time-to-hire by 40-60%",
      "Higher quality candidate matches",
      "Improved retention rates and cultural fit",
      "Streamlined compliance and documentation",
      "Cost-effective recruitment solutions",
      "Access to passive candidate networks"
    ],
    process: [
      "Requirements analysis and role definition",
      "Candidate sourcing and initial screening",
      "Technical assessment and evaluation",
      "Interview coordination and management",
      "Offer negotiation and onboarding support"
    ],
    timeline: "2-8 weeks per position, ongoing retainer relationships available",
    pricing: "Per-position fees from $5,000, annual retainer contracts from $50,000+",
    ctaText: "Find Top Talent",
    ctaLink: "/contact",
    caseStudy: {
      title: "Startup Technical Team Building",
      description: "Helped a fast-growing startup build their entire technical team of 15 engineers within 6 months.",
      results: [
        "15 technical positions filled in 6 months",
        "90% candidate retention rate after 1 year",
        "50% reduction in hiring timeline",
        "100% compliance with employment regulations"
      ]
    }
  }
];
