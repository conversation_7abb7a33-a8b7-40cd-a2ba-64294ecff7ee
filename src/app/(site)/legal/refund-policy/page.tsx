import { Metadata } from 'next';
import { LegalPageLayout } from '@/components/Legal';
import MarkdownContent from '@/components/Legal/MarkdownContent';
import { getLegalDocument, generateTableOfContents } from '@/utils/legal-content';

export const metadata: Metadata = {
  title: 'Refund Policy | Digital Wave Systems',
  description: 'Comprehensive refund policy for Digital Wave Systems software development and consulting services. Milestone-based refunds, service guarantees, and transparent refund procedures.',
  keywords: 'refund policy, software development refunds, consulting service refunds, milestone-based refunds, service guarantees, cybersecurity refunds, talent acquisition guarantees',
  openGraph: {
    title: 'Refund Policy | Digital Wave Systems',
    description: 'Comprehensive refund policy with milestone-based structure for software development and consulting services.',
    type: 'website',
    url: 'https://digitalwavesystems.com.co/legal/refund-policy',
    siteName: 'Digital Wave Systems',
    images: [
      {
        url: '/images/logo/Logo.svg',
        width: 1200,
        height: 630,
        alt: 'Digital Wave Systems Refund Policy',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Refund Policy | Digital Wave Systems',
    description: 'Comprehensive refund policy with milestone-based structure for software development and consulting services.',
    images: ['/images/logo/Logo.svg'],
  },
  alternates: {
    canonical: 'https://digitalwavesystems.com.co/legal/refund-policy',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RefundPolicyPage() {
  const document = getLegalDocument('refund-policy');
  const tableOfContents = generateTableOfContents(document.content);

  return (
    <LegalPageLayout
      title={document.title}
      lastUpdated={document.lastUpdated}
      effectiveDate={document.effectiveDate}
      documentType="refund"
      tableOfContents={tableOfContents}
    >
      <MarkdownContent content={document.content} />
    </LegalPageLayout>
  );
}
