import { Metadata } from 'next';
import { LegalPageLayout } from '@/components/Legal';
import MarkdownContent from '@/components/Legal/MarkdownContent';
import { getLegalDocument, generateTableOfContents } from '@/utils/legal-content';

export const metadata: Metadata = {
  title: 'Terms and Conditions | Digital Wave Systems',
  description: 'Comprehensive B2B terms and conditions for Digital Wave Systems software development and consulting services. Covers service agreements, IP rights, liability, and international compliance.',
  keywords: 'terms and conditions, B2B software development terms, service agreements, intellectual property rights, liability limitations, international business terms, software consulting agreements',
  openGraph: {
    title: 'Terms and Conditions | Digital Wave Systems',
    description: 'Comprehensive B2B terms and conditions for professional software development and consulting services.',
    type: 'website',
    url: 'https://digitalwavesystems.com.co/legal/terms-and-conditions',
    siteName: 'Digital Wave Systems',
    images: [
      {
        url: '/images/logo/Logo.svg',
        width: 1200,
        height: 630,
        alt: 'Digital Wave Systems Terms and Conditions',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Terms and Conditions | Digital Wave Systems',
    description: 'Comprehensive B2B terms and conditions for professional software development and consulting services.',
    images: ['/images/logo/Logo.svg'],
  },
  alternates: {
    canonical: 'https://digitalwavesystems.com.co/legal/terms-and-conditions',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function TermsAndConditionsPage() {
  const document = getLegalDocument('terms-and-conditions');
  const tableOfContents = generateTableOfContents(document.content);

  return (
    <LegalPageLayout
      title={document.title}
      lastUpdated={document.lastUpdated}
      effectiveDate={document.effectiveDate}
      documentType="terms"
      tableOfContents={tableOfContents}
    >
      <MarkdownContent content={document.content} />
    </LegalPageLayout>
  );
}
