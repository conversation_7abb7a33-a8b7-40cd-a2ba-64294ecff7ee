import { Metadata } from 'next';
import { LegalPageLayout } from '@/components/Legal';
import MarkdownContent from '@/components/Legal/MarkdownContent';
import { getLegalDocument, generateTableOfContents } from '@/utils/legal-content';

export const metadata: Metadata = {
  title: 'Privacy Policy | Digital Wave Systems',
  description: 'GDPR and CCPA compliant privacy policy for Digital Wave Systems. Learn how we protect your data in our software development, AI/ML, cybersecurity, and consulting services.',
  keywords: 'privacy policy, GDPR compliance, CCPA compliance, data protection, software development privacy, AI ML data processing, cybersecurity data protection, international privacy law',
  openGraph: {
    title: 'Privacy Policy | Digital Wave Systems',
    description: 'GDPR and CCPA compliant privacy policy for professional software development services.',
    type: 'website',
    url: 'https://digitalwavesystems.com.co/legal/privacy-policy',
    siteName: 'Digital Wave Systems',
    images: [
      {
        url: '/images/logo/Logo.svg',
        width: 1200,
        height: 630,
        alt: 'Digital Wave Systems Privacy Policy',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Privacy Policy | Digital Wave Systems',
    description: 'GDPR and CCPA compliant privacy policy for professional software development services.',
    images: ['/images/logo/Logo.svg'],
  },
  alternates: {
    canonical: 'https://digitalwavesystems.com.co/legal/privacy-policy',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function PrivacyPolicyPage() {
  const document = getLegalDocument('privacy-policy');
  const tableOfContents = generateTableOfContents(document.content);

  return (
    <LegalPageLayout
      title={document.title}
      lastUpdated={document.lastUpdated}
      effectiveDate={document.effectiveDate}
      documentType="privacy"
      tableOfContents={tableOfContents}
    >
      <MarkdownContent content={document.content} />
    </LegalPageLayout>
  );
}
