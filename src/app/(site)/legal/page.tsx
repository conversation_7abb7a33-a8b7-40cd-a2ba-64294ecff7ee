import { Metadata } from 'next';
import { LegalIndex } from '@/components/Legal';

export const metadata: Metadata = {
  title: 'Legal Documents | Digital Wave Systems',
  description: 'Comprehensive legal framework for B2B software development and consulting services including privacy policy, terms and conditions, and refund policy.',
  keywords: 'legal documents, privacy policy, terms and conditions, refund policy, software development legal, B2B legal framework, GDPR compliance, CCPA compliance',
  openGraph: {
    title: 'Legal Documents | Digital Wave Systems',
    description: 'Comprehensive legal framework for B2B software development and consulting services.',
    type: 'website',
    url: 'https://digitalwavesystems.com.co/legal',
    siteName: 'Digital Wave Systems',
    images: [
      {
        url: '/images/logo/Logo.svg',
        width: 1200,
        height: 630,
        alt: 'Digital Wave Systems Legal Documents',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Legal Documents | Digital Wave Systems',
    description: 'Comprehensive legal framework for B2B software development and consulting services.',
    images: ['/images/logo/Logo.svg'],
  },
  alternates: {
    canonical: 'https://digitalwavesystems.com.co/legal',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function LegalPage() {
  return <LegalIndex />;
}
