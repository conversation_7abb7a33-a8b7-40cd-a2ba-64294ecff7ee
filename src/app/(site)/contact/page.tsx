import PageHero from "@/components/Common/PageHero";
import Contact from "@/components/Contact";
import { Metadata } from "next";
import React from "react";

export const metadata: Metadata = {
  title: "Contact Digital Wave Systems | Software Development Consulting",
  description: "Get in touch with Digital Wave Systems for custom software development, enterprise solutions, and technology consulting services. Let's discuss your project needs.",
};

const ContactPage = () => {
  return (
    <>
      <PageHero
        badge="Contact Us"
        title="Let's Discuss Your Software Solution Needs"
        description="Get in touch with Digital Wave Systems for custom software development, enterprise solutions, and technology consulting services. We're here to help transform your business."
        primaryButtonText="Schedule Consultation"
        primaryButtonLink="#contact"
        secondaryButtonText="View Our Services"
        secondaryButtonLink="/services"
      />

      <Contact />
    </>
  );
};

export default ContactPage;
