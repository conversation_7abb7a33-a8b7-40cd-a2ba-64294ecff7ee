import React from 'react';
import { Metadata } from 'next';
import ServiceDetailPage from '@/components/Services/ServiceDetailPage';

export const metadata: Metadata = {
  title: "Cloud & DevOps Services | Digital Wave Systems",
  description: "Accelerate your digital transformation with expert cloud migration, DevOps automation, and infrastructure optimization services. AWS, Azure, Google Cloud specialists.",
  keywords: "cloud migration, DevOps, AWS, Azure, Google Cloud, infrastructure automation, CI/CD, containerization, Kubernetes, cloud consulting",
  openGraph: {
    title: "Cloud & DevOps Services | Digital Wave Systems",
    description: "Expert cloud migration and DevOps automation services to accelerate your digital transformation.",
    type: "website",
    url: "https://digitalwavesystems.com/services/cloud-devops",
  },
  twitter: {
    card: "summary_large_image",
    title: "Cloud & DevOps Services | Digital Wave Systems",
    description: "Expert cloud migration and DevOps automation services to accelerate your digital transformation.",
  },
  alternates: {
    canonical: "https://digitalwavesystems.com/services/cloud-devops",
  },
};

// Professional service data for Cloud & DevOps
const serviceData = {
  id: "cloud-devops",
  title: "Cloud & DevOps Services",
  subtitle: "Accelerate Your Digital Transformation",
  heroImage: "/images/services/cloud-devops-hero.jpg",
  description: "Accelerate your digital transformation with expert cloud migration, DevOps automation, and infrastructure optimization. Our certified cloud architects and DevOps engineers help you leverage the full potential of cloud technologies while ensuring security, scalability, and cost-effectiveness.",
  
  overview: {
    title: "Cloud-Native Solutions & DevOps Excellence",
    content: "We provide comprehensive cloud and DevOps services that enable organizations to build, deploy, and scale applications faster and more reliably. Our expertise spans all major cloud platforms and modern DevOps practices, ensuring your infrastructure is optimized for performance, security, and cost-efficiency.",
    benefits: [
      "Reduced deployment time by 80% with automated CI/CD",
      "Decreased infrastructure costs by 40-60%",
      "Improved system reliability and uptime to 99.9%",
      "Enhanced security with cloud-native best practices",
      "Faster time-to-market for new features and products",
      "Scalable infrastructure that grows with your business"
    ]
  },

  services: [
    {
      title: "Cloud Migration & Architecture",
      description: "Strategic cloud migration planning and implementation with optimized architecture design.",
      features: [
        "Cloud readiness assessment and strategy",
        "Multi-cloud and hybrid cloud architecture",
        "Application modernization and refactoring",
        "Data migration and synchronization",
        "Performance optimization and cost analysis",
        "Security and compliance implementation"
      ]
    },
    {
      title: "DevOps Automation & CI/CD",
      description: "Complete DevOps transformation with automated pipelines and infrastructure as code.",
      features: [
        "CI/CD pipeline design and implementation",
        "Infrastructure as Code (IaC) with Terraform",
        "Automated testing and quality assurance",
        "Container orchestration with Kubernetes",
        "Monitoring and observability solutions",
        "Release management and deployment strategies"
      ]
    },
    {
      title: "Container & Microservices",
      description: "Modern application architecture with containerization and microservices design.",
      features: [
        "Docker containerization strategies",
        "Kubernetes cluster management",
        "Microservices architecture design",
        "Service mesh implementation",
        "Container security and scanning",
        "Scalable container orchestration"
      ]
    },
    {
      title: "Cloud Security & Compliance",
      description: "Comprehensive cloud security implementation and compliance management.",
      features: [
        "Cloud security architecture and policies",
        "Identity and access management (IAM)",
        "Network security and segmentation",
        "Compliance automation and monitoring",
        "Security scanning and vulnerability management",
        "Disaster recovery and backup strategies"
      ]
    }
  ],

  process: {
    title: "Our Cloud & DevOps Transformation Process",
    steps: [
      {
        phase: "Assessment & Strategy",
        duration: "1-2 weeks",
        description: "Comprehensive evaluation of current infrastructure and development of cloud strategy.",
        deliverables: ["Infrastructure assessment", "Cloud migration strategy", "Cost-benefit analysis"]
      },
      {
        phase: "Architecture Design",
        duration: "2-3 weeks",
        description: "Design optimal cloud architecture and DevOps workflows for your requirements.",
        deliverables: ["Cloud architecture diagrams", "DevOps pipeline design", "Security framework"]
      },
      {
        phase: "Implementation & Migration",
        duration: "4-12 weeks",
        description: "Execute cloud migration and implement DevOps automation with minimal downtime.",
        deliverables: ["Migrated applications", "Automated pipelines", "Infrastructure as Code"]
      },
      {
        phase: "Optimization & Training",
        duration: "2-4 weeks",
        description: "Optimize performance and costs while training your team on new processes.",
        deliverables: ["Performance reports", "Cost optimization", "Team training materials"]
      },
      {
        phase: "Ongoing Support",
        duration: "Ongoing",
        description: "Continuous monitoring, optimization, and support for your cloud infrastructure.",
        deliverables: ["Monitoring dashboards", "Performance reports", "Regular optimizations"]
      }
    ]
  },

  technologies: {
    "Cloud Platforms": ["AWS", "Microsoft Azure", "Google Cloud Platform", "DigitalOcean", "Linode"],
    "DevOps Tools": ["Jenkins", "GitLab CI", "GitHub Actions", "Azure DevOps", "CircleCI"],
    "Infrastructure": ["Terraform", "Ansible", "CloudFormation", "Pulumi", "Helm"],
    "Containers": ["Docker", "Kubernetes", "OpenShift", "Docker Swarm", "Rancher"],
    "Monitoring": ["Prometheus", "Grafana", "ELK Stack", "Datadog", "New Relic"],
    "Security": ["Vault", "AWS IAM", "Azure AD", "Cloud Security Posture Management"]
  },

  caseStudies: [
    {
      title: "E-commerce Platform Cloud Migration",
      client: "Online Retail Company",
      challenge: "Legacy on-premises infrastructure couldn't handle traffic spikes during peak seasons, leading to downtime and lost revenue.",
      solution: "Migrated to AWS with auto-scaling architecture, implemented CI/CD pipelines, and established comprehensive monitoring and alerting systems.",
      results: [
        "99.9% uptime during peak traffic periods",
        "50% reduction in infrastructure costs",
        "80% faster deployment cycles",
        "Zero downtime deployments achieved"
      ],
      technologies: ["AWS", "Kubernetes", "Terraform", "Jenkins", "Prometheus"]
    }
  ],

  pricing: {
    title: "Cloud & DevOps Investment Models",
    models: [
      {
        type: "Cloud Assessment",
        description: "Comprehensive evaluation of current infrastructure and migration planning",
        startingPrice: "Starting from $8,000",
        features: [
          "Infrastructure assessment",
          "Cloud readiness evaluation",
          "Migration strategy and roadmap",
          "Cost-benefit analysis"
        ]
      },
      {
        type: "Cloud Migration",
        description: "Complete cloud migration with DevOps implementation",
        startingPrice: "Starting from $30,000",
        features: [
          "Full cloud migration",
          "DevOps pipeline setup",
          "Infrastructure automation",
          "Team training and documentation"
        ]
      },
      {
        type: "Managed Cloud Services",
        description: "Ongoing cloud management and optimization",
        startingPrice: "Starting from $3,000/month",
        features: [
          "24/7 infrastructure monitoring",
          "Performance optimization",
          "Cost management",
          "Security and compliance"
        ]
      }
    ]
  },

  faq: [
    {
      question: "Which cloud platform is best for my business?",
      answer: "The best cloud platform depends on your specific requirements, existing technology stack, budget, and compliance needs. We evaluate AWS, Azure, Google Cloud, and other platforms to recommend the optimal solution for your business goals."
    },
    {
      question: "How long does a typical cloud migration take?",
      answer: "Cloud migration timelines vary based on application complexity and data volume, but most projects take 2-6 months. We use phased migration approaches to minimize downtime and ensure business continuity throughout the process."
    },
    {
      question: "Will cloud migration reduce our costs?",
      answer: "Most organizations see 30-50% cost reduction after proper cloud migration and optimization. We provide detailed cost analysis and implement cost optimization strategies including right-sizing, reserved instances, and automated scaling."
    },
    {
      question: "How do you ensure security during cloud migration?",
      answer: "We implement comprehensive security measures including encryption in transit and at rest, identity and access management, network security, compliance frameworks, and continuous security monitoring throughout the migration process."
    },
    {
      question: "Do you provide training for our team?",
      answer: "Yes, we provide comprehensive training programs covering cloud platforms, DevOps tools, and best practices. Our training includes hands-on workshops, documentation, and ongoing support to ensure your team can effectively manage the new infrastructure."
    }
  ]
};

export default function CloudDevOpsPage() {
  return <ServiceDetailPage service={serviceData} />;
}
