import { Metadata } from "next";
import ServicesPage from "@/components/Services";

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Digital Wave Systems",
  "description": "Leading software development company specializing in custom software solutions, enterprise applications, AI/ML integration, and cloud/DevOps services.",
  "url": "https://digitalwavesystems.com",
  "logo": "https://digitalwavesystems.com/images/logo/logo.svg",
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "******-0123",
    "contactType": "customer service",
    "availableLanguage": "English"
  },
  "address": {
    "@type": "PostalAddress",
    "addressCountry": "US"
  },
  "sameAs": [
    "https://linkedin.com/company/digitalwavesystems",
    "https://twitter.com/digitalwavesys"
  ],
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Software Development Services",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Custom Software Development",
          "description": "End-to-end custom software solutions tailored to your business needs."
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "AI & Machine Learning Integration",
          "description": "Transform your business with intelligent automation and machine learning solutions."
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Cybersecurity Solutions",
          "description": "Comprehensive security services to protect your digital assets."
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Cloud Architecture & DevOps",
          "description": "Scalable cloud solutions and DevOps practices to optimize your infrastructure."
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Enterprise Software Consulting",
          "description": "Strategic consulting for large-scale enterprise transformations and system integrations."
        }
      }
    ]
  }
};

export const metadata: Metadata = {
  title: "Software Consulting Services | Digital Wave Systems",
  description: "Comprehensive software consulting services including custom development, AI integration, cybersecurity solutions, cloud architecture, and enterprise consulting. Transform your business with our expert team.",
  keywords: "software consulting, custom software development, AI integration, machine learning, cybersecurity solutions, cloud architecture, DevOps, enterprise software, digital transformation, software development company, enterprise consulting, technology consulting, full-stack development, microservices architecture, legacy system modernization",
  authors: [{ name: "Digital Wave Systems" }],
  creator: "Digital Wave Systems",
  publisher: "Digital Wave Systems",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: "Software Consulting Services | Digital Wave Systems",
    description: "Expert software consulting services to accelerate your digital transformation. Custom development, AI/ML integration, cybersecurity, and cloud solutions.",
    type: "website",
    url: "https://digitalwavesystems.com.co/services",
    siteName: "Digital Wave Systems",
    images: [
      {
        url: "/images/logo/Logo.svg",
        width: 1200,
        height: 630,
        alt: "Digital Wave Systems - Software Consulting Services",
      },
    ],
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Software Consulting Services | Digital Wave Systems",
    description: "Expert software consulting services to accelerate your digital transformation.",
    images: ["/images/logo/Logo.svg"],
    creator: "@digitalwavesys",
    site: "@digitalwavesys",
  },
  alternates: {
    canonical: "https://digitalwavesystems.com.co/services",
  },
  category: "Technology",
};

const Services = () => {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <ServicesPage />
    </>
  );
};

export default Services;
