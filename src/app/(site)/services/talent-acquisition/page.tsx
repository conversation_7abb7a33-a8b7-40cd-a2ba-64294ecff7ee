import React from 'react';
import { Metadata } from 'next';
import ServiceDetailPage from '@/components/Services/ServiceDetailPage';

export const metadata: Metadata = {
  title: "Talent Acquisition & HR Management Services | Digital Wave Systems",
  description: "Comprehensive talent acquisition and HR management services for technical positions. From sourcing to onboarding, we handle the complete recruitment lifecycle with proven results.",
  keywords: "talent acquisition, technical recruitment, HR management, software developer hiring, tech talent sourcing, IT recruitment, developer staffing",
  openGraph: {
    title: "Talent Acquisition & HR Management Services | Digital Wave Systems",
    description: "Expert talent acquisition services to build high-performing technical teams.",
    type: "website",
    url: "https://digitalwavesystems.com/services/talent-acquisition",
  },
  twitter: {
    card: "summary_large_image",
    title: "Talent Acquisition & HR Management Services | Digital Wave Systems",
    description: "Expert talent acquisition services to build high-performing technical teams.",
  },
  alternates: {
    canonical: "https://digitalwavesystems.com/services/talent-acquisition",
  },
};

// Professional service data for Talent Acquisition
const serviceData = {
  id: "talent-acquisition",
  title: "Talent Acquisition & HR Management",
  subtitle: "Build Your Dream Technical Team",
  heroImage: "/images/services/talent-acquisition-hero.jpg",
  description: "Comprehensive talent acquisition and HR management services designed specifically for technology companies. We streamline your hiring process with specialized focus on technical roles, ensuring you attract, evaluate, and onboard top talent while maintaining compliance and cultural fit.",
  
  overview: {
    title: "Complete Talent Acquisition Solutions",
    content: "Our talent acquisition services are designed specifically for technology companies looking to build high-performing technical teams. We understand the unique challenges of hiring in the tech industry and provide end-to-end recruitment solutions that deliver exceptional results with proven retention rates.",
    benefits: [
      "90%+ candidate retention rates after 1 year",
      "Reduced time-to-hire by 40-60%",
      "Access to exclusive talent networks and communities",
      "Comprehensive technical skills assessment",
      "Full compliance with employment regulations",
      "Ongoing support throughout the hiring lifecycle"
    ]
  },

  services: [
    {
      title: "Technical Talent Search & Recruitment",
      description: "Comprehensive sourcing and recruitment for all technical positions from junior developers to senior architects.",
      features: [
        "Active and passive candidate sourcing",
        "Technical skills assessment and evaluation",
        "Cultural fit evaluation and team matching",
        "Competitive salary benchmarking and analysis",
        "Specialized technical role expertise",
        "Global talent network access"
      ]
    },
    {
      title: "Interview Management & Coordination",
      description: "Complete interview process management to ensure efficient and effective candidate evaluation.",
      features: [
        "Interview scheduling and coordination",
        "Technical interview facilitation",
        "Reference checks and background verification",
        "Candidate experience optimization",
        "Interview feedback compilation",
        "Decision-making support and guidance"
      ]
    },
    {
      title: "Technical Assessment & Evaluation",
      description: "Rigorous technical evaluation processes to ensure candidates meet your specific requirements.",
      features: [
        "Custom technical assessments and coding challenges",
        "Portfolio and project review",
        "Technical interview design and execution",
        "Skills gap analysis and recommendations",
        "Competency-based evaluation frameworks",
        "Performance prediction modeling"
      ]
    },
    {
      title: "HR Compliance & Onboarding",
      description: "Complete HR support to ensure legal compliance and smooth onboarding experiences.",
      features: [
        "Employment law compliance and documentation",
        "Onboarding process design and execution",
        "New hire orientation and training coordination",
        "Retention strategy development and implementation",
        "Performance management framework setup",
        "Employee engagement and satisfaction monitoring"
      ]
    }
  ],

  process: {
    title: "Our Proven Recruitment Process",
    steps: [
      {
        phase: "Requirements Analysis",
        duration: "1 week",
        description: "We work closely with your team to understand the role requirements, team dynamics, and cultural fit criteria.",
        deliverables: ["Detailed job description", "Skills assessment criteria", "Cultural fit profile"]
      },
      {
        phase: "Candidate Sourcing",
        duration: "2-3 weeks",
        description: "Comprehensive sourcing strategy using multiple channels to identify and attract top talent.",
        deliverables: ["Candidate pipeline", "Initial screening reports", "Market analysis"]
      },
      {
        phase: "Technical Assessment",
        duration: "1-2 weeks",
        description: "Rigorous evaluation process including technical interviews, coding assessments, and portfolio reviews.",
        deliverables: ["Technical evaluation reports", "Skills assessment results", "Recommendation rankings"]
      },
      {
        phase: "Interview Coordination",
        duration: "1-2 weeks",
        description: "Complete interview process management including scheduling, facilitation, and feedback collection.",
        deliverables: ["Interview schedules", "Feedback compilation", "Final recommendations"]
      },
      {
        phase: "Offer & Onboarding",
        duration: "2-4 weeks",
        description: "Support through offer negotiation, acceptance, and comprehensive onboarding process.",
        deliverables: ["Offer negotiation support", "Onboarding plan", "30-60-90 day check-ins"]
      }
    ]
  },

  technologies: {
    "ATS Platforms": ["Greenhouse", "Lever", "Workday", "BambooHR", "SmartRecruiters"],
    "Sourcing Tools": ["LinkedIn Recruiter", "GitHub", "Stack Overflow", "AngelList", "Dice"],
    "Assessment": ["HackerRank", "Codility", "CodeSignal", "Triplebyte", "Karat"],
    "Background Check": ["Checkr", "Sterling", "HireRight", "GoodHire", "Accurate"],
    "Analytics": ["Tableau", "Power BI", "Google Analytics", "Mixpanel", "Custom Dashboards"],
    "Communication": ["Slack", "Microsoft Teams", "Zoom", "Calendly", "DocuSign"]
  },

  caseStudies: [
    {
      title: "Startup Technical Team Building",
      client: "Fast-Growing SaaS Startup",
      challenge: "A rapidly scaling SaaS startup needed to build their entire technical team of 15 engineers within 6 months to meet aggressive growth targets.",
      solution: "We implemented a comprehensive recruitment strategy including targeted sourcing, streamlined interview processes, competitive compensation packages, and comprehensive onboarding programs.",
      results: [
        "15 technical positions filled in 6 months",
        "90% candidate retention after 1 year",
        "50% reduction in average time-to-hire",
        "100% compliance with employment regulations"
      ],
      technologies: ["LinkedIn Recruiter", "HackerRank", "Greenhouse", "Slack", "DocuSign"]
    }
  ],

  pricing: {
    title: "Flexible Pricing Models",
    models: [
      {
        type: "Per-Position",
        description: "Pay per successful hire with no upfront costs",
        startingPrice: "Starting from $5,000",
        features: [
          "No upfront fees",
          "Pay only for successful hires",
          "90-day replacement guarantee",
          "Complete recruitment process"
        ]
      },
      {
        type: "Annual Retainer",
        description: "Ongoing recruitment support with dedicated resources",
        startingPrice: "Starting from $50,000/year",
        features: [
          "Dedicated recruitment team",
          "Priority candidate sourcing",
          "Unlimited position postings",
          "Quarterly hiring strategy reviews"
        ]
      },
      {
        type: "Enterprise Solutions",
        description: "Custom solutions for large-scale hiring needs",
        startingPrice: "Custom pricing",
        features: [
          "Fully customized recruitment strategy",
          "Dedicated account management",
          "Advanced reporting and analytics",
          "Integration with existing HR systems"
        ]
      }
    ]
  },

  faq: [
    {
      question: "What types of technical positions do you recruit for?",
      answer: "We recruit for all technical positions including software developers, DevOps engineers, data scientists, product managers, technical leads, and C-level technology executives across various technologies and experience levels."
    },
    {
      question: "How do you ensure candidate quality?",
      answer: "We use a multi-stage evaluation process including technical assessments, coding challenges, portfolio reviews, and cultural fit interviews to ensure candidates meet your specific requirements and company culture."
    },
    {
      question: "What is your typical time-to-hire?",
      answer: "Our average time-to-hire is 2-8 weeks depending on the role complexity and requirements. We focus on quality while maintaining efficiency throughout the process with streamlined workflows."
    },
    {
      question: "Do you provide replacement guarantees?",
      answer: "Yes, we offer a 90-day replacement guarantee for all placements. If a candidate doesn't work out within the first 90 days, we'll find a replacement at no additional cost."
    },
    {
      question: "Can you help with onboarding and retention?",
      answer: "Absolutely. We provide comprehensive onboarding support and develop retention strategies to ensure long-term success of your new hires, including 30-60-90 day check-ins and performance monitoring."
    }
  ]
};

export default function TalentAcquisitionPage() {
  return <ServiceDetailPage service={serviceData} />;
}
