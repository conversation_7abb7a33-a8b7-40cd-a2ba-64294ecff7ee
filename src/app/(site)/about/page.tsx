import About from "@/components/About";
import PageHero from "@/components/Common/PageHero";
import Team from "@/components/Team";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "About Digital Wave Systems | Leading Software Development Company",
  description: "Learn about Digital Wave Systems, a premier software development company specializing in custom software solutions, enterprise applications, full-stack development, and technology consulting services.",
  keywords: "Digital Wave Systems, software development company, custom software solutions, enterprise software development, full-stack development, software consulting, technology services",
  openGraph: {
    title: "About Digital Wave Systems | Leading Software Development Company",
    description: "Learn about Digital Wave Systems, a premier software development company specializing in custom software solutions, enterprise applications, and technology consulting services.",
    images: ['/images/about/FLUXDevImage4.webp'],
    type: 'website',
  },
};

const AboutPage = () => {
  return (
    <main>
      <PageHero
        badge="About Digital Wave Systems"
        title="Your Trusted Software Consulting Partner"
        description="Learn about Digital Wave Systems, a premier software development company specializing in custom software solutions, enterprise applications, and technology consulting services."
        primaryButtonText="Start Your Project"
        primaryButtonLink="/contact"
        secondaryButtonText="View Our Services"
        secondaryButtonLink="/services"
      />
      <About />
      <Team />
    </main>
  );
};

export default AboutPage;
