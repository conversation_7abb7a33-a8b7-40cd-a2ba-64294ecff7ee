import React from "react";
import ResetPassword from "@/components/Auth/ResetPassword";
import PageHero from "@/components/Common/PageHero";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Reset Password | Digital Wave Systems LLC",
};

interface PageProps {
  params: Promise<{
    token: string;
  }>;
}

const ResetPasswordPage = async ({ params }: PageProps) => {
  const { token } = await params;
  
  return (
    <>
      <PageHero
        badge="Account Recovery"
        title="Reset Your Password"
        description="Enter your new password below to regain access to your Digital Wave Systems account."
      />
      <ResetPassword token={token} />
    </>
  );
};

export default ResetPasswordPage;
