import PageHero from "@/components/Common/PageHero";
import Faq from "@/components/Faq";
import Pricing from "@/components/Pricing";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Software Development Pricing | Digital Wave Systems",
  description: "Transparent pricing for custom software development, enterprise solutions, and technology consulting services. Choose the plan that fits your business needs.",
};

const PricingPage = () => {
  return (
    <>
      <PageHero
        badge="Pricing Plans"
        title="Transparent Software Development Pricing"
        description="Choose the perfect plan for your software development needs. From startups to enterprise, we have flexible pricing options that scale with your business."
        primaryButtonText="Get Custom Quote"
        primaryButtonLink="/contact"
        secondaryButtonText="View Services"
        secondaryButtonLink="/services"
      />
      <Pricing />
      <Faq />
    </>
  );
};

export default PricingPage;
