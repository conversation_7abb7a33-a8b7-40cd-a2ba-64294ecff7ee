import React from "react";

export default function Head() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Digital Wave Systems, LLC",
    "description": "Leading software consulting firm specializing in AI-powered solutions, custom software development, cybersecurity services, and digital transformation for enterprises.",
    "url": "https://digitalwavesystems.com",
    "logo": "https://digitalwavesystems.com/images/logo/logo.svg",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-0123",
      "contactType": "customer service",
      "availableLanguage": "English"
    },
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "US"
    },
    "sameAs": [
      "https://linkedin.com/company/digitalwavesystems",
      "https://twitter.com/digitalwavesys"
    ],
    "serviceType": [
      "Software Consulting",
      "AI Development",
      "Custom Software Development",
      "Cybersecurity Services",
      "Digital Transformation",
      "Enterprise Software Solutions"
    ]
  };

  return (
    <>
      <title>Digital Wave Systems | Premier Software Consulting & AI Development</title>
      <meta content="width=device-width, initial-scale=1" name="viewport" />
      <meta name="description" content="Leading software consulting firm specializing in AI-powered solutions, custom software development, cybersecurity services, and digital transformation for enterprises. Expert consultants delivering innovative technology solutions." />
      <meta name="theme-color" content="#3d4afc" />
      <meta name="msapplication-TileColor" content="#3d4afc" />

      {/* Favicons */}
      <link rel="icon" href="/images/logo/favicon.svg" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />

      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      {/* Additional SEO meta tags */}
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="Digital Wave Systems" />
    </>
  );
}
