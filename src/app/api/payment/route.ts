import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";

export async function POST(request: NextRequest) {
  const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
    apiVersion: "2023-10-16",
  });
  let data = await request.json();
  let priceId = data.priceId;

  const session = await stripe.checkout.sessions.create({
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    mode: "subscription",
    success_url: process.env.SITE_URL!,
    cancel_url: process.env.SITE_URL!,
  });

  return NextResponse.json(session.url);
}
