import { NextRequest, NextResponse } from 'next/server';

// Types for lead generation quote data
interface GetQuoteData {
  // Basic Contact Information
  name: string;
  email: string;
  phone?: string;
  company?: string;
  
  // Service Interest
  serviceInterest: string;
  
  // Consultation Preference
  consultationType: 'video' | 'phone' | 'email';
  
  // Timeline Flexibility
  timelineFlexibility: 'immediate' | 'flexible' | 'exploring';
  
  // Context
  leadSource: string;
  pageUrl: string;
  timestamp: string;
  formType: string;
  utmParams?: any;
  
  // Consent
  privacyConsent: boolean;
}

// Validation function
function validateQuoteData(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Required fields
  if (!data.name || typeof data.name !== 'string' || data.name.trim().length < 2) {
    errors.push('Name is required and must be at least 2 characters');
  }
  
  if (!data.email || typeof data.email !== 'string' || !isValidEmail(data.email)) {
    errors.push('Valid email address is required');
  }
  
  if (!data.serviceInterest || typeof data.serviceInterest !== 'string') {
    errors.push('Service interest is required');
  }
  
  if (!data.consultationType || !['video', 'phone', 'email'].includes(data.consultationType)) {
    errors.push('Valid consultation type is required');
  }
  
  if (!data.timelineFlexibility || !['immediate', 'flexible', 'exploring'].includes(data.timelineFlexibility)) {
    errors.push('Valid timeline flexibility is required');
  }
  
  if (!data.privacyConsent) {
    errors.push('Privacy consent is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Calculate lead generation score (different from high-intent scoring)
function calculateLeadGenScore(data: GetQuoteData): number {
  let score = 20; // Base score for lead generation form
  
  // Service interest scoring
  const serviceScores: { [key: string]: number } = {
    'enterprise-solutions': 25,
    'custom-software': 20,
    'ai-ml': 20,
    'cybersecurity': 15,
    'cloud-devops': 15,
    'web-application': 12,
    'mobile-app': 12,
    'not-sure': 8
  };
  score += serviceScores[data.serviceInterest] || 10;
  
  // Timeline urgency scoring
  const timelineScores: { [key: string]: number } = {
    'immediate': 25,
    'flexible': 15,
    'exploring': 5
  };
  score += timelineScores[data.timelineFlexibility];
  
  // Consultation type scoring (shows engagement level)
  const consultationScores: { [key: string]: number } = {
    'video': 20,
    'phone': 15,
    'email': 5
  };
  score += consultationScores[data.consultationType];
  
  // Company provided
  if (data.company && data.company.trim().length > 0) {
    score += 10;
  }
  
  // Phone provided
  if (data.phone && data.phone.trim().length > 0) {
    score += 5;
  }
  
  return Math.min(score, 100);
}

// Determine nurturing sequence based on lead characteristics
function getNurturingSequence(data: GetQuoteData, score: number): {
  sequence: string;
  priority: 'low' | 'medium' | 'high';
  responseTime: string;
  contentType: string;
} {
  let sequence = 'standard-nurture';
  let priority: 'low' | 'medium' | 'high' = 'medium';
  let responseTime = '24 hours';
  let contentType = 'educational';
  
  // Determine sequence based on timeline and score
  if (data.timelineFlexibility === 'immediate' || score >= 70) {
    sequence = 'high-intent-nurture';
    priority = 'high';
    responseTime = '4 hours';
    contentType = 'solution-focused';
  } else if (data.timelineFlexibility === 'flexible' || score >= 50) {
    sequence = 'qualified-lead-nurture';
    priority = 'medium';
    responseTime = '12 hours';
    contentType = 'educational-with-solutions';
  } else {
    sequence = 'educational-nurture';
    priority = 'low';
    responseTime = '48 hours';
    contentType = 'educational';
  }
  
  // Adjust based on consultation type
  if (data.consultationType === 'video' || data.consultationType === 'phone') {
    if (priority === 'low') priority = 'medium';
    sequence = 'consultation-focused-nurture';
  }
  
  // Service-specific adjustments
  if (['enterprise-solutions', 'custom-software', 'ai-ml'].includes(data.serviceInterest)) {
    if (priority === 'low') priority = 'medium';
    contentType = 'technical-focused';
  }
  
  return { sequence, priority, responseTime, contentType };
}

// Generate personalized content recommendations
function getContentRecommendations(data: GetQuoteData): string[] {
  const baseContent = [
    'Software Development Best Practices Guide',
    'Technology Stack Selection Checklist'
  ];
  
  const serviceContent: { [key: string]: string[] } = {
    'custom-software': [
      'Custom Software Development Process Guide',
      'ROI Calculator for Custom Software Projects',
      'Case Study: Enterprise Software Transformation'
    ],
    'web-application': [
      'Modern Web Application Architecture Guide',
      'Progressive Web App Benefits Whitepaper',
      'Web Performance Optimization Checklist'
    ],
    'mobile-app': [
      'Mobile App Development Strategy Guide',
      'iOS vs Android: Platform Selection Guide',
      'Mobile App Monetization Strategies'
    ],
    'ai-ml': [
      'AI Implementation Roadmap for Businesses',
      'Machine Learning Use Cases by Industry',
      'AI Ethics and Compliance Guide'
    ],
    'cybersecurity': [
      'Cybersecurity Assessment Checklist',
      'Data Protection Compliance Guide',
      'Security Architecture Best Practices'
    ],
    'cloud-devops': [
      'Cloud Migration Strategy Guide',
      'DevOps Implementation Roadmap',
      'Cost Optimization in Cloud Infrastructure'
    ],
    'enterprise-solutions': [
      'Enterprise Digital Transformation Guide',
      'Legacy System Modernization Strategies',
      'Enterprise Architecture Planning'
    ]
  };
  
  const serviceSpecificContent = serviceContent[data.serviceInterest] || [
    'Technology Consulting Overview',
    'Project Planning Best Practices'
  ];
  
  return [...baseContent, ...serviceSpecificContent];
}

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate the quote data
    const validation = validateQuoteData(body);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed', 
          details: validation.errors 
        },
        { status: 400 }
      );
    }
    
    // Prepare quote data
    const quoteData: GetQuoteData = {
      ...body,
      name: body.name.trim(),
      email: body.email.toLowerCase().trim(),
      company: body.company?.trim(),
      phone: body.phone?.trim()
    };
    
    // Calculate lead score and nurturing sequence
    const leadScore = calculateLeadGenScore(quoteData);
    const nurturingDetails = getNurturingSequence(quoteData, leadScore);
    const contentRecommendations = getContentRecommendations(quoteData);
    
    // Generate unique quote ID
    const quoteId = `quote_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Prepare payload for n8n webhook
    const n8nPayload = {
      ...quoteData,
      quoteId,
      leadScore,
      ...nurturingDetails,
      contentRecommendations,
      timestamp: new Date().toISOString(),
      source: 'lead_generation_form',
      leadType: 'nurture_qualified',
      formCompletionRate: 100,
      dataQuality: 'medium'
    };
    
    // Send to n8n webhook
    const n8nWebhookUrl = process.env.N8N_GET_QUOTE_WEBHOOK_URL || process.env.N8N_LEAD_CAPTURE_WEBHOOK_URL;
    
    if (n8nWebhookUrl) {
      try {
        const n8nResponse = await fetch(n8nWebhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.N8N_WEBHOOK_SECRET}`
          },
          body: JSON.stringify(n8nPayload)
        });
        
        if (!n8nResponse.ok) {
          console.error('Failed to send quote to n8n:', n8nResponse.statusText);
        }
      } catch (n8nError) {
        console.error('Error sending quote to n8n:', n8nError);
      }
    }
    
    // Prepare response based on timeline and consultation type
    const responseMessage = quoteData.timelineFlexibility === 'immediate'
      ? 'Thank you! Given your immediate timeline, our team will prioritize your request.'
      : 'Thank you! We\'ll send you helpful resources and follow up soon.';

    const nextSteps = [];

    if (quoteData.consultationType === 'video' || quoteData.consultationType === 'phone') {
      nextSteps.push(`We'll contact you within ${nurturingDetails.responseTime} to schedule your ${quoteData.consultationType} consultation`);
    }

    nextSteps.push(
      'You\'ll receive an email with helpful resources tailored to your project type',
      'Our team will send you relevant case studies and guides',
      'We\'ll keep you updated on industry trends and best practices'
    );

    if (quoteData.timelineFlexibility === 'immediate') {
      nextSteps.unshift('A senior consultant will review your request immediately');
    }
    
    return NextResponse.json({
      success: true,
      message: responseMessage,
      quoteId,
      leadScore,
      priority: nurturingDetails.priority,
      nurturingSequence: nurturingDetails.sequence,
      contentRecommendations: contentRecommendations.slice(0, 3), // Return top 3
      nextSteps
    });
    
  } catch (error) {
    console.error('Get quote error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to process quote request'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
