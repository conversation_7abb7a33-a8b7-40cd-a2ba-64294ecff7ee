import { NextRequest, NextResponse } from 'next/server';

// Types for consultation booking data
interface ConsultationBookingData {
  // Contact Information
  name: string;
  email: string;
  phone?: string;
  company?: string;
  jobTitle?: string;
  
  // Consultation Details
  consultationType: 'discovery' | 'technical-review' | 'strategy-session' | 'demo';
  serviceArea: string;
  preferredDate?: string;
  preferredTime?: string;
  timezone?: string;
  duration?: '15min' | '30min' | '45min' | '60min';
  
  // Project Context
  projectDescription?: string;
  currentChallenges?: string;
  desiredOutcomes?: string;
  timeline?: string;
  budgetRange?: string;
  
  // Meeting Preferences
  meetingType: 'video' | 'phone' | 'in-person';
  meetingPlatform?: 'zoom' | 'teams' | 'google-meet' | 'phone';
  
  // Additional Information
  additionalNotes?: string;
  hearAboutUs?: string;
  urgencyLevel?: 'low' | 'medium' | 'high' | 'urgent';
  
  // Context
  pageUrl: string;
  leadSource: string;
}

// Validation function
function validateBookingData(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Required fields
  if (!data.name || typeof data.name !== 'string' || data.name.trim().length < 2) {
    errors.push('Name is required and must be at least 2 characters');
  }
  
  if (!data.email || typeof data.email !== 'string' || !isValidEmail(data.email)) {
    errors.push('Valid email address is required');
  }
  
  if (!data.consultationType || !['discovery', 'technical-review', 'strategy-session', 'demo'].includes(data.consultationType)) {
    errors.push('Valid consultation type is required');
  }
  
  if (!data.serviceArea || typeof data.serviceArea !== 'string') {
    errors.push('Service area is required');
  }
  
  if (!data.meetingType || !['video', 'phone', 'in-person'].includes(data.meetingType)) {
    errors.push('Valid meeting type is required');
  }
  
  // Validate date format if provided
  if (data.preferredDate && !isValidDate(data.preferredDate)) {
    errors.push('Invalid date format. Please use YYYY-MM-DD');
  }
  
  // Validate time format if provided
  if (data.preferredTime && !isValidTime(data.preferredTime)) {
    errors.push('Invalid time format. Please use HH:MM');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function isValidDate(dateString: string): boolean {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime()) && dateString.match(/^\d{4}-\d{2}-\d{2}$/);
}

function isValidTime(timeString: string): boolean {
  return timeString.match(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/) !== null;
}

// Generate meeting agenda based on consultation type and service area
function generateMeetingAgenda(consultationType: string, serviceArea: string): string[] {
  const baseAgenda = [
    'Introductions and company overview',
    'Discussion of your current challenges and goals'
  ];
  
  const agendaMap: { [key: string]: { [key: string]: string[] } } = {
    'discovery': {
      'custom-software': [
        ...baseAgenda,
        'Review of current software landscape',
        'Discussion of technical requirements',
        'Timeline and budget considerations',
        'Next steps and proposal timeline'
      ],
      'ai-ml': [
        ...baseAgenda,
        'Current data infrastructure assessment',
        'AI/ML use case identification',
        'Technical feasibility discussion',
        'Implementation roadmap planning'
      ],
      'cybersecurity': [
        ...baseAgenda,
        'Current security posture review',
        'Risk assessment discussion',
        'Compliance requirements',
        'Security strategy recommendations'
      ],
      'cloud-devops': [
        ...baseAgenda,
        'Current infrastructure review',
        'Cloud migration strategy',
        'DevOps maturity assessment',
        'Implementation planning'
      ],
      'enterprise-consulting': [
        ...baseAgenda,
        'Enterprise architecture review',
        'Digital transformation goals',
        'Technology roadmap planning',
        'Change management considerations'
      ]
    },
    'technical-review': {
      'default': [
        'Technical requirements deep dive',
        'Architecture and design discussion',
        'Technology stack recommendations',
        'Integration considerations',
        'Performance and scalability planning',
        'Security and compliance requirements'
      ]
    },
    'strategy-session': {
      'default': [
        'Business objectives alignment',
        'Technology strategy development',
        'Digital transformation roadmap',
        'Resource and timeline planning',
        'ROI and success metrics',
        'Implementation phases'
      ]
    },
    'demo': {
      'default': [
        'Solution demonstration',
        'Feature walkthrough',
        'Customization possibilities',
        'Integration capabilities',
        'Q&A session',
        'Next steps discussion'
      ]
    }
  };
  
  return agendaMap[consultationType]?.[serviceArea] || agendaMap[consultationType]?.['default'] || baseAgenda;
}

// Determine consultation priority and routing
function getConsultationPriority(data: ConsultationBookingData): {
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignedTeam: string;
  responseTime: string;
} {
  let priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium';
  let assignedTeam = 'general-sales';
  let responseTime = '24 hours';
  
  // Priority based on urgency level
  if (data.urgencyLevel) {
    priority = data.urgencyLevel;
  }
  
  // Adjust priority based on consultation type
  if (data.consultationType === 'strategy-session') {
    priority = priority === 'low' ? 'medium' : priority;
    assignedTeam = 'senior-consultants';
  }
  
  // Adjust priority based on service area
  if (data.serviceArea === 'enterprise-consulting') {
    priority = priority === 'low' ? 'medium' : priority;
    assignedTeam = 'enterprise-team';
  }
  
  // Adjust response time based on priority
  switch (priority) {
    case 'urgent':
      responseTime = '2 hours';
      break;
    case 'high':
      responseTime = '4 hours';
      break;
    case 'medium':
      responseTime = '12 hours';
      break;
    default:
      responseTime = '24 hours';
  }
  
  return { priority, assignedTeam, responseTime };
}

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate the booking data
    const validation = validateBookingData(body);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed', 
          details: validation.errors 
        },
        { status: 400 }
      );
    }
    
    // Prepare booking data
    const bookingData: ConsultationBookingData = {
      ...body,
      name: body.name.trim(),
      email: body.email.toLowerCase().trim(),
    };
    
    // Get consultation details
    const consultationDetails = getConsultationPriority(bookingData);
    const meetingAgenda = generateMeetingAgenda(bookingData.consultationType, bookingData.serviceArea);
    
    // Generate unique booking ID
    const bookingId = `booking_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Prepare payload for n8n webhook
    const n8nPayload = {
      ...bookingData,
      bookingId,
      ...consultationDetails,
      meetingAgenda,
      timestamp: new Date().toISOString(),
      source: 'website_consultation_form',
      status: 'pending_confirmation'
    };
    
    // Send to n8n webhook
    const n8nWebhookUrl = process.env.N8N_CONSULTATION_WEBHOOK_URL;
    
    if (n8nWebhookUrl) {
      try {
        const n8nResponse = await fetch(n8nWebhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.N8N_WEBHOOK_SECRET}`
          },
          body: JSON.stringify(n8nPayload)
        });
        
        if (!n8nResponse.ok) {
          console.error('Failed to send booking to n8n:', n8nResponse.statusText);
        }
      } catch (n8nError) {
        console.error('Error sending booking to n8n:', n8nError);
      }
    }
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Consultation booking request received successfully',
      bookingId,
      consultationDetails: {
        type: bookingData.consultationType,
        serviceArea: bookingData.serviceArea,
        priority: consultationDetails.priority,
        expectedResponseTime: consultationDetails.responseTime,
        assignedTeam: consultationDetails.assignedTeam
      },
      nextSteps: [
        'You will receive a confirmation email shortly',
        `Our ${consultationDetails.assignedTeam} will contact you within ${consultationDetails.responseTime}`,
        'A calendar invitation will be sent once the meeting is confirmed',
        'Please check your email for pre-meeting preparation materials'
      ]
    });
    
  } catch (error) {
    console.error('Consultation booking error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to process consultation booking'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
