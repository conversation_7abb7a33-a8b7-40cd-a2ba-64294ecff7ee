import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';

// Types for lead capture data
interface LeadCaptureData {
  // Contact Information
  name: string;
  email: string;
  phone?: string;
  company?: string;
  
  // Project Details
  serviceType: string;
  projectDescription?: string;
  timeline?: string;
  budgetRange?: string;
  
  // Company Information
  companySize?: string;
  industry?: string;
  currentTechStack?: string[];
  
  // Lead Source & Context
  leadSource: string;
  referralSource?: string;
  pageUrl: string;
  utmParams?: {
    source?: string;
    medium?: string;
    campaign?: string;
    term?: string;
    content?: string;
  };
  
  // Urgency & Qualification
  urgencyLevel?: 'low' | 'medium' | 'high' | 'urgent';
  decisionTimeframe?: string;
  decisionMakers?: string;
  
  // Additional Context
  additionalNotes?: string;
  preferredContactMethod?: 'email' | 'phone' | 'video_call';
  preferredContactTime?: string;
}

// Validation function
function validateLeadData(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Required fields
  if (!data.name || typeof data.name !== 'string' || data.name.trim().length < 2) {
    errors.push('Name is required and must be at least 2 characters');
  }
  
  if (!data.email || typeof data.email !== 'string' || !isValidEmail(data.email)) {
    errors.push('Valid email address is required');
  }
  
  if (!data.serviceType || typeof data.serviceType !== 'string') {
    errors.push('Service type is required');
  }
  
  if (!data.leadSource || typeof data.leadSource !== 'string') {
    errors.push('Lead source is required');
  }
  
  if (!data.pageUrl || typeof data.pageUrl !== 'string') {
    errors.push('Page URL is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Calculate lead score based on provided data
function calculateLeadScore(data: LeadCaptureData): number {
  let score = 0;
  
  // Service type scoring
  const serviceScores: { [key: string]: number } = {
    'enterprise-consulting': 25,
    'custom-software': 20,
    'ai-ml': 20,
    'cybersecurity': 15,
    'cloud-devops': 15,
    'general-inquiry': 5
  };
  score += serviceScores[data.serviceType] || 5;
  
  // Company size scoring
  const companySizeScores: { [key: string]: number } = {
    'enterprise': 25,
    'large': 20,
    'medium': 15,
    'small': 10,
    'startup': 5
  };
  if (data.companySize) {
    score += companySizeScores[data.companySize] || 5;
  }
  
  // Budget range scoring
  const budgetScores: { [key: string]: number } = {
    '500k+': 25,
    '250k-500k': 20,
    '100k-250k': 15,
    '50k-100k': 10,
    '25k-50k': 5,
    'under-25k': 2
  };
  if (data.budgetRange) {
    score += budgetScores[data.budgetRange] || 0;
  }
  
  // Timeline urgency scoring
  const timelineScores: { [key: string]: number } = {
    'immediate': 20,
    '1-month': 15,
    '3-months': 10,
    '6-months': 5,
    '1-year+': 2
  };
  if (data.timeline) {
    score += timelineScores[data.timeline] || 0;
  }
  
  // Additional factors
  if (data.phone) score += 5; // Phone number provided
  if (data.company) score += 5; // Company name provided
  if (data.projectDescription && data.projectDescription.length > 50) score += 5; // Detailed description
  
  return Math.min(score, 100); // Cap at 100
}

// Determine lead priority based on score and urgency
function getLeadPriority(score: number, urgencyLevel?: string): 'low' | 'medium' | 'high' | 'urgent' {
  if (urgencyLevel === 'urgent' || score >= 80) return 'urgent';
  if (score >= 60) return 'high';
  if (score >= 40) return 'medium';
  return 'low';
}

export async function POST(request: NextRequest) {
  try {
    // Verify request origin (optional security measure)
    const headersList = headers();
    const origin = headersList.get('origin');
    const referer = headersList.get('referer');
    
    // Parse request body
    const body = await request.json();
    
    // Validate the lead data
    const validation = validateLeadData(body);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed', 
          details: validation.errors 
        },
        { status: 400 }
      );
    }
    
    // Prepare lead data
    const leadData: LeadCaptureData = {
      ...body,
      name: body.name.trim(),
      email: body.email.toLowerCase().trim(),
    };
    
    // Calculate lead score and priority
    const leadScore = calculateLeadScore(leadData);
    const leadPriority = getLeadPriority(leadScore, leadData.urgencyLevel);
    
    // Prepare payload for n8n webhook
    const n8nPayload = {
      ...leadData,
      leadScore,
      leadPriority,
      timestamp: new Date().toISOString(),
      source: 'website_form',
      processed: false
    };
    
    // Send to n8n webhook (replace with your actual n8n webhook URL)
    const n8nWebhookUrl = process.env.N8N_LEAD_CAPTURE_WEBHOOK_URL;
    
    if (n8nWebhookUrl) {
      try {
        const n8nResponse = await fetch(n8nWebhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.N8N_WEBHOOK_SECRET}` // Optional auth
          },
          body: JSON.stringify(n8nPayload)
        });
        
        if (!n8nResponse.ok) {
          console.error('Failed to send data to n8n:', n8nResponse.statusText);
          // Continue processing even if n8n fails
        }
      } catch (n8nError) {
        console.error('Error sending to n8n:', n8nError);
        // Continue processing even if n8n fails
      }
    }
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Lead captured successfully',
      leadId: `lead_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      leadScore,
      leadPriority,
      nextSteps: getNextSteps(leadPriority, leadData.serviceType)
    });
    
  } catch (error) {
    console.error('Lead capture error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to process lead capture'
      },
      { status: 500 }
    );
  }
}

// Helper function to determine next steps based on lead priority and service type
function getNextSteps(priority: string, serviceType: string): string[] {
  const baseSteps = [
    'Thank you for your interest in our services',
    'We will review your requirements and get back to you soon'
  ];
  
  if (priority === 'urgent' || priority === 'high') {
    return [
      'Thank you for your interest in our services',
      'A senior consultant will contact you within 2 hours',
      'Please check your email for a calendar link to schedule a discovery call'
    ];
  }
  
  if (priority === 'medium') {
    return [
      'Thank you for your interest in our services',
      'Our team will contact you within 24 hours',
      'You will receive a detailed information packet about our services'
    ];
  }
  
  return [
    'Thank you for your interest in our services',
    'We will contact you within 48 hours',
    'You will receive helpful resources related to your inquiry'
  ];
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
