import { NextRequest, NextResponse } from 'next/server';

// Types for project estimation data
interface ProjectEstimationData {
  // Contact Information
  name: string;
  email: string;
  phone?: string;
  company: string;
  jobTitle?: string;
  
  // Project Overview
  projectName?: string;
  projectType: string;
  projectDescription: string;
  businessObjectives?: string;
  
  // Technical Requirements
  platforms?: string[];
  integrations?: string[];
  userBase?: string;
  performanceRequirements?: string;
  securityRequirements?: string;
  complianceRequirements?: string[];
  
  // Project Scope
  features?: string[];
  complexity: 'simple' | 'moderate' | 'complex' | 'enterprise';
  timeline: string;
  budgetRange: string;
  
  // Current State
  existingSystems?: string;
  currentTechStack?: string[];
  dataVolume?: string;
  currentChallenges?: string;
  
  // Team & Resources
  internalTeamSize?: string;
  technicalExpertise?: string;
  projectManager?: boolean;
  
  // Decision Making
  decisionTimeframe?: string;
  decisionMakers?: string;
  approvalProcess?: string;
  
  // Additional Context
  additionalRequirements?: string;
  preferredStartDate?: string;
  criticalDeadlines?: string;
  
  // Context
  pageUrl: string;
  leadSource: string;
}

// Validation function
function validateEstimationData(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Required fields
  if (!data.name || typeof data.name !== 'string' || data.name.trim().length < 2) {
    errors.push('Name is required and must be at least 2 characters');
  }
  
  if (!data.email || typeof data.email !== 'string' || !isValidEmail(data.email)) {
    errors.push('Valid email address is required');
  }
  
  if (!data.company || typeof data.company !== 'string' || data.company.trim().length < 2) {
    errors.push('Company name is required');
  }
  
  if (!data.projectType || typeof data.projectType !== 'string') {
    errors.push('Project type is required');
  }
  
  if (!data.projectDescription || typeof data.projectDescription !== 'string' || data.projectDescription.trim().length < 20) {
    errors.push('Project description is required and must be at least 20 characters');
  }
  
  if (!data.complexity || !['simple', 'moderate', 'complex', 'enterprise'].includes(data.complexity)) {
    errors.push('Valid complexity level is required');
  }
  
  if (!data.timeline || typeof data.timeline !== 'string') {
    errors.push('Timeline is required');
  }
  
  if (!data.budgetRange || typeof data.budgetRange !== 'string') {
    errors.push('Budget range is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Calculate project estimation score and complexity
function calculateProjectMetrics(data: ProjectEstimationData): {
  estimationScore: number;
  complexityMultiplier: number;
  riskLevel: 'low' | 'medium' | 'high';
  estimatedDuration: string;
  estimatedBudgetRange: string;
} {
  let score = 0;
  let complexityMultiplier = 1;
  let riskLevel: 'low' | 'medium' | 'high' = 'low';
  
  // Base scoring by project type
  const projectTypeScores: { [key: string]: number } = {
    'web-application': 20,
    'mobile-app': 25,
    'enterprise-software': 40,
    'ai-ml-solution': 35,
    'cybersecurity-implementation': 30,
    'cloud-migration': 25,
    'system-integration': 30,
    'custom-development': 20
  };
  score += projectTypeScores[data.projectType] || 20;
  
  // Complexity scoring
  const complexityScores: { [key: string]: { score: number; multiplier: number } } = {
    'simple': { score: 10, multiplier: 1 },
    'moderate': { score: 20, multiplier: 1.5 },
    'complex': { score: 35, multiplier: 2.5 },
    'enterprise': { score: 50, multiplier: 4 }
  };
  const complexityData = complexityScores[data.complexity];
  score += complexityData.score;
  complexityMultiplier = complexityData.multiplier;
  
  // Platform complexity
  if (data.platforms && data.platforms.length > 2) {
    score += 15;
    complexityMultiplier *= 1.2;
  }
  
  // Integration complexity
  if (data.integrations && data.integrations.length > 3) {
    score += 20;
    complexityMultiplier *= 1.3;
  }
  
  // User base impact
  const userBaseScores: { [key: string]: number } = {
    'small': 5,
    'medium': 10,
    'large': 20,
    'enterprise': 30
  };
  if (data.userBase) {
    score += userBaseScores[data.userBase] || 5;
  }
  
  // Compliance requirements
  if (data.complianceRequirements && data.complianceRequirements.length > 0) {
    score += data.complianceRequirements.length * 10;
    complexityMultiplier *= 1.2;
  }
  
  // Risk assessment
  if (score >= 80 || complexityMultiplier >= 3) {
    riskLevel = 'high';
  } else if (score >= 50 || complexityMultiplier >= 2) {
    riskLevel = 'medium';
  }
  
  // Estimate duration based on complexity and score
  const baseDuration = {
    'simple': '2-4 months',
    'moderate': '4-8 months',
    'complex': '8-18 months',
    'enterprise': '12-36 months'
  };
  
  // Estimate budget range based on complexity and requirements
  const baseBudget = {
    'simple': '$25K - $75K',
    'moderate': '$75K - $200K',
    'complex': '$200K - $500K',
    'enterprise': '$500K - $2M+'
  };
  
  return {
    estimationScore: Math.min(score, 100),
    complexityMultiplier,
    riskLevel,
    estimatedDuration: baseDuration[data.complexity],
    estimatedBudgetRange: baseBudget[data.complexity]
  };
}

// Generate estimation report
function generateEstimationReport(data: ProjectEstimationData, metrics: any): {
  summary: string;
  keyConsiderations: string[];
  recommendedApproach: string[];
  nextSteps: string[];
} {
  const summary = `Based on your ${data.complexity} ${data.projectType} project requirements, we estimate a ${metrics.estimatedDuration} timeline with a budget range of ${metrics.estimatedBudgetRange}. The project has a ${metrics.riskLevel} risk level.`;
  
  const keyConsiderations = [
    `Project complexity: ${data.complexity}`,
    `Estimated timeline: ${metrics.estimatedDuration}`,
    `Budget range: ${metrics.estimatedBudgetRange}`,
    `Risk level: ${metrics.riskLevel}`
  ];
  
  if (data.integrations && data.integrations.length > 0) {
    keyConsiderations.push(`Integration requirements: ${data.integrations.length} systems`);
  }
  
  if (data.complianceRequirements && data.complianceRequirements.length > 0) {
    keyConsiderations.push(`Compliance requirements: ${data.complianceRequirements.join(', ')}`);
  }
  
  const recommendedApproach = [
    'Detailed requirements analysis and technical discovery',
    'Phased development approach with regular milestones',
    'Agile methodology with bi-weekly sprint reviews'
  ];
  
  if (metrics.riskLevel === 'high') {
    recommendedApproach.push('Risk mitigation planning and contingency strategies');
    recommendedApproach.push('Proof of concept development for critical components');
  }
  
  if (data.complexity === 'enterprise') {
    recommendedApproach.push('Enterprise architecture review and approval');
    recommendedApproach.push('Change management and training planning');
  }
  
  const nextSteps = [
    'Schedule a detailed technical discovery session',
    'Prepare comprehensive project proposal',
    'Conduct stakeholder alignment meeting',
    'Finalize project scope and timeline'
  ];
  
  return {
    summary,
    keyConsiderations,
    recommendedApproach,
    nextSteps
  };
}

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate the estimation data
    const validation = validateEstimationData(body);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed', 
          details: validation.errors 
        },
        { status: 400 }
      );
    }
    
    // Prepare estimation data
    const estimationData: ProjectEstimationData = {
      ...body,
      name: body.name.trim(),
      email: body.email.toLowerCase().trim(),
      company: body.company.trim(),
      projectDescription: body.projectDescription.trim()
    };
    
    // Calculate project metrics
    const projectMetrics = calculateProjectMetrics(estimationData);
    
    // Generate estimation report
    const estimationReport = generateEstimationReport(estimationData, projectMetrics);
    
    // Generate unique estimation ID
    const estimationId = `est_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Prepare payload for n8n webhook
    const n8nPayload = {
      ...estimationData,
      estimationId,
      ...projectMetrics,
      ...estimationReport,
      timestamp: new Date().toISOString(),
      source: 'website_estimation_form',
      status: 'pending_review'
    };
    
    // Send to n8n webhook
    const n8nWebhookUrl = process.env.N8N_ESTIMATION_WEBHOOK_URL;
    
    if (n8nWebhookUrl) {
      try {
        const n8nResponse = await fetch(n8nWebhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.N8N_WEBHOOK_SECRET}`
          },
          body: JSON.stringify(n8nPayload)
        });
        
        if (!n8nResponse.ok) {
          console.error('Failed to send estimation to n8n:', n8nResponse.statusText);
        }
      } catch (n8nError) {
        console.error('Error sending estimation to n8n:', n8nError);
      }
    }
    
    // Return success response with preliminary estimation
    return NextResponse.json({
      success: true,
      message: 'Project estimation request received successfully',
      estimationId,
      preliminaryEstimation: {
        timeline: projectMetrics.estimatedDuration,
        budgetRange: projectMetrics.estimatedBudgetRange,
        complexity: estimationData.complexity,
        riskLevel: projectMetrics.riskLevel
      },
      report: estimationReport,
      nextSteps: [
        'You will receive a detailed estimation report within 24-48 hours',
        'Our technical team will review your requirements',
        'A senior consultant will contact you to discuss the estimation',
        'We will schedule a technical discovery session if needed'
      ]
    });
    
  } catch (error) {
    console.error('Project estimation error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to process project estimation request'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
