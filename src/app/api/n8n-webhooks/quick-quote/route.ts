import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';

// Types for quick quote data
interface QuickQuoteData {
  // Contact Information
  name: string;
  email: string;
  phone?: string;
  company?: string;
  
  // Consultation Preferences
  consultationType: 'video' | 'phone';
  serviceInterest?: string;
  urgency: 'asap' | 'this-week' | 'this-month' | 'exploring';
  
  // Context
  leadSource: string;
  pageUrl: string;
  timestamp: string;
  intent: 'schedule_video_consultation' | 'request_immediate_callback';
  callbackRequested?: boolean;
  
  // Consent
  privacyConsent: boolean;
}

// Validation function
function validateQuickQuoteData(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Required fields
  if (!data.name || typeof data.name !== 'string' || data.name.trim().length < 2) {
    errors.push('Name is required and must be at least 2 characters');
  }
  
  if (!data.email || typeof data.email !== 'string' || !isValidEmail(data.email)) {
    errors.push('Valid email address is required');
  }
  
  if (!data.consultationType || !['video', 'phone'].includes(data.consultationType)) {
    errors.push('Valid consultation type is required');
  }
  
  // Phone required for phone consultations
  if (data.consultationType === 'phone' && (!data.phone || typeof data.phone !== 'string')) {
    errors.push('Phone number is required for phone consultations');
  }
  
  if (!data.urgency || !['asap', 'this-week', 'this-month', 'exploring'].includes(data.urgency)) {
    errors.push('Valid urgency level is required');
  }
  
  if (!data.privacyConsent) {
    errors.push('Privacy consent is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Calculate lead priority based on urgency and consultation type
function calculateLeadPriority(data: QuickQuoteData): {
  priority: 'low' | 'medium' | 'high' | 'urgent';
  responseTime: string;
  callbackTime?: string;
} {
  let priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium';
  let responseTime = '24 hours';
  let callbackTime: string | undefined;
  
  // Priority based on urgency
  switch (data.urgency) {
    case 'asap':
      priority = 'urgent';
      responseTime = '15 minutes';
      callbackTime = 'within 15 minutes';
      break;
    case 'this-week':
      priority = 'high';
      responseTime = '2 hours';
      callbackTime = 'within 2 hours';
      break;
    case 'this-month':
      priority = 'medium';
      responseTime = '24 hours';
      callbackTime = 'within 24 hours';
      break;
    case 'exploring':
      priority = 'low';
      responseTime = '48 hours';
      callbackTime = 'within 48 hours';
      break;
  }
  
  // Phone calls get higher priority
  if (data.consultationType === 'phone' && priority !== 'urgent') {
    priority = priority === 'low' ? 'medium' : 'high';
  }
  
  return { priority, responseTime, callbackTime };
}

// Generate lead score for quick quotes
function calculateQuickLeadScore(data: QuickQuoteData): number {
  let score = 30; // Base score for any quote request
  
  // Urgency scoring
  const urgencyScores = {
    'asap': 40,
    'this-week': 30,
    'this-month': 20,
    'exploring': 10
  };
  score += urgencyScores[data.urgency];
  
  // Consultation type scoring
  if (data.consultationType === 'phone') {
    score += 20; // Phone calls show higher intent
  } else {
    score += 15; // Video calls are also good
  }
  
  // Service interest scoring
  const serviceScores: { [key: string]: number } = {
    'enterprise': 25,
    'custom-software': 20,
    'ai-ml': 20,
    'cybersecurity': 15,
    'cloud-devops': 15,
    'mobile-app': 15,
    'web-app': 10,
    'not-sure': 5
  };
  if (data.serviceInterest) {
    score += serviceScores[data.serviceInterest] || 10;
  }
  
  // Company provided
  if (data.company) {
    score += 10;
  }
  
  // Phone provided (even for video calls)
  if (data.phone) {
    score += 5;
  }
  
  return Math.min(score, 100);
}

// Determine callback workflow based on urgency
function getCallbackWorkflow(urgency: string): {
  workflowType: string;
  delay: number;
  instructions: string[];
} {
  switch (urgency) {
    case 'asap':
      return {
        workflowType: 'immediate_callback',
        delay: 0,
        instructions: [
          'Call immediately - urgent request',
          'Lead expects call within 15 minutes',
          'Be prepared to discuss project immediately'
        ]
      };
    case 'this-week':
      return {
        workflowType: 'priority_callback',
        delay: 300, // 5 minutes delay
        instructions: [
          'High priority callback',
          'Call within 2 hours',
          'Lead is actively looking for solutions'
        ]
      };
    case 'this-month':
      return {
        workflowType: 'standard_callback',
        delay: 1800, // 30 minutes delay
        instructions: [
          'Standard callback timing',
          'Call within 24 hours',
          'Lead has medium urgency'
        ]
      };
    default:
      return {
        workflowType: 'exploratory_callback',
        delay: 3600, // 1 hour delay
        instructions: [
          'Exploratory callback',
          'Call within 48 hours',
          'Lead is in research phase'
        ]
      };
  }
}

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate the quote data
    const validation = validateQuickQuoteData(body);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed', 
          details: validation.errors 
        },
        { status: 400 }
      );
    }
    
    // Prepare quote data
    const quoteData: QuickQuoteData = {
      ...body,
      name: body.name.trim(),
      email: body.email.toLowerCase().trim(),
      phone: body.phone?.trim(),
      company: body.company?.trim()
    };
    
    // Calculate lead metrics
    const leadPriority = calculateLeadPriority(quoteData);
    const leadScore = calculateQuickLeadScore(quoteData);
    
    // Generate unique quote ID
    const quoteId = `quote_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Prepare different payloads based on consultation type
    let n8nPayload;
    
    if (quoteData.consultationType === 'phone') {
      // Phone callback workflow
      const callbackWorkflow = getCallbackWorkflow(quoteData.urgency);
      
      n8nPayload = {
        ...quoteData,
        quoteId,
        leadScore,
        ...leadPriority,
        ...callbackWorkflow,
        timestamp: new Date().toISOString(),
        source: 'quick_quote_phone',
        workflowType: 'phone_callback',
        callbackRequested: true
      };
    } else {
      // Video consultation workflow
      n8nPayload = {
        ...quoteData,
        quoteId,
        leadScore,
        ...leadPriority,
        timestamp: new Date().toISOString(),
        source: 'quick_quote_video',
        workflowType: 'video_consultation_lead',
        calendlyIntegration: true
      };
    }
    
    // Send to appropriate n8n webhook
    const webhookUrl = quoteData.consultationType === 'phone' 
      ? process.env.N8N_PHONE_CALLBACK_WEBHOOK_URL || process.env.N8N_CONSULTATION_WEBHOOK_URL
      : process.env.N8N_VIDEO_CONSULTATION_WEBHOOK_URL || process.env.N8N_CONSULTATION_WEBHOOK_URL;
    
    if (webhookUrl) {
      try {
        const n8nResponse = await fetch(webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.N8N_WEBHOOK_SECRET}`
          },
          body: JSON.stringify(n8nPayload)
        });
        
        if (!n8nResponse.ok) {
          console.error('Failed to send quote to n8n:', n8nResponse.statusText);
        }
      } catch (n8nError) {
        console.error('Error sending quote to n8n:', n8nError);
      }
    }
    
    // Prepare response based on consultation type
    const responseData = {
      success: true,
      message: quoteData.consultationType === 'phone' 
        ? 'Callback request received successfully'
        : 'Video consultation request received successfully',
      quoteId,
      leadScore,
      priority: leadPriority.priority,
      consultationType: quoteData.consultationType
    };
    
    if (quoteData.consultationType === 'phone') {
      responseData['callbackTime'] = leadPriority.callbackTime;
      responseData['nextSteps'] = [
        `We'll call you ${leadPriority.callbackTime}`,
        'Please keep your phone available',
        'Have your project details ready to discuss',
        'The call will take about 15-30 minutes'
      ];
    } else {
      responseData['nextSteps'] = [
        'Please select a convenient time from the calendar',
        'You\'ll receive a confirmation email with meeting details',
        'We\'ll send you a brief questionnaire before the call',
        'The consultation will take about 30-45 minutes'
      ];
    }
    
    return NextResponse.json(responseData);
    
  } catch (error) {
    console.error('Quick quote error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to process quote request'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
