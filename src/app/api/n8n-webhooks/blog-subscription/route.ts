import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.email || !body.firstName) {
      return NextResponse.json(
        { error: 'Email and first name are required' },
        { status: 400 }
      );
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Prepare data for n8n
    const subscriptionData = {
      type: 'blog-subscription',
      email: body.email,
      firstName: body.firstName,
      interests: body.interests || '',
      source: body.source || 'unknown',
      timestamp: body.timestamp || new Date().toISOString(),
      userAgent: body.userAgent || '',
      url: body.url || '',
      analyticsEvent: body.analyticsEvent || 'blog_subscription',
      // Additional metadata
      ipAddress: request.headers.get('x-forwarded-for') || 
                 request.headers.get('x-real-ip') || 
                 'unknown',
      referer: request.headers.get('referer') || '',
    };

    // Send to n8n webhook
    const n8nWebhookUrl = process.env.N8N_BLOG_SUBSCRIPTION_WEBHOOK_URL;
    
    if (!n8nWebhookUrl) {
      console.error('N8N_BLOG_SUBSCRIPTION_WEBHOOK_URL not configured');
      return NextResponse.json(
        { error: 'Blog subscription service temporarily unavailable' },
        { status: 503 }
      );
    }

    const n8nResponse = await fetch(n8nWebhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(subscriptionData),
    });

    if (!n8nResponse.ok) {
      console.error('n8n webhook failed:', n8nResponse.status, n8nResponse.statusText);
      return NextResponse.json(
        { error: 'Failed to process subscription' },
        { status: 500 }
      );
    }

    // Log successful subscription (you might want to store this in a database)
    console.log('Blog subscription successful:', {
      email: body.email,
      firstName: body.firstName,
      source: body.source,
      timestamp: subscriptionData.timestamp
    });

    return NextResponse.json(
      { 
        success: true, 
        message: 'Successfully subscribed to blog updates',
        email: body.email
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Blog subscription API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
