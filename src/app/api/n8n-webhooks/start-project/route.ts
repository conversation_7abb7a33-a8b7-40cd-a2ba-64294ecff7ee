import { NextRequest, NextResponse } from 'next/server';

// Types for high-intent project data
interface StartProjectData {
  // Contact & Company Information
  name: string;
  email: string;
  phone?: string;
  jobTitle: string;
  company: string;
  companySize?: string;
  industry?: string;
  
  // Project Details
  projectName?: string;
  projectType: string;
  projectDescription: string;
  businessObjectives: string;
  
  // Technical Requirements
  platforms?: string[];
  integrations?: string[];
  userBase?: string;
  performanceRequirements?: string;
  securityRequirements?: string;
  complianceRequirements?: string[];
  
  // Current State
  existingSystems?: string;
  currentTechStack?: string[];
  currentChallenges?: string;
  
  // Project Scope & Timeline
  projectScope?: string;
  timeline: string;
  budgetRange: string;
  urgencyLevel: string;
  
  // Decision Making
  decisionTimeframe: string;
  decisionMakers: string;
  approvalProcess?: string;
  
  // Team & Resources
  internalTeamSize?: string;
  technicalExpertise?: string;
  projectManager?: boolean;
  
  // Next Steps
  preferredStartDate?: string;
  criticalDeadlines?: string;
  additionalRequirements?: string;
  
  // Context
  leadSource: string;
  pageUrl: string;
  timestamp: string;
  formType: string;
  utmParams?: any;
  
  // Consent
  privacyConsent: boolean;
  marketingConsent?: boolean;
}

// Validation function
function validateProjectData(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Required fields
  if (!data.name || typeof data.name !== 'string' || data.name.trim().length < 2) {
    errors.push('Name is required and must be at least 2 characters');
  }
  
  if (!data.email || typeof data.email !== 'string' || !isValidEmail(data.email)) {
    errors.push('Valid email address is required');
  }
  
  if (!data.jobTitle || typeof data.jobTitle !== 'string') {
    errors.push('Job title is required');
  }
  
  if (!data.company || typeof data.company !== 'string' || data.company.trim().length < 2) {
    errors.push('Company name is required');
  }
  
  if (!data.projectType || typeof data.projectType !== 'string') {
    errors.push('Project type is required');
  }
  
  if (!data.projectDescription || typeof data.projectDescription !== 'string' || data.projectDescription.trim().length < 20) {
    errors.push('Project description is required and must be at least 20 characters');
  }
  
  if (!data.businessObjectives || typeof data.businessObjectives !== 'string' || data.businessObjectives.trim().length < 10) {
    errors.push('Business objectives are required and must be at least 10 characters');
  }
  
  if (!data.timeline || typeof data.timeline !== 'string') {
    errors.push('Timeline is required');
  }
  
  if (!data.budgetRange || typeof data.budgetRange !== 'string') {
    errors.push('Budget range is required');
  }
  
  if (!data.decisionTimeframe || typeof data.decisionTimeframe !== 'string') {
    errors.push('Decision timeframe is required');
  }
  
  if (!data.decisionMakers || typeof data.decisionMakers !== 'string') {
    errors.push('Decision makers information is required');
  }
  
  if (!data.privacyConsent) {
    errors.push('Privacy consent is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Calculate high-intent lead score
function calculateHighIntentScore(data: StartProjectData): number {
  let score = 50; // Base score for high-intent form completion
  
  // Budget range scoring (high weight for enterprise projects)
  const budgetScores: { [key: string]: number } = {
    '2m+': 30,
    '1m-2m': 25,
    '500k-1m': 20,
    '250k-500k': 15,
    '100k-250k': 10,
    '50k-100k': 5,
    'flexible': 15
  };
  score += budgetScores[data.budgetRange] || 5;
  
  // Company size scoring
  const companySizeScores: { [key: string]: number } = {
    'enterprise': 20,
    'large': 15,
    'medium': 10,
    'small': 5,
    'startup': 3
  };
  if (data.companySize) {
    score += companySizeScores[data.companySize] || 5;
  }
  
  // Urgency level scoring
  const urgencyScores: { [key: string]: number } = {
    'urgent': 15,
    'high': 10,
    'medium': 5,
    'low': 2
  };
  score += urgencyScores[data.urgencyLevel] || 5;
  
  // Decision timeframe scoring (faster decision = higher score)
  const decisionScores: { [key: string]: number } = {
    '1-week': 15,
    '2-weeks': 12,
    '1-month': 8,
    '3-months': 5,
    '6-months': 2
  };
  score += decisionScores[data.decisionTimeframe] || 2;
  
  // Project complexity indicators
  if (data.complianceRequirements && data.complianceRequirements.length > 0) {
    score += data.complianceRequirements.length * 3; // Compliance adds complexity/value
  }
  
  if (data.platforms && data.platforms.length > 2) {
    score += 5; // Multi-platform projects
  }
  
  if (data.existingSystems && data.existingSystems.length > 50) {
    score += 5; // Complex integration requirements
  }
  
  // Team readiness indicators
  if (data.projectManager) {
    score += 5; // Dedicated PM shows readiness
  }
  
  if (data.technicalExpertise === 'advanced' || data.technicalExpertise === 'expert') {
    score += 5; // Technical team ready
  }
  
  // Detailed information provided
  if (data.businessObjectives && data.businessObjectives.length > 100) {
    score += 3; // Detailed objectives
  }
  
  if (data.criticalDeadlines && data.criticalDeadlines.length > 20) {
    score += 3; // Specific deadlines
  }
  
  return Math.min(score, 100); // Cap at 100
}

// Determine sales routing based on project characteristics
function getSalesRouting(data: StartProjectData, score: number): {
  priority: 'urgent' | 'high' | 'medium' | 'low';
  assignedTeam: string;
  responseTime: string;
  followUpSequence: string;
} {
  let priority: 'urgent' | 'high' | 'medium' | 'low' = 'medium';
  let assignedTeam = 'senior-sales';
  let responseTime = '4 hours';
  let followUpSequence = 'enterprise-nurture';
  
  // Priority based on score and urgency
  if (score >= 85 || data.urgencyLevel === 'urgent') {
    priority = 'urgent';
    assignedTeam = 'executive-team';
    responseTime = '1 hour';
    followUpSequence = 'executive-engagement';
  } else if (score >= 70 || data.urgencyLevel === 'high') {
    priority = 'high';
    assignedTeam = 'senior-sales';
    responseTime = '2 hours';
    followUpSequence = 'high-value-prospect';
  } else if (score >= 55) {
    priority = 'medium';
    assignedTeam = 'sales-team';
    responseTime = '4 hours';
    followUpSequence = 'qualified-lead';
  } else {
    priority = 'low';
    assignedTeam = 'sales-team';
    responseTime = '8 hours';
    followUpSequence = 'standard-nurture';
  }
  
  // Adjust based on budget range
  if (['2m+', '1m-2m', '500k-1m'].includes(data.budgetRange)) {
    if (priority === 'medium') priority = 'high';
    if (priority === 'low') priority = 'medium';
    assignedTeam = 'senior-sales';
  }
  
  // Enterprise company size gets priority treatment
  if (data.companySize === 'enterprise' || data.companySize === 'large') {
    if (priority === 'low') priority = 'medium';
    assignedTeam = 'enterprise-team';
  }
  
  return { priority, assignedTeam, responseTime, followUpSequence };
}

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate the project data
    const validation = validateProjectData(body);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed', 
          details: validation.errors 
        },
        { status: 400 }
      );
    }
    
    // Prepare project data
    const projectData: StartProjectData = {
      ...body,
      name: body.name.trim(),
      email: body.email.toLowerCase().trim(),
      company: body.company.trim(),
      projectDescription: body.projectDescription.trim(),
      businessObjectives: body.businessObjectives.trim()
    };
    
    // Calculate lead score and routing
    const leadScore = calculateHighIntentScore(projectData);
    const salesRouting = getSalesRouting(projectData, leadScore);
    
    // Generate unique project ID
    const projectId = `proj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Prepare payload for n8n webhook
    const n8nPayload = {
      ...projectData,
      projectId,
      leadScore,
      ...salesRouting,
      timestamp: new Date().toISOString(),
      source: 'high_intent_project_form',
      leadType: 'enterprise_qualified',
      formCompletionRate: 100, // They completed the full form
      dataQuality: 'high'
    };
    
    // Send to n8n webhook
    const n8nWebhookUrl = process.env.N8N_START_PROJECT_WEBHOOK_URL || process.env.N8N_LEAD_CAPTURE_WEBHOOK_URL;
    
    if (n8nWebhookUrl) {
      try {
        const n8nResponse = await fetch(n8nWebhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.N8N_WEBHOOK_SECRET}`
          },
          body: JSON.stringify(n8nPayload)
        });
        
        if (!n8nResponse.ok) {
          console.error('Failed to send project to n8n:', n8nResponse.statusText);
        }
      } catch (n8nError) {
        console.error('Error sending project to n8n:', n8nError);
      }
    }
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Project request submitted successfully',
      projectId,
      leadScore,
      priority: salesRouting.priority,
      responseTime: salesRouting.responseTime,
      nextSteps: [
        `Our ${salesRouting.assignedTeam} will review your project within ${salesRouting.responseTime}`,
        'You will receive a detailed follow-up email with next steps',
        'We will schedule a comprehensive discovery call to discuss your requirements',
        'You will receive a detailed proposal with timeline, team structure, and pricing',
        'We can begin project planning immediately upon approval'
      ]
    });
    
  } catch (error) {
    console.error('Start project error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to process project request'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
