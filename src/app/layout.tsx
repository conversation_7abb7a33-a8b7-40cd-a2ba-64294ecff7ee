"use client";

import Footer from "@/components/Footer";
import Header from "@/components/Header";
import ScrollToTop from "@/components/ScrollToTop";
import { SessionProvider } from "next-auth/react";
import { ThemeProvider } from "next-themes";
import { HeroUIProvider } from "@heroui/react";
import "../styles/index.css";
import "../styles/prism.css";
import ToasterContext from "./api/contex/ToasetContex";
import { useEffect, useState } from "react";
import PreLoader from "@/components/Common/PreLoader";
import { preloadCriticalResources, addResourceHints, trackWebVitals } from "@/utils/performance";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // Initialize performance optimizations
    preloadCriticalResources();
    addResourceHints();
    trackWebVitals();

    setTimeout(() => setLoading(false), 1000);
  }, []);

  return (
    <html suppressHydrationWarning={true} className="!scroll-smooth" lang="en">
      {/*
        <head /> will contain the components returned by the nearest parent
        head.js. Find out more at https://beta.nextjs.org/docs/api-reference/file-conventions/head
      */}
      <head />

      <body className="antialiased">{/* Added antialiased for better text rendering */}
        {loading ? (
          <PreLoader />
        ) : (
          <SessionProvider>
            <HeroUIProvider>
              <ThemeProvider
                attribute="class"
                enableSystem={false}
                defaultTheme="light"
              >
                <ToasterContext />
                <Header />
                {children}
                <Footer />
                <ScrollToTop />
              </ThemeProvider>
            </HeroUIProvider>
          </SessionProvider>
        )}
      </body>
    </html>
  );
}
