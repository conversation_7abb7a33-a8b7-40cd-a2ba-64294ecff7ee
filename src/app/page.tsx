import About from "@/components/About";
import HomeBlogSection from "@/components/Blog/HomeBlogSection";
import CallToAction from "@/components/CallToAction";
import Clients from "@/components/Clients";
import ScrollUp from "@/components/Common/ScrollUp";
import Contact from "@/components/Contact";
import Faq from "@/components/Faq";
import Features from "@/components/Features";
import Hero from "@/components/Hero";
import Pricing from "@/components/Pricing";
import Team from "@/components/Team";
import { SpeedInsights } from "@vercel/speed-insights/next";
import Testimonials from "@/components/Testimonials";
import { getAllPosts } from "@/utils/markdown";
import { Metadata } from "next";
import { getOrganizationStructuredData } from "@/utils/seo";

export const metadata: Metadata = {
  title: "Digital Wave Systems | Full-Service Software Development Company",
  description: "Leading software development company specializing in custom software solutions, enterprise applications, AI/ML integration, cloud/DevOps services, and full-stack development. Transform your business with our expert development team.",
  keywords: "software development company, custom software solutions, enterprise software consulting, full-stack development, DevOps services, software engineering, web application development, mobile app development, cloud solutions, AI integration, software consulting services, enterprise applications, custom software development, technology consulting",
  authors: [{ name: "Digital Wave Systems" }],
  creator: "Digital Wave Systems",
  publisher: "Digital Wave Systems",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://digitalwavesystems.com.co',
    siteName: 'Digital Wave Systems',
    title: "Digital Wave Systems | Full-Service Software Development Company",
    description: "Leading software development company delivering custom software solutions, enterprise applications, and full-stack development services for businesses worldwide.",
    images: [
      {
        url: '/images/logo/favicon.svg',
        width: 1200,
        height: 630,
        alt: 'Digital Wave Systems - Software Consulting & AI Development',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: "Digital Wave Systems | Full-Service Software Development Company",
    description: "Leading software development company delivering custom software solutions, enterprise applications, and full-stack development services.",
    images: ['/images/logo/favicon.svg'],
    creator: '@digitalwavesys',
  },
  alternates: {
    canonical: 'https://digitalwavesystems.com.co',
  },
  category: 'Technology',
};

export default function Home() {
  const posts = getAllPosts(["title", "date", "excerpt", "coverImage", "slug"]);
  const organizationData = getOrganizationStructuredData();

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationData) }}
      />
      <main>
        <ScrollUp />
        <Hero />
        <Features />
        <About />
        <Pricing />
        <Team />
        <CallToAction />
        <Testimonials />
        <Faq />
        <HomeBlogSection posts={posts} />
        <Contact />
        <Clients />
        <SpeedInsights />
      </main>
    </>
  );
}
