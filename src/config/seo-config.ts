/**
 * SEO Configuration for Digital Wave Systems
 * Centralized configuration for all SEO-related settings
 */

export const SEO_CONFIG = {
  // Site Information
  siteName: 'Digital Wave Systems',
  siteUrl: 'https://digitalwavesystems.com.co',
  defaultLocale: 'en',
  supportedLocales: ['en', 'es'],
  
  // Company Information
  company: {
    name: 'Digital Wave Systems',
    alternateName: 'DWS',
    description: 'Leading software development company delivering custom software solutions, enterprise applications, and AI integration services.',
    foundingDate: '2020',
    address: {
      country: 'CO',
      region: 'Colombia'
    },
    contact: {
      telephone: '+57-XXX-XXX-XXXX',
      email: '<EMAIL>',
      availableLanguages: ['English', 'Spanish']
    },
    social: {
      twitter: '@digitalwavesys',
      linkedin: 'https://linkedin.com/company/digitalwavesystems',
      github: 'https://github.com/digitalwavesystems'
    }
  },

  // Default SEO Settings
  defaults: {
    title: 'Digital Wave Systems | Full-Service Software Development Company',
    description: 'Leading software development company delivering custom software solutions, enterprise applications, and full-stack development services for businesses worldwide.',
    keywords: 'software consulting, custom software development, enterprise software solutions, software development company, digital transformation, AI integration services, machine learning consulting, cloud architecture, DevOps consulting, cybersecurity solutions',
    image: '/images/logo/Logo.svg',
    type: 'website' as const
  },

  // Google Services
  google: {
    // Replace with actual verification codes from Google Search Console
    siteVerification: 'your-google-verification-code-here',
    analyticsId: 'G-XXXXXXXXXX', // Replace with actual GA4 ID
    tagManagerId: 'GTM-XXXXXXX', // Replace with actual GTM ID
  },

  // Social Media
  social: {
    twitter: {
      site: '@digitalwavesys',
      creator: '@digitalwavesys'
    },
    facebook: {
      appId: 'your-facebook-app-id' // If using Facebook integration
    }
  },

  // Robots Configuration
  robots: {
    default: {
      index: true,
      follow: true,
      noarchive: false,
      nosnippet: false,
      noimageindex: false
    },
    // Special configurations for specific page types
    blog: {
      index: true,
      follow: true,
      noarchive: false,
      nosnippet: false,
      noimageindex: false
    },
    admin: {
      index: false,
      follow: false,
      noarchive: true,
      nosnippet: true,
      noimageindex: true
    }
  },

  // Sitemap Configuration
  sitemap: {
    changefreq: {
      home: 'weekly',
      services: 'weekly',
      about: 'monthly',
      blog: 'weekly',
      blogPost: 'monthly',
      contact: 'monthly',
      pricing: 'monthly'
    },
    priority: {
      home: 1.0,
      services: 0.9,
      about: 0.8,
      blog: 0.8,
      blogPost: 0.6,
      contact: 0.7,
      pricing: 0.7
    }
  },

  // Structured Data
  structuredData: {
    organization: {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: 'Digital Wave Systems',
      alternateName: 'DWS',
      url: 'https://digitalwavesystems.com.co',
      logo: 'https://digitalwavesystems.com.co/images/logo/Logo.svg',
      sameAs: [
        'https://linkedin.com/company/digitalwavesystems',
        'https://twitter.com/digitalwavesys'
      ]
    }
  }
};

// Environment-specific configurations
export const getEnvironmentConfig = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isProduction = process.env.NODE_ENV === 'production';

  return {
    // In development, we might want to prevent indexing
    robots: isDevelopment ? {
      index: false,
      follow: false
    } : SEO_CONFIG.robots.default,
    
    // Use different verification codes for staging vs production
    googleVerification: isProduction 
      ? SEO_CONFIG.google.siteVerification 
      : 'development-verification-code',
      
    // Different base URLs for different environments
    baseUrl: isProduction 
      ? SEO_CONFIG.siteUrl 
      : 'http://localhost:3000'
  };
};

export default SEO_CONFIG;
