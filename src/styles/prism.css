/**
 * DWS Website code highlighting theme - based on VS Code dark+ with blue accents
 */

pre[class*="language-"],
code[class*="language-"] {
  color: #d4d4d4;
  font-size: 14px;
  text-shadow: none;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  direction: ltr;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  line-height: 1.5;
  -moz-tab-size: 2;
  -o-tab-size: 2;
  tab-size: 2;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}

pre[class*="language-"]::selection,
code[class*="language-"]::selection,
pre[class*="language-"] *::selection,
code[class*="language-"] *::selection {
  text-shadow: none;
  background: #264f78;
}

@media print {
  pre[class*="language-"],
  code[class*="language-"] {
    text-shadow: none;
  }
}

pre[class*="language-"] {
  padding: 1.25rem;
  margin: 1.5rem 0;
  overflow: auto;
  background: #1e1e1e;
  border-radius: 0.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(74, 108, 247, 0.2);
}

.code-block-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #2d2d2d;
  color: #d4d4d4;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  border-bottom: 1px solid rgba(74, 108, 247, 0.2);
}

.code-block-header + pre[class*="language-"] {
  margin-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

:not(pre) > code[class*="language-"] {
  padding: 0.2em 0.3em;
  border-radius: 0.3em;
  color: #4A6CF7;
  background: rgba(74, 108, 247, 0.1);
  white-space: normal;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #6a9955;
}

.token.punctuation {
  color: #d4d4d4;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
  color: #b5cea8;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #ce9178;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
  color: #d4d4d4;
  background: transparent;
}

.token.atrule,
.token.attr-value,
.token.keyword {
  color: #4A6CF7;
}

.token.function {
  color: #dcdcaa;
}

.token.regex,
.token.important,
.token.variable {
  color: #d16969;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.constant {
  color: #9cdcfe;
}

.token.class-name {
  color: #4ec9b0;
}

.token.parameter {
  color: #9cdcfe;
}

.token.interpolation {
  color: #9cdcfe;
}

.token.punctuation.interpolation-punctuation {
  color: #569cd6;
}

.token.namespace {
  opacity: 0.7;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #6a9955;
}

pre[class*="language-"] > code[class*="language-"] {
  position: relative;
}

.line-highlight {
  position: absolute;
  left: 0;
  right: 0;
  margin-top: 1em;
  background: rgba(74, 108, 247, 0.1);
  pointer-events: none;
  line-height: inherit;
  white-space: pre;
}

.line-numbers .line-numbers-rows {
  position: absolute;
  pointer-events: none;
  top: 0;
  font-size: 100%;
  left: -3.8em;
  width: 3em;
  letter-spacing: -1px;
  border-right: 1px solid rgba(74, 108, 247, 0.2);
  user-select: none;
}

.line-numbers-rows > span {
  display: block;
  counter-increment: linenumber;
}

.line-numbers-rows > span:before {
  content: counter(linenumber);
  color: #999;
  display: block;
  padding-right: 0.8em;
  text-align: right;
}

/* Copy Button */
.copy-to-clipboard-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  background: rgba(74, 108, 247, 0.2);
  color: #fff;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s, background-color 0.2s;
}

pre:hover .copy-to-clipboard-button {
  opacity: 1;
}

.copy-to-clipboard-button:hover {
  background: rgba(74, 108, 247, 0.5);
}

.copy-to-clipboard-button.copied {
  background: #4A6CF7;
}

/* Dark mode adjustments */
.dark pre[class*="language-"] {
  background: #1a1a1a;
  border-color: rgba(74, 108, 247, 0.3);
}

.dark .code-block-header {
  background: #252525;
  border-color: rgba(74, 108, 247, 0.3);
}

.dark :not(pre) > code[class*="language-"] {
  background: rgba(74, 108, 247, 0.15);
  color: #6d8eff;
} 