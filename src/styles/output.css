@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap");

*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: Inter, sans-serif;
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

.container{
  margin-left: auto;
  margin-r-ight: auto;
  padding-left: 16px;
  padding-r-ight: 16px;
}

input[type="radio"]:checked ~ .box .circle{
  background: #4A6CF7;
}

.shipping:checked ~ label .title{
  color: #4A6CF7;
}

.download-radio:checked ~ label .icon{
  opacity: 1;
}

.checkbox-list:checked ~ label .icon{
  opacity: 1;
}

.box-select-1:checked ~ label .box .icon{
  opacity: 1;
}

.select-list:checked ~ label .icon{
  background: #4A6CF7;
}

.tableCheckbox:checked ~ label .icon{
  opacity: 1;
}

.tableCheckbox-2:checked ~ label .icon{
  color: #fff;
  opacity: 1;
}

.container{
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 16px;
  padding-left: 16px;
}

@media (min-width: 400px){
  .container{
    max-width: 400px;
  }
}

@media (min-width: 540px){
  .container{
    max-width: 540px;
  }
}

@media (min-width: 720px){
  .container{
    max-width: 720px;
  }
}

@media (min-width: 960px){
  .container{
    max-width: 960px;
  }
}

@media (min-width: 1140px){
  .container{
    max-width: 1140px;
  }
}

@media (min-width: 1320px){
  .container{
    max-width: 1320px;
  }
}

.sticky-menu .navbar-logo{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

#navbarCollapse li .ud-menu-scroll.active{
  opacity: 0.7;
}

.sticky-menu #navbarCollapse li .ud-menu-scroll.active{
  --tw-text-opacity: 1;
  color: rgb(74 108 247 / var(--tw-text-opacity, 1));
  opacity: 1;
}

.sticky-menu #navbarCollapse li .ud-menu-scroll.active:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.blog-details,
  .blog-details p{
  font-size: 1rem;
  line-height: 1.5rem;
  line-height: 1.625;
  --tw-text-opacity: 1;
  color: rgb(149 156 177 / var(--tw-text-opacity, 1));
}

.blog-details:is(.dark *),
  .blog-details p:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(136 144 173 / var(--tw-text-opacity, 1));
}

.blog-details p{
  margin-bottom: 2rem;
}

.blog-details strong{
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(29 33 68 / var(--tw-text-opacity, 1));
}

.blog-details strong:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.blog-details ul{
  margin-bottom: 2rem;
  list-style-position: inside;
  list-style-type: disc;
}

.blog-details ul > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.blog-details ol{
  margin-bottom: 2rem;
  list-style-position: inside;
  list-style-type: decimal;
}

.blog-details ol > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.blog-details h1{
  margin-bottom: 2rem;
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(29 33 68 / var(--tw-text-opacity, 1));
}

.blog-details h1:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

@media (min-width: 540px){
  .blog-details h1{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

@media (min-width: 720px){
  .blog-details h1{
    font-size: 40px;
    line-height: 1.28;
  }
}

.blog-details h2{
  margin-bottom: 2rem;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(29 33 68 / var(--tw-text-opacity, 1));
}

.blog-details h2:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

@media (min-width: 540px){
  .blog-details h2{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

@media (min-width: 720px){
  .blog-details h2{
    font-size: 35px;
    line-height: 1.28;
  }
}

.blog-details h3{
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(29 33 68 / var(--tw-text-opacity, 1));
}

.blog-details h3:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

@media (min-width: 540px){
  .blog-details h3{
    font-size: 28px;
    line-height: 40px;
  }
}

.invisible{
  visibility: hidden;
}

.fixed{
  position: fixed;
}

.absolute{
  position: absolute;
}

.relative{
  position: relative;
}

.sticky{
  position: sticky;
}

.inset-0{
  inset: 0px;
}

.-bottom-2{
  bottom: -0.5rem;
}

.-right-11{
  right: -2.75rem;
}

.-top-11{
  top: -2.75rem;
}

.bottom-0{
  bottom: 0px;
}

.bottom-1{
  bottom: 0.25rem;
}

.bottom-4{
  bottom: 1rem;
}

.bottom-8{
  bottom: 2rem;
}

.left-0{
  left: 0px;
}

.left-1{
  left: 0.25rem;
}

.left-4{
  left: 1rem;
}

.right-0{
  right: 0px;
}

.right-1{
  right: 0.25rem;
}

.right-4{
  right: 1rem;
}

.right-8{
  right: 2rem;
}

.right-\[-50px\]{
  right: -50px;
}

.top-0{
  top: 0px;
}

.top-1{
  top: 0.25rem;
}

.top-1\/2{
  top: 50%;
}

.top-4{
  top: 1rem;
}

.top-\[-8px\]{
  top: -8px;
}

.top-\[120\%\]{
  top: 120%;
}

.top-\[60px\]{
  top: 60px;
}

.top-\[7px\]{
  top: 7px;
}

.top-full{
  top: 100%;
}

.-z-10{
  z-index: -10;
}

.-z-\[1\]{
  z-index: -1;
}

.z-10{
  z-index: 10;
}

.z-20{
  z-index: 20;
}

.z-30{
  z-index: 30;
}

.z-40{
  z-index: 40;
}

.z-\[-1\]{
  z-index: -1;
}

.z-\[99999\]{
  z-index: 99999;
}

.z-\[999\]{
  z-index: 999;
}

.-mx-2{
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}

.-mx-3{
  margin-left: -0.75rem;
  margin-right: -0.75rem;
}

.-mx-4{
  margin-left: -1rem;
  margin-right: -1rem;
}

.mx-auto{
  margin-left: auto;
  margin-right: auto;
}

.my-1{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-1\.5{
  margin-top: 0.375rem;
  margin-bottom: 0.375rem;
}

.my-8{
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.-ml-1{
  margin-left: -0.25rem;
}

.mb-0{
  margin-bottom: 0px;
}

.mb-1{
  margin-bottom: 0.25rem;
}

.mb-10{
  margin-bottom: 2.5rem;
}

.mb-11{
  margin-bottom: 2.75rem;
}

.mb-12{
  margin-bottom: 3rem;
}

.mb-2{
  margin-bottom: 0.5rem;
}

.mb-2\.5{
  margin-bottom: 0.625rem;
}

.mb-3{
  margin-bottom: 0.75rem;
}

.mb-4{
  margin-bottom: 1rem;
}

.mb-5{
  margin-bottom: 1.25rem;
}

.mb-6{
  margin-bottom: 1.5rem;
}

.mb-8{
  margin-bottom: 2rem;
}

.mb-9{
  margin-bottom: 2.25rem;
}

.mb-\[18px\]{
  margin-bottom: 18px;
}

.mb-\[22px\]{
  margin-bottom: 22px;
}

.mb-\[30px\]{
  margin-bottom: 30px;
}

.mb-\[50px\]{
  margin-bottom: 50px;
}

.mb-\[60px\]{
  margin-bottom: 60px;
}

.mb-\[6px\]{
  margin-bottom: 6px;
}

.ml-1\.5{
  margin-left: 0.375rem;
}

.ml-2{
  margin-left: 0.5rem;
}

.ml-5{
  margin-left: 1.25rem;
}

.mr-10{
  margin-right: 2.5rem;
}

.mr-2{
  margin-right: 0.5rem;
}

.mr-3{
  margin-right: 0.75rem;
}

.mr-4{
  margin-right: 1rem;
}

.mr-5{
  margin-right: 1.25rem;
}

.mr-6{
  margin-right: 1.5rem;
}

.mt-1{
  margin-top: 0.25rem;
}

.mt-12{
  margin-top: 3rem;
}

.mt-16{
  margin-top: 4rem;
}

.mt-20{
  margin-top: 5rem;
}

.mt-4{
  margin-top: 1rem;
}

.mt-\[60px\]{
  margin-top: 60px;
}

.mt-\[6px\]{
  margin-top: 6px;
}

.block{
  display: block;
}

.inline-block{
  display: inline-block;
}

.flex{
  display: flex;
}

.inline-flex{
  display: inline-flex;
}

.grid{
  display: grid;
}

.hidden{
  display: none;
}

.aspect-\[129\/138\]{
  aspect-ratio: 129/138;
}

.aspect-\[31\/22\]{
  aspect-ratio: 31/22;
}

.aspect-\[95\/82\]{
  aspect-ratio: 95/82;
}

.h-0\.5{
  height: 0.125rem;
}

.h-1{
  height: 0.25rem;
}

.h-1\/2{
  height: 50%;
}

.h-10{
  height: 2.5rem;
}

.h-16{
  height: 4rem;
}

.h-3{
  height: 0.75rem;
}

.h-4{
  height: 1rem;
}

.h-8{
  height: 2rem;
}

.h-\[120px\]{
  height: 120px;
}

.h-\[200px\]{
  height: 200px;
}

.h-\[22px\]{
  height: 22px;
}

.h-\[24px\]{
  height: 24px;
}

.h-\[2px\]{
  height: 2px;
}

.h-\[300px\]{
  height: 300px;
}

.h-\[30px\]{
  height: 30px;
}

.h-\[50px\]{
  height: 50px;
}

.h-\[70px\]{
  height: 70px;
}

.h-full{
  height: 100%;
}

.h-px{
  height: 1px;
}

.h-screen{
  height: 100vh;
}

.w-0{
  width: 0px;
}

.w-10{
  width: 2.5rem;
}

.w-16{
  width: 4rem;
}

.w-20{
  width: 5rem;
}

.w-3{
  width: 0.75rem;
}

.w-4{
  width: 1rem;
}

.w-60{
  width: 15rem;
}

.w-8{
  width: 2rem;
}

.w-\[120px\]{
  width: 120px;
}

.w-\[22px\]{
  width: 22px;
}

.w-\[24px\]{
  width: 24px;
}

.w-\[250px\]{
  width: 250px;
}

.w-\[30px\]{
  width: 30px;
}

.w-\[330px\]{
  width: 330px;
}

.w-\[50px\]{
  width: 50px;
}

.w-\[70px\]{
  width: 70px;
}

.w-full{
  width: 100%;
}

.w-screen{
  width: 100vw;
}

.max-w-\[140px\]{
  max-width: 140px;
}

.max-w-\[160px\]{
  max-width: 160px;
}

.max-w-\[260px\]{
  max-width: 260px;
}

.max-w-\[270px\]{
  max-width: 270px;
}

.max-w-\[357px\]{
  max-width: 357px;
}

.max-w-\[372px\]{
  max-width: 372px;
}

.max-w-\[50px\]{
  max-width: 50px;
}

.max-w-\[515px\]{
  max-width: 515px;
}

.max-w-\[525px\]{
  max-width: 525px;
}

.max-w-\[530px\]{
  max-width: 530px;
}

.max-w-\[570px\]{
  max-width: 570px;
}

.max-w-\[600px\]{
  max-width: 600px;
}

.max-w-\[780px\]{
  max-width: 780px;
}

.max-w-full{
  max-width: 100%;
}

.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-45{
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-90{
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-45{
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-\[25deg\]{
  --tw-rotate: 25deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes spin{
  to{
    transform: rotate(360deg);
  }
}

.animate-spin{
  animation: spin 1s linear infinite;
}

.cursor-pointer{
  cursor: pointer;
}

.resize-none{
  resize: none;
}

.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.flex-col{
  flex-direction: column;
}

.flex-wrap{
  flex-wrap: wrap;
}

.items-end{
  align-items: flex-end;
}

.items-center{
  align-items: center;
}

.items-stretch{
  align-items: stretch;
}

.justify-end{
  justify-content: flex-end;
}

.justify-center{
  justify-content: center;
}

.justify-between{
  justify-content: space-between;
}

.gap-2\.5{
  gap: 0.625rem;
}

.gap-4{
  gap: 1rem;
}

.gap-5{
  gap: 1.25rem;
}

.gap-8{
  gap: 2rem;
}

.gap-\[10px\]{
  gap: 10px;
}

.gap-\[2px\]{
  gap: 2px;
}

.gap-y-8{
  row-gap: 2rem;
}

.overflow-hidden{
  overflow: hidden;
}

.\!scroll-smooth{
  scroll-behavior: smooth !important;
}

.rounded{
  border-radius: 0.25rem;
}

.rounded-2xl{
  border-radius: 1rem;
}

.rounded-\[5px\]{
  border-radius: 5px;
}

.rounded-full{
  border-radius: 9999px;
}

.rounded-lg{
  border-radius: 0.5rem;
}

.rounded-md{
  border-radius: 0.375rem;
}

.rounded-xl{
  border-radius: 0.75rem;
}

.rounded-bl-md{
  border-bottom-left-radius: 0.375rem;
}

.rounded-tl-md{
  border-top-left-radius: 0.375rem;
}

.border{
  border-width: 1px;
}

.border-0{
  border-width: 0px;
}

.border-2{
  border-width: 2px;
}

.border-4{
  border-width: 4px;
}

.border-\[\.5px\]{
  border-width: .5px;
}

.border-b{
  border-bottom-width: 1px;
}

.border-l{
  border-left-width: 1px;
}

.border-t{
  border-top-width: 1px;
}

.border-solid{
  border-style: solid;
}

.border-\[\#8890A4\]{
  --tw-border-opacity: 1;
  border-color: rgb(136 144 164 / var(--tw-border-opacity, 1));
}

.border-\[\#f1f1f1\]{
  --tw-border-opacity: 1;
  border-color: rgb(241 241 241 / var(--tw-border-opacity, 1));
}

.border-body-color\/50{
  border-color: rgb(149 156 177 / 0.5);
}

.border-gray-100{
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}

.border-gray-4{
  --tw-border-opacity: 1;
  border-color: rgb(208 215 239 / var(--tw-border-opacity, 1));
}

.border-primary{
  --tw-border-opacity: 1;
  border-color: rgb(74 108 247 / var(--tw-border-opacity, 1));
}

.border-primary\/20{
  border-color: rgb(74 108 247 / 0.2);
}

.border-stroke{
  --tw-border-opacity: 1;
  border-color: rgb(227 232 248 / var(--tw-border-opacity, 1));
}

.border-transparent{
  border-color: transparent;
}

.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-white\/10{
  border-color: rgb(255 255 255 / 0.1);
}

.border-white\/20{
  border-color: rgb(255 255 255 / 0.2);
}

.border-t-transparent{
  border-top-color: transparent;
}

.border-opacity-40{
  --tw-border-opacity: 0.4;
}

.\!bg-dark{
  --tw-bg-opacity: 1 !important;
  background-color: rgb(29 33 68 / var(--tw-bg-opacity, 1)) !important;
}

.bg-\[\#090E34\]{
  --tw-bg-opacity: 1;
  background-color: rgb(9 14 52 / var(--tw-bg-opacity, 1));
}

.bg-\[\#E9F9FF\]{
  --tw-bg-opacity: 1;
  background-color: rgb(233 249 255 / var(--tw-bg-opacity, 1));
}

.bg-\[\#F4F7FF\]{
  --tw-bg-opacity: 1;
  background-color: rgb(244 247 255 / var(--tw-bg-opacity, 1));
}

.bg-blue-400\/20{
  background-color: rgb(96 165 250 / 0.2);
}

.bg-dark{
  --tw-bg-opacity: 1;
  background-color: rgb(29 33 68 / var(--tw-bg-opacity, 1));
}

.bg-dark-3{
  --tw-bg-opacity: 1;
  background-color: rgb(13 16 37 / var(--tw-bg-opacity, 1));
}

.bg-gray{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-gray-1{
  --tw-bg-opacity: 1;
  background-color: rgb(248 249 255 / var(--tw-bg-opacity, 1));
}

.bg-primary{
  --tw-bg-opacity: 1;
  background-color: rgb(74 108 247 / var(--tw-bg-opacity, 1));
}

.bg-primary\/10{
  background-color: rgb(74 108 247 / 0.1);
}

.bg-secondary{
  --tw-bg-opacity: 1;
  background-color: rgb(19 194 150 / var(--tw-bg-opacity, 1));
}

.bg-stroke{
  --tw-bg-opacity: 1;
  background-color: rgb(227 232 248 / var(--tw-bg-opacity, 1));
}

.bg-transparent{
  background-color: transparent;
}

.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/10{
  background-color: rgb(255 255 255 / 0.1);
}

.bg-white\/15{
  background-color: rgb(255 255 255 / 0.15);
}

.bg-white\/90{
  background-color: rgb(255 255 255 / 0.9);
}

.bg-white\/\[0\.15\]{
  background-color: rgb(255 255 255 / 0.15);
}

.bg-opacity-20{
  --tw-bg-opacity: 0.2;
}

.bg-opacity-5{
  --tw-bg-opacity: 0.05;
}

.bg-gradient-to-b{
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-t{
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.from-black\/60{
  --tw-gradient-from: rgb(33 43 54 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(33 43 54 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-400{
  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-900{
  --tw-gradient-from: #1e3a8a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-900\/60{
  --tw-gradient-from: rgb(30 58 138 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-dark-700{
  --tw-gradient-from: #090e34b3 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(9 14 52 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary{
  --tw-gradient-from: #4A6CF7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(74 108 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-stroke\/0{
  --tw-gradient-from: rgb(227 232 248 / 0) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(227 232 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white{
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-primary{
  --tw-gradient-to: rgb(74 108 247 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #4A6CF7 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-stroke{
  --tw-gradient-to: rgb(227 232 248 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #E3E8F8 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.to-blue-200{
  --tw-gradient-to: #bfdbfe var(--tw-gradient-to-position);
}

.to-blue-700{
  --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);
}

.to-blue-800{
  --tw-gradient-to: #1e40af var(--tw-gradient-to-position);
}

.to-gray-50{
  --tw-gradient-to: #f9fafb var(--tw-gradient-to-position);
}

.to-primary{
  --tw-gradient-to: #4A6CF7 var(--tw-gradient-to-position);
}

.to-stroke\/0{
  --tw-gradient-to: rgb(227 232 248 / 0) var(--tw-gradient-to-position);
}

.to-transparent{
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.bg-clip-text{
  -webkit-background-clip: text;
          background-clip: text;
}

.fill-current{
  fill: currentColor;
}

.object-cover{
  -o-object-fit: cover;
     object-fit: cover;
}

.object-center{
  -o-object-position: center;
     object-position: center;
}

.p-2{
  padding: 0.5rem;
}

.p-3\.5{
  padding: 0.875rem;
}

.p-4{
  padding: 1rem;
}

.p-6{
  padding: 1.5rem;
}

.p-8{
  padding: 2rem;
}

.px-10{
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-11{
  padding-left: 2.75rem;
  padding-right: 2.75rem;
}

.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-7{
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}

.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-10{
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-12{
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-14{
  padding-top: 3.5rem;
  padding-bottom: 3.5rem;
}

.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-20{
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5{
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-\[30px\]{
  padding-top: 30px;
  padding-bottom: 30px;
}

.py-\[60px\]{
  padding-top: 60px;
  padding-bottom: 60px;
}

.py-\[6px\]{
  padding-top: 6px;
  padding-bottom: 6px;
}

.pb-10{
  padding-bottom: 2.5rem;
}

.pb-12{
  padding-bottom: 3rem;
}

.pb-16{
  padding-bottom: 4rem;
}

.pb-20{
  padding-bottom: 5rem;
}

.pb-3{
  padding-bottom: 0.75rem;
}

.pb-4{
  padding-bottom: 1rem;
}

.pb-5{
  padding-bottom: 1.25rem;
}

.pb-8{
  padding-bottom: 2rem;
}

.pb-\[60px\]{
  padding-bottom: 60px;
}

.pl-1{
  padding-left: 0.25rem;
}

.pl-2{
  padding-left: 0.5rem;
}

.pr-16{
  padding-right: 4rem;
}

.pt-12{
  padding-top: 3rem;
}

.pt-20{
  padding-top: 5rem;
}

.pt-8{
  padding-top: 2rem;
}

.pt-\[120px\]{
  padding-top: 120px;
}

.text-center{
  text-align: center;
}

.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl{
  font-size: 3rem;
  line-height: 1;
}

.text-\[28px\]{
  font-size: 28px;
}

.text-\[32px\]{
  font-size: 32px;
}

.text-\[35px\]{
  font-size: 35px;
}

.text-base{
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold{
  font-weight: 700;
}

.font-extrabold{
  font-weight: 800;
}

.font-medium{
  font-weight: 500;
}

.font-normal{
  font-weight: 400;
}

.font-semibold{
  font-weight: 600;
}

.italic{
  font-style: italic;
}

.leading-\[1\.14\]{
  line-height: 1.14;
}

.leading-\[1\.5\]{
  line-height: 1.5;
}

.leading-\[26px\]{
  line-height: 26px;
}

.leading-\[40px\]{
  line-height: 40px;
}

.leading-loose{
  line-height: 2;
}

.leading-relaxed{
  line-height: 1.625;
}

.leading-snug{
  line-height: 1.375;
}

.leading-tight{
  line-height: 1.25;
}

.-tracking-\[2px\]{
  letter-spacing: -2px;
}

.\!text-primary{
  --tw-text-opacity: 1 !important;
  color: rgb(74 108 247 / var(--tw-text-opacity, 1)) !important;
}

.text-\[\#fbb040\]{
  --tw-text-opacity: 1;
  color: rgb(251 176 64 / var(--tw-text-opacity, 1));
}

.text-blue-300{
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}

.text-body-color{
  --tw-text-opacity: 1;
  color: rgb(149 156 177 / var(--tw-text-opacity, 1));
}

.text-dark{
  --tw-text-opacity: 1;
  color: rgb(29 33 68 / var(--tw-text-opacity, 1));
}

.text-dark-6{
  --tw-text-opacity: 1;
  color: rgb(136 144 173 / var(--tw-text-opacity, 1));
}

.text-gray-1{
  --tw-text-opacity: 1;
  color: rgb(248 249 255 / var(--tw-text-opacity, 1));
}

.text-gray-7{
  --tw-text-opacity: 1;
  color: rgb(122 132 169 / var(--tw-text-opacity, 1));
}

.text-primary{
  --tw-text-opacity: 1;
  color: rgb(74 108 247 / var(--tw-text-opacity, 1));
}

.text-transparent{
  color: transparent;
}

.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-white\/80{
  color: rgb(255 255 255 / 0.8);
}

.text-white\/90{
  color: rgb(255 255 255 / 0.9);
}

.text-opacity-80{
  --tw-text-opacity: 0.8;
}

.opacity-0{
  opacity: 0;
}

.opacity-100{
  opacity: 1;
}

.opacity-40{
  opacity: 0.4;
}

.shadow-2xl{
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0px_0px_40px_0px_rgba\(0\2c 0\2c 0\2c 0\.08\)\]{
  --tw-shadow: 0px 0px 40px 0px rgba(0,0,0,0.08);
  --tw-shadow-colored: 0px 0px 40px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg{
  --tw-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 10px 25px -3px var(--tw-shadow-color), 0 4px 6px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md{
  --tw-shadow: 0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06);
  --tw-shadow-colored: 0px 4px 8px -2px var(--tw-shadow-color), 0px 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-testimonial{
  --tw-shadow: 0px 2px 20px 0px rgba(0, 0, 0, 0.08);
  --tw-shadow-colored: 0px 2px 20px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.ring-primary{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(74 108 247 / var(--tw-ring-opacity, 1));
}

.backdrop-blur-md{
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm{
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-\[top\]{
  transition-property: top;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-200{
  transition-duration: 200ms;
}

.duration-300{
  transition-duration: 300ms;
}

.duration-500{
  transition-duration: 500ms;
}

.ease-in{
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

body {
  font-family: "Inter", sans-serif;
}

.placeholder\:text-body-color\/60::-moz-placeholder{
  color: rgb(149 156 177 / 0.6);
}

.placeholder\:text-body-color\/60::placeholder{
  color: rgb(149 156 177 / 0.6);
}

.placeholder\:text-dark-6::-moz-placeholder{
  --tw-text-opacity: 1;
  color: rgb(136 144 173 / var(--tw-text-opacity, 1));
}

.placeholder\:text-dark-6::placeholder{
  --tw-text-opacity: 1;
  color: rgb(136 144 173 / var(--tw-text-opacity, 1));
}

.placeholder\:text-white\/60::-moz-placeholder{
  color: rgb(255 255 255 / 0.6);
}

.placeholder\:text-white\/60::placeholder{
  color: rgb(255 255 255 / 0.6);
}

.hover\:-translate-y-1:hover{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-dark-3:hover{
  --tw-border-opacity: 1;
  border-color: rgb(13 16 37 / var(--tw-border-opacity, 1));
}

.hover\:border-gray-5:hover{
  --tw-border-opacity: 1;
  border-color: rgb(187 195 220 / var(--tw-border-opacity, 1));
}

.hover\:bg-\[\#0BB489\]:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(11 180 137 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-400:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-dark:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(28 63 183 / var(--tw-bg-opacity, 1));
}

.hover\:bg-dark:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(29 33 68 / var(--tw-bg-opacity, 1));
}

.hover\:bg-dark-3:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(13 16 37 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(74 108 247 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary\/90:hover{
  background-color: rgb(74 108 247 / 0.9);
}

.hover\:bg-white:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-white\/25:hover{
  background-color: rgb(255 255 255 / 0.25);
}

.hover\:bg-opacity-100:hover{
  --tw-bg-opacity: 1;
}

.hover\:bg-opacity-90:hover{
  --tw-bg-opacity: 0.9;
}

.hover\:text-primary:hover{
  --tw-text-opacity: 1;
  color: rgb(74 108 247 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover{
  text-decoration-line: underline;
}

.hover\:opacity-70:hover{
  opacity: 0.7;
}

.hover\:opacity-80:hover{
  opacity: 0.8;
}

.hover\:shadow-lg:hover{
  --tw-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 10px 25px -3px var(--tw-shadow-color), 0 4px 6px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover{
  --tw-shadow: 0px 8px 8px -4px rgba(16, 24, 40, 0.03), 0px 20px 24px -4px rgba(16, 24, 40, 0.08);
  --tw-shadow-colored: 0px 8px 8px -4px var(--tw-shadow-color), 0px 20px 24px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:border-primary:focus{
  --tw-border-opacity: 1;
  border-color: rgb(74 108 247 / var(--tw-border-opacity, 1));
}

.focus\:border-white:focus{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:shadow-none:focus-visible{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group:hover .group-hover\:w-full{
  width: 100%;
}

.group:hover .group-hover\:rotate-45{
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:rotate-6{
  --tw-rotate: 6deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-125{
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:bg-blue-400{
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}

.group:hover .group-hover\:text-blue-300{
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-primary{
  --tw-text-opacity: 1;
  color: rgb(74 108 247 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:opacity-100{
  opacity: 1;
}

.group:hover .group-hover\:shadow-lg{
  --tw-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 10px 25px -3px var(--tw-shadow-color), 0 4px 6px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.dark\:block:is(.dark *){
  display: block;
}

.dark\:hidden:is(.dark *){
  display: none;
}

.dark\:border-body-color\/20:is(.dark *){
  border-color: rgb(149 156 177 / 0.2);
}

.dark\:border-dark-3:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(13 16 37 / var(--tw-border-opacity, 1));
}

.dark\:border-dark-3\/20:is(.dark *){
  border-color: rgb(13 16 37 / 0.2);
}

.dark\:border-transparent:is(.dark *){
  border-color: transparent;
}

.dark\:border-t-transparent:is(.dark *){
  border-top-color: transparent;
}

.dark\:\!bg-white:is(.dark *){
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1)) !important;
}

.dark\:bg-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(29 33 68 / var(--tw-bg-opacity, 1));
}

.dark\:bg-dark-2:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(23 28 63 / var(--tw-bg-opacity, 1));
}

.dark\:bg-dark-2\/40:is(.dark *){
  background-color: rgb(23 28 63 / 0.4);
}

.dark\:bg-dark-3:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(13 16 37 / var(--tw-bg-opacity, 1));
}

.dark\:bg-dark-700:is(.dark *){
  background-color: #090e34b3;
}

.dark\:bg-dark\/20:is(.dark *){
  background-color: rgb(29 33 68 / 0.2);
}

.dark\:bg-gray-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.dark\:bg-primary:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(74 108 247 / var(--tw-bg-opacity, 1));
}

.dark\:bg-primary\/20:is(.dark *){
  background-color: rgb(74 108 247 / 0.2);
}

.dark\:bg-white:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.dark\:bg-white\/10:is(.dark *){
  background-color: rgb(255 255 255 / 0.1);
}

.dark\:from-dark-2:is(.dark *){
  --tw-gradient-from: #171c3f var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(23 28 63 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:via-dark-3:is(.dark *){
  --tw-gradient-to: rgb(13 16 37 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #0D1025 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:to-dark-3:is(.dark *){
  --tw-gradient-to: #0D1025 var(--tw-gradient-to-position);
}

.dark\:text-\[\#8890AD\]:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(136 144 173 / var(--tw-text-opacity, 1));
}

.dark\:text-dark-6:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(136 144 173 / var(--tw-text-opacity, 1));
}

.dark\:text-white:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.dark\:text-white\/70:is(.dark *){
  color: rgb(255 255 255 / 0.7);
}

.dark\:shadow-none:is(.dark *){
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.dark\:hover\:bg-dark-3:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(13 16 37 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-primary\/80:hover:is(.dark *){
  background-color: rgb(74 108 247 / 0.8);
}

.dark\:hover\:bg-white\/20:hover:is(.dark *){
  background-color: rgb(255 255 255 / 0.2);
}

.dark\:hover\:text-primary:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(74 108 247 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-white:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.dark\:hover\:shadow-none:hover:is(.dark *){
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.dark\:focus\:border-primary:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(74 108 247 / var(--tw-border-opacity, 1));
}

.group:hover .dark\:group-hover\:text-primary:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(74 108 247 / var(--tw-text-opacity, 1));
}

@media (min-width: 540px){
  .sm\:-mx-4{
    margin-left: -1rem;
    margin-right: -1rem;
  }

  .sm\:mb-8{
    margin-bottom: 2rem;
  }

  .sm\:mr-6{
    margin-right: 1.5rem;
  }

  .sm\:flex{
    display: flex;
  }

  .sm\:h-\[180px\]{
    height: 180px;
  }

  .sm\:h-\[220px\]{
    height: 220px;
  }

  .sm\:h-\[400px\]{
    height: 400px;
  }

  .sm\:h-\[60px\]{
    height: 60px;
  }

  .sm\:w-1\/2{
    width: 50%;
  }

  .sm\:max-w-\[60px\]{
    max-width: 60px;
  }

  .sm\:p-10{
    padding: 2.5rem;
  }

  .sm\:p-12{
    padding: 3rem;
  }

  .sm\:p-5{
    padding: 1.25rem;
  }

  .sm\:p-8{
    padding: 2rem;
  }

  .sm\:px-10{
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .sm\:px-12{
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .sm\:px-4{
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .sm\:px-\[30px\]{
    padding-left: 30px;
    padding-right: 30px;
  }

  .sm\:py-12{
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .sm\:text-2xl{
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .sm\:text-4xl{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .sm\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }

  .sm\:text-\[28px\]{
    font-size: 28px;
  }

  .sm\:text-\[40px\]{
    font-size: 40px;
  }

  .sm\:text-xl{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .sm\:leading-\[1\.2\]{
    line-height: 1.2;
  }

  .sm\:leading-\[1\.5\]{
    line-height: 1.5;
  }

  .sm\:leading-relaxed{
    line-height: 1.625;
  }

  .sm\:leading-tight{
    line-height: 1.25;
  }
}

@media (min-width: 720px){
  .md\:mr-10{
    margin-right: 2.5rem;
  }

  .md\:mr-4{
    margin-right: 1rem;
  }

  .md\:mr-6{
    margin-right: 1.5rem;
  }

  .md\:h-\[346px\]{
    height: 346px;
  }

  .md\:h-\[400px\]{
    height: 400px;
  }

  .md\:h-\[540px\]{
    height: 540px;
  }

  .md\:w-1\/2{
    width: 50%;
  }

  .md\:w-1\/3{
    width: 33.333333%;
  }

  .md\:w-2\/3{
    width: 66.666667%;
  }

  .md\:w-5\/12{
    width: 41.666667%;
  }

  .md\:w-7\/12{
    width: 58.333333%;
  }

  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:flex-row{
    flex-direction: row;
  }

  .md\:justify-start{
    justify-content: flex-start;
  }

  .md\:justify-end{
    justify-content: flex-end;
  }

  .md\:p-\[60px\]{
    padding: 60px;
  }

  .md\:px-\[60px\]{
    padding-left: 60px;
    padding-right: 60px;
  }

  .md\:py-\[120px\]{
    padding-top: 120px;
    padding-bottom: 120px;
  }

  .md\:pt-\[130px\]{
    padding-top: 130px;
  }

  .md\:text-\[28px\]{
    font-size: 28px;
  }

  .md\:text-\[38px\]{
    font-size: 38px;
  }

  .md\:text-\[40px\]{
    font-size: 40px;
  }

  .md\:leading-\[1\.2\]{
    line-height: 1.2;
  }

  .md\:leading-\[1\.42\]{
    line-height: 1.42;
  }

  .md\:leading-\[1\.44\]{
    line-height: 1.44;
  }
}

@media (min-width: 960px){
  .lg\:visible{
    visibility: visible;
  }

  .lg\:invisible{
    visibility: hidden;
  }

  .lg\:static{
    position: static;
  }

  .lg\:absolute{
    position: absolute;
  }

  .lg\:top-\[110\%\]{
    top: 110%;
  }

  .lg\:-mx-2{
    margin-left: -0.5rem;
    margin-right: -0.5rem;
  }

  .lg\:mx-0{
    margin-left: 0px;
    margin-right: 0px;
  }

  .lg\:mb-0{
    margin-bottom: 0px;
  }

  .lg\:mb-11{
    margin-bottom: 2.75rem;
  }

  .lg\:mb-4{
    margin-bottom: 1rem;
  }

  .lg\:mb-\[150px\]{
    margin-bottom: 150px;
  }

  .lg\:mb-\[70px\]{
    margin-bottom: 70px;
  }

  .lg\:mr-2{
    margin-right: 0.5rem;
  }

  .lg\:mt-0{
    margin-top: 0px;
  }

  .lg\:mt-20{
    margin-top: 5rem;
  }

  .lg\:mt-\[60px\]{
    margin-top: 60px;
  }

  .lg\:block{
    display: block;
  }

  .lg\:flex{
    display: flex;
  }

  .lg\:inline-flex{
    display: inline-flex;
  }

  .lg\:hidden{
    display: none;
  }

  .lg\:h-\[225px\]{
    height: 225px;
  }

  .lg\:h-\[400px\]{
    height: 400px;
  }

  .lg\:h-\[45\%\]{
    height: 45%;
  }

  .lg\:h-\[500px\]{
    height: 500px;
  }

  .lg\:w-1\/2{
    width: 50%;
  }

  .lg\:w-1\/3{
    width: 33.333333%;
  }

  .lg\:w-1\/4{
    width: 25%;
  }

  .lg\:w-2\/12{
    width: 16.666667%;
  }

  .lg\:w-3\/12{
    width: 25%;
  }

  .lg\:w-4\/12{
    width: 33.333333%;
  }

  .lg\:w-5\/12{
    width: 41.666667%;
  }

  .lg\:w-6\/12{
    width: 50%;
  }

  .lg\:w-7\/12{
    width: 58.333333%;
  }

  .lg\:w-8\/12{
    width: 66.666667%;
  }

  .lg\:w-\[250px\]{
    width: 250px;
  }

  .lg\:w-auto{
    width: auto;
  }

  .lg\:w-full{
    width: 100%;
  }

  .lg\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:justify-start{
    justify-content: flex-start;
  }

  .lg\:space-x-7 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(1.75rem * var(--tw-space-x-reverse));
    margin-left: calc(1.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:border-none{
    border-style: none;
  }

  .lg\:\!bg-transparent{
    background-color: transparent !important;
  }

  .lg\:p-0{
    padding: 0px;
  }

  .lg\:p-10{
    padding: 2.5rem;
  }

  .lg\:p-4{
    padding: 1rem;
  }

  .lg\:px-0{
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:px-10{
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .lg\:px-2{
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .lg\:px-3{
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .lg\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .lg\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-10{
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .lg\:py-12{
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .lg\:py-20{
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .lg\:py-6{
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .lg\:py-\[110px\]{
    padding-top: 110px;
    padding-bottom: 110px;
  }

  .lg\:py-\[115px\]{
    padding-top: 115px;
    padding-bottom: 115px;
  }

  .lg\:py-\[90px\]{
    padding-top: 90px;
    padding-bottom: 90px;
  }

  .lg\:pb-20{
    padding-bottom: 5rem;
  }

  .lg\:pb-\[50px\]{
    padding-bottom: 50px;
  }

  .lg\:pb-\[70px\]{
    padding-bottom: 70px;
  }

  .lg\:pb-\[90px\]{
    padding-bottom: 90px;
  }

  .lg\:pr-0{
    padding-right: 0px;
  }

  .lg\:pt-\[100px\]{
    padding-top: 100px;
  }

  .lg\:pt-\[120px\]{
    padding-top: 120px;
  }

  .lg\:pt-\[160px\]{
    padding-top: 160px;
  }

  .lg\:text-left{
    text-align: left;
  }

  .lg\:text-6xl{
    font-size: 3.75rem;
    line-height: 1;
  }

  .lg\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .lg\:text-xl{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .lg\:leading-\[1\.2\]{
    line-height: 1.2;
  }

  .lg\:text-white{
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }

  .lg\:opacity-100{
    opacity: 1;
  }

  .lg\:shadow-lg{
    --tw-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --tw-shadow-colored: 0 10px 25px -3px var(--tw-shadow-color), 0 4px 6px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .group:hover .lg\:group-hover\:visible{
    visibility: visible;
  }

  .group:hover .lg\:group-hover\:top-full{
    top: 100%;
  }

  .group:hover .lg\:group-hover\:rotate-180{
    --tw-rotate: 180deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
}

@media (min-width: 1140px){
  .xl\:-mx-4{
    margin-left: -1rem;
    margin-right: -1rem;
  }

  .xl\:mb-8{
    margin-bottom: 2rem;
  }

  .xl\:mr-4{
    margin-right: 1rem;
  }

  .xl\:h-1\/2{
    height: 50%;
  }

  .xl\:h-\[310px\]{
    height: 310px;
  }

  .xl\:h-\[500px\]{
    height: 500px;
  }

  .xl\:w-1\/3{
    width: 33.333333%;
  }

  .xl\:w-1\/4{
    width: 25%;
  }

  .xl\:w-2\/12{
    width: 16.666667%;
  }

  .xl\:w-3\/12{
    width: 25%;
  }

  .xl\:w-4\/12{
    width: 33.333333%;
  }

  .xl\:w-5\/12{
    width: 41.666667%;
  }

  .xl\:w-8\/12{
    width: 66.666667%;
  }

  .xl\:gap-11{
    gap: 2.75rem;
  }

  .xl\:p-14{
    padding: 3.5rem;
  }

  .xl\:px-4{
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .xl\:pr-10{
    padding-right: 2.5rem;
  }

  .xl\:text-2xl{
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .xl\:text-\[42px\]{
    font-size: 42px;
  }

  .xl\:text-lg{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .xl\:leading-\[1\.21\]{
    line-height: 1.21;
  }
}

@media (min-width: 1320px){
  .\32xl\:p-\[60px\]{
    padding: 60px;
  }
}
