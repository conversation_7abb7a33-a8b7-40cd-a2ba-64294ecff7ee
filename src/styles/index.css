@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern design enhancements */
:root {
  --font-inter: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

body {
  font-family: var(--font-inter);
  font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Enhanced typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

/* Modern button styles */
.btn-modern {
  @apply inline-flex items-center justify-center px-6 py-3 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/50;
  @apply transform hover:-translate-y-0.5 hover:shadow-lg;
}

/* Modern card styles */
.card-modern {
  @apply bg-white dark:bg-dark-2 rounded-xl shadow-lg border border-gray-100 dark:border-dark-3;
  @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
}

/* Glass morphism effect */
.glass {
  @apply backdrop-blur-md bg-white/10 border border-white/20;
}

/* Performance optimizations - Shimmer animation for loading states */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* Enhanced Blog Typography */
.blog-content {
  @apply text-body-color dark:text-dark-6 leading-relaxed;
}

.blog-content h1,
.blog-content h2,
.blog-content h3,
.blog-content h4,
.blog-content h5,
.blog-content h6 {
  @apply text-dark dark:text-white font-bold tracking-tight mb-4 mt-8;
}

.blog-content h1 {
  @apply text-4xl mb-6;
}

.blog-content h2 {
  @apply text-3xl mb-5;
}

.blog-content h3 {
  @apply text-2xl mb-4;
}

.blog-content h4 {
  @apply text-xl mb-3;
}

.blog-content p {
  @apply mb-6 leading-relaxed;
}

.blog-content a {
  @apply text-primary no-underline hover:underline transition-colors duration-200;
}

.blog-content strong {
  @apply text-dark dark:text-white font-semibold;
}

.blog-content code {
  @apply text-primary bg-primary/10 px-2 py-1 rounded text-sm font-medium;
}

.blog-content pre {
  @apply bg-dark border border-dark-3 rounded-xl p-6 overflow-x-auto my-6;
}

.blog-content blockquote {
  @apply border-l-4 border-primary bg-primary/5 p-6 rounded-r-lg my-8 text-dark dark:text-white;
}

.blog-content ul,
.blog-content ol {
  @apply space-y-2 mb-6;
}

.blog-content li {
  @apply text-body-color dark:text-dark-6;
}

.blog-content img {
  @apply rounded-xl shadow-lg border border-gray-200 dark:border-dark-3 my-6;
}

.blog-content table {
  @apply border border-gray-200 dark:border-dark-3 rounded-lg overflow-hidden my-6 w-full;
}

.blog-content th {
  @apply bg-gray-50 dark:bg-dark-2 text-dark dark:text-white font-semibold p-4;
}

.blog-content td {
  @apply p-4 border-t border-gray-200 dark:border-dark-3;
}

.blog-content hr {
  @apply border-gray-200 dark:border-dark-3 my-8;
}

/* Improved focus states */
.focus-modern {
  @apply focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-dark;
}

/* Dark mode enhancements with blue accents */
.dark {
  /* Enhanced dark backgrounds with subtle blue tints */
  --tw-bg-dark: #0a0e1a;
  --tw-bg-dark-2: #0f1419;
  --tw-bg-dark-3: #151b26;

  /* Blue accent borders and highlights */
  --tw-border-accent: #d1d9ff;
  --tw-text-accent: #b3c0ff;
  --tw-bg-accent: #3d4afc;
}

/* Dark mode specific component styles */
.dark .card-modern {
  @apply bg-dark-2 border-dark-4 hover:border-primary/20;
  @apply shadow-lg shadow-black/20 hover:shadow-xl hover:shadow-primary/5;
}

.dark .glass {
  @apply backdrop-blur-md bg-dark-2/80 border-primary/10;
}

/* Dark mode button enhancements */
.dark .btn-modern {
  @apply border-dark-4 hover:border-primary/30;
}

/* Dark mode text improvements */
.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  @apply text-white;
}

.dark p {
  @apply text-dark-6;
}

/* Dark mode interactive elements with blue accents */
.dark a:hover {
  @apply text-primary-light;
}

.dark .text-primary {
  @apply text-primary-light;
}

.dark .bg-primary {
  @apply bg-primary-dark;
}

.dark .border-primary {
  @apply border-primary-light;
}

@layer components {
  .sticky-menu .navbar-logo {
    @apply py-2;
  }

  .sticky-menu #navbarToggler span {
    @apply bg-dark dark:bg-white;
  }

  .sticky-menu #navbarCollapse li > a {
    @apply text-dark hover:text-primary hover:opacity-100 dark:text-white;
  }
  #navbarCollapse li .ud-menu-scroll.active {
    @apply opacity-70;
  }
  .sticky-menu #navbarCollapse li .ud-menu-scroll.active {
    @apply text-primary opacity-100 dark:text-white;
  }
  .sticky-menu .loginBtn {
    @apply text-dark hover:text-primary hover:opacity-100 dark:text-white;
  }

  .sticky-menu .signUpBtn {
    @apply bg-primary hover:bg-dark text-white hover:text-white;
  }

  .navbarTogglerActive > span:nth-child(1) {
    @apply top-[7px] rotate-45 transform;
  }
  .navbarTogglerActive > span:nth-child(2) {
    @apply opacity-0;
  }
  .navbarTogglerActive > span:nth-child(3) {
    @apply top-[-8px] rotate-[135deg];
  }

  .blog-details,
  .blog-details p {
    @apply text-body-color text-base leading-relaxed;
    @apply dark:text-[#8890AD];
  }

  .blog-details p {
    @apply mb-8;
  }

  .blog-details strong {
    @apply text-dark font-bold dark:text-white;
  }

  .blog-details ul {
    @apply mb-8 list-inside list-disc space-y-3;
  }

  .blog-details ol {
    @apply mb-8 list-inside list-decimal space-y-3;
  }

  .blog-details h1 {
    @apply text-dark mb-8 text-3xl font-bold dark:text-white sm:text-4xl md:text-[40px] md:leading-[1.28];
  }

  .blog-details h2 {
    @apply text-dark mb-8 text-2xl font-bold dark:text-white sm:text-3xl md:text-[35px] md:leading-[1.28];
  }

  .blog-details h3 {
    @apply text-dark mb-6 text-2xl font-bold dark:text-white sm:text-[28px] sm:leading-[40px];
  }
}
