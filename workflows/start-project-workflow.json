{"name": "Start Project - High Intent Sales Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "start-project", "authentication": "headerAuth", "options": {}}, "id": "webhook-start-project", "name": "Webhook - Start Project", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "start-project-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validation-name", "leftValue": "={{ $json.name }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}, {"id": "validation-email", "leftValue": "={{ $json.email }}", "rightValue": "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$", "operator": {"type": "string", "operation": "regex"}}, {"id": "validation-company", "leftValue": "={{ $json.company }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}, {"id": "validation-project-type", "leftValue": "={{ $json.projectType }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}, {"id": "validation-budget", "leftValue": "={{ $json.budgetRange }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}, {"id": "validation-privacy", "leftValue": "={{ $json.privacyConsent }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "data-validation", "name": "Data Validation", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"jsCode": "// High-Intent Lead Scoring Algorithm\nconst data = $input.first().json;\n\nlet score = 50; // Base score for high-intent form completion\n\n// Budget range scoring (high weight for enterprise projects)\nconst budgetScores = {\n  '2m+': 30,\n  '1m-2m': 25,\n  '500k-1m': 20,\n  '250k-500k': 15,\n  '100k-250k': 10,\n  '50k-100k': 5,\n  'flexible': 15\n};\nscore += budgetScores[data.budgetRange] || 5;\n\n// Company size scoring\nconst companySizeScores = {\n  'enterprise': 20,\n  'large': 15,\n  'medium': 10,\n  'small': 5,\n  'startup': 3\n};\nif (data.companySize) {\n  score += companySizeScores[data.companySize] || 5;\n}\n\n// Urgency level scoring\nconst urgencyScores = {\n  'urgent': 15,\n  'high': 10,\n  'medium': 5,\n  'low': 2\n};\nscore += urgencyScores[data.urgencyLevel] || 5;\n\n// Decision timeframe scoring (faster decision = higher score)\nconst decisionScores = {\n  '1-week': 15,\n  '2-weeks': 12,\n  '1-month': 8,\n  '3-months': 5,\n  '6-months': 2\n};\nscore += decisionScores[data.decisionTimeframe] || 2;\n\n// Project complexity indicators\nif (data.complianceRequirements && data.complianceRequirements.length > 0) {\n  score += data.complianceRequirements.length * 3;\n}\n\nif (data.platforms && data.platforms.length > 2) {\n  score += 5;\n}\n\nif (data.existingSystems && data.existingSystems.length > 50) {\n  score += 5;\n}\n\n// Team readiness indicators\nif (data.projectManager) {\n  score += 5;\n}\n\nif (data.technicalExpertise === 'advanced' || data.technicalExpertise === 'expert') {\n  score += 5;\n}\n\n// Detailed information provided\nif (data.businessObjectives && data.businessObjectives.length > 100) {\n  score += 3;\n}\n\nif (data.criticalDeadlines && data.criticalDeadlines.length > 20) {\n  score += 3;\n}\n\n// Cap at 100\nconst finalScore = Math.min(score, 100);\n\n// Determine routing\nlet priority, assignedTeam, responseTime, followUpSequence;\n\nif (finalScore >= 85 || data.urgencyLevel === 'urgent') {\n  priority = 'urgent';\n  assignedTeam = 'executive-team';\n  responseTime = '1 hour';\n  followUpSequence = 'executive-engagement';\n} else if (finalScore >= 70 || data.urgencyLevel === 'high') {\n  priority = 'high';\n  assignedTeam = 'senior-sales';\n  responseTime = '2 hours';\n  followUpSequence = 'high-value-prospect';\n} else if (finalScore >= 55) {\n  priority = 'medium';\n  assignedTeam = 'sales-team';\n  responseTime = '4 hours';\n  followUpSequence = 'qualified-lead';\n} else {\n  priority = 'low';\n  assignedTeam = 'sales-team';\n  responseTime = '8 hours';\n  followUpSequence = 'standard-nurture';\n}\n\n// Adjust based on budget range\nif (['2m+', '1m-2m', '500k-1m'].includes(data.budgetRange)) {\n  if (priority === 'medium') priority = 'high';\n  if (priority === 'low') priority = 'medium';\n  assignedTeam = 'senior-sales';\n}\n\n// Enterprise company size gets priority treatment\nif (data.companySize === 'enterprise' || data.companySize === 'large') {\n  if (priority === 'low') priority = 'medium';\n  assignedTeam = 'enterprise-team';\n}\n\nconst projectId = `proj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n\nreturn {\n  ...data,\n  projectId,\n  leadScore: finalScore,\n  priority,\n  assignedTeam,\n  responseTime,\n  followUpSequence,\n  timestamp: new Date().toISOString(),\n  source: 'high_intent_project_form',\n  leadType: 'enterprise_qualified',\n  formCompletionRate: 100,\n  dataQuality: 'high'\n};"}, "id": "lead-scoring", "name": "Lead Scoring & Routing", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "urgent-condition", "leftValue": "={{ $json.priority }}", "rightValue": "urgent", "operator": {"type": "string", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "priority-routing", "name": "Priority Routing", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"resource": "contact", "operation": "create", "additionalFields": {"company": "={{ $json.company }}", "phone": "={{ $json.phone }}", "jobtitle": "={{ $json.jobTitle }}", "website": "={{ $json.pageUrl }}", "lifecyclestage": "lead", "lead_status": "new", "hs_lead_status": "NEW"}, "email": "={{ $json.email }}", "firstname": "={{ $json.name.split(' ')[0] }}", "lastname": "={{ $json.name.split(' ').slice(1).join(' ') }}"}, "id": "hubspot-contact", "name": "Create HubSpot Contact", "type": "n8n-nodes-base.hubspot", "typeVersion": 2, "position": [1120, 80], "credentials": {"hubspotApi": {"id": "hubspot-credentials", "name": "HubSpot API"}}}, {"parameters": {"resource": "deal", "operation": "create", "additionalFields": {"amount": "={{ $json.budgetRange === '2m+' ? 2000000 : $json.budgetRange === '1m-2m' ? 1500000 : $json.budgetRange === '500k-1m' ? 750000 : $json.budgetRange === '250k-500k' ? 375000 : $json.budgetRange === '100k-250k' ? 175000 : 100000 }}", "closedate": "={{ new Date(Date.now() + (90 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0] }}", "dealstage": "appointmentscheduled", "dealtype": "newbusiness", "pipeline": "default"}, "dealname": "={{ $json.projectName || $json.projectType + ' - ' + $json.company }}", "associatedCompanyIds": [], "associatedContactIds": ["={{ $('Create HubSpot Contact').first().json.id }}"]}, "id": "hubspot-deal", "name": "Create HubSpot Deal", "type": "n8n-nodes-base.hubspot", "typeVersion": 2, "position": [1340, 80], "credentials": {"hubspotApi": {"id": "hubspot-credentials", "name": "HubSpot API"}}}, {"parameters": {"channel": "={{ $json.priority === 'urgent' ? '#executive-alerts' : $json.priority === 'high' ? '#high-priority-leads' : '#sales-leads' }}", "text": "🚨 NEW HIGH-INTENT PROJECT LEAD 🚨", "attachments": [{"color": "={{ $json.priority === 'urgent' ? '#ff0000' : $json.priority === 'high' ? '#ff8c00' : '#3d4afc' }}", "fields": [{"title": "Lead Score", "value": "={{ $json.leadScore }}/100", "short": true}, {"title": "Priority", "value": "={{ $json.priority.toUpperCase() }}", "short": true}, {"title": "Company", "value": "={{ $json.company }}", "short": true}, {"title": "Contact", "value": "={{ $json.name }} ({{ $json.jobTitle }})", "short": true}, {"title": "Project Type", "value": "={{ $json.projectType }}", "short": true}, {"title": "Budget Range", "value": "={{ $json.budgetRange }}", "short": true}, {"title": "Timeline", "value": "={{ $json.timeline }}", "short": true}, {"title": "Response Time", "value": "={{ $json.responseTime }}", "short": true}, {"title": "Project Description", "value": "={{ $json.projectDescription.substring(0, 200) }}...", "short": false}], "footer": "Digital Wave Systems CRM", "ts": "={{ Math.floor(Date.now() / 1000) }}"}], "otherOptions": {}}, "id": "slack-notification", "name": "Slack Team Notification", "type": "n8n-nodes-base.slack", "typeVersion": 2, "position": [1120, 200], "credentials": {"slackApi": {"id": "slack-credentials", "name": "Slack API"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": false,\n  \"error\": \"Validation failed\",\n  \"message\": \"Please fill in all required fields correctly\",\n  \"details\": [\n    $json.name ? \"\" : \"Name is required\",\n    /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test($json.email) ? \"\" : \"Valid email is required\",\n    $json.company ? \"\" : \"Company is required\",\n    $json.projectType ? \"\" : \"Project type is required\",\n    $json.budgetRange ? \"\" : \"Budget range is required\",\n    $json.privacyConsent ? \"\" : \"Privacy consent is required\"\n  ].filter(<PERSON><PERSON><PERSON>)\n} }}", "options": {}}, "id": "validation-error-response", "name": "Validation Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 400]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.email }}", "subject": "Thank you for your project inquiry - Next steps", "emailType": "html", "message": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Project Inquiry Confirmation</title>\n    <style>\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background: linear-gradient(135deg, #3d4afc 0%, #667eea 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }\n        .content { background: white; padding: 30px; border: 1px solid #e1e5e9; }\n        .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; }\n        .button { display: inline-block; background: #3d4afc; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }\n        .project-details { background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; }\n        .next-steps { background: #e8f5e8; padding: 20px; border-radius: 6px; margin: 20px 0; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>Thank You for Your Project Inquiry!</h1>\n            <p>We're excited to learn about your {{ $json.projectType }} project</p>\n        </div>\n        \n        <div class=\"content\">\n            <p>Dear {{ $json.name }},</p>\n            \n            <p>Thank you for providing detailed information about your project. Our {{ $json.assignedTeam }} has received your inquiry and will review it immediately.</p>\n            \n            <div class=\"project-details\">\n                <h3>📋 Project Summary</h3>\n                <p><strong>Project:</strong> {{ $json.projectName || $json.projectType }}</p>\n                <p><strong>Company:</strong> {{ $json.company }}</p>\n                <p><strong>Timeline:</strong> {{ $json.timeline }}</p>\n                <p><strong>Budget Range:</strong> {{ $json.budgetRange }}</p>\n                <p><strong>Lead Score:</strong> {{ $json.leadScore }}/100</p>\n                <p><strong>Priority Level:</strong> {{ $json.priority.toUpperCase() }}</p>\n            </div>\n            \n            <div class=\"next-steps\">\n                <h3>🚀 What Happens Next?</h3>\n                <ul>\n                    <li><strong>Response Time:</strong> Our team will contact you within {{ $json.responseTime }}</li>\n                    <li><strong>Discovery Call:</strong> We'll schedule a detailed discussion about your requirements</li>\n                    <li><strong>Proposal:</strong> You'll receive a comprehensive proposal with timeline and pricing</li>\n                    <li><strong>Project Start:</strong> We can begin immediately upon approval</li>\n                </ul>\n            </div>\n            \n            <p>In the meantime, feel free to review our case studies and learn more about our development process:</p>\n            \n            <a href=\"https://digitalwavesystems.com.co/case-studies\" class=\"button\">View Case Studies</a>\n            <a href=\"https://digitalwavesystems.com.co/process\" class=\"button\">Our Process</a>\n            \n            <p>If you have any immediate questions, please don't hesitate to reach out to us directly.</p>\n            \n            <p>Best regards,<br>\n            The Digital Wave Systems Team</p>\n        </div>\n        \n        <div class=\"footer\">\n            <p>Digital Wave Systems | Software Development Consultancy</p>\n            <p>📧 <EMAIL> | 🌐 digitalwavesystems.com.co</p>\n        </div>\n    </div>\n</body>\n</html>", "options": {}}, "id": "confirmation-email", "name": "Send Confirmation Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1340, 200], "credentials": {"smtp": {"id": "email-credentials", "name": "SMTP Email"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"Project request submitted successfully\",\n  \"projectId\": $json.projectId,\n  \"leadScore\": $json.leadScore,\n  \"priority\": $json.priority,\n  \"responseTime\": $json.responseTime,\n  \"nextSteps\": [\n    `Our ${$json.assignedTeam} will review your project within ${$json.responseTime}`,\n    \"You will receive a detailed follow-up email with next steps\",\n    \"We will schedule a comprehensive discovery call to discuss your requirements\",\n    \"You will receive a detailed proposal with timeline, team structure, and pricing\",\n    \"We can begin project planning immediately upon approval\"\n  ]\n} }}", "options": {}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 200]}], "pinData": {}, "connections": {"Webhook - Start Project": {"main": [[{"node": "Data Validation", "type": "main", "index": 0}]]}, "Data Validation": {"main": [[{"node": "Lead Scoring & Routing", "type": "main", "index": 0}], [{"node": "Validation Error Response", "type": "main", "index": 0}]]}, "Lead Scoring & Routing": {"main": [[{"node": "Priority Routing", "type": "main", "index": 0}]]}, "Priority Routing": {"main": [[{"node": "Create HubSpot Contact", "type": "main", "index": 0}, {"node": "Slack Team Notification", "type": "main", "index": 0}], [{"node": "Create HubSpot Contact", "type": "main", "index": 0}, {"node": "Slack Team Notification", "type": "main", "index": 0}]]}, "Create HubSpot Contact": {"main": [[{"node": "Create HubSpot Deal", "type": "main", "index": 0}]]}, "Create HubSpot Deal": {"main": [[{"node": "Send Confirmation Email", "type": "main", "index": 0}]]}, "Send Confirmation Email": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "start-project-workflow", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "high-intent-sales", "name": "High Intent Sales"}]}