# n8n Workflow Setup Guide

## Overview

This directory contains four production-ready n8n workflow JSON files designed for the Digital Wave Systems lead capture and automation system. Each workflow handles different aspects of the lead generation and sales qualification process.

## Workflow Files

### 1. `start-project-workflow.json` - High-Intent Enterprise Sales
**Purpose**: Handles detailed project inquiries from enterprise clients ready to start development

**Key Features**:
- Advanced lead scoring algorithm (50-100 points)
- Executive routing for high-value leads (score 85+)
- Comprehensive CRM integration with HubSpot
- Automated proposal generation triggers
- Multi-tier response times (1-8 hours based on priority)

**Webhook Endpoint**: `/api/n8n-webhooks/start-project`

### 2. `get-quote-workflow.json` - Lead Generation & Nurturing
**Purpose**: Captures and nurtures early-stage prospects with educational content

**Key Features**:
- Lead generation scoring (20-100 points)
- Three nurture sequence branches (high-intent, qualified, educational)
- Service-specific content delivery
- Progressive qualification tracking
- Engagement monitoring and scoring updates

**Webhook Endpoint**: `/api/n8n-webhooks/get-quote`

### 3. `quick-quote-workflow.json` - Consultation Scheduling
**Purpose**: Handles immediate consultation requests with video/phone/email options

**Key Features**:
- Multi-channel consultation routing (video/phone/email)
- Calendly API integration for video bookings
- ElevenLabs AI voice preparation for phone calls
- Callback automation with priority scheduling
- Pre-consultation material delivery

**Webhook Endpoint**: `/api/n8n-webhooks/quick-quote`

### 4. `lead-capture-workflow.json` - General Lead Qualification
**Purpose**: Processes general inquiries with UTM attribution and service routing

**Key Features**:
- UTM campaign attribution and analysis
- Service-specific routing logic
- Multi-channel notifications (Slack, email, CRM)
- Automated response delivery with relevant resources
- Lead qualification levels (sales-qualified, marketing-qualified, lead, subscriber)

**Webhook Endpoint**: `/api/n8n-webhooks/lead-capture`

## Prerequisites

### Required n8n Credentials

1. **HubSpot API** (`hubspot-credentials`)
   - API Key or OAuth connection
   - Required scopes: contacts, deals, companies

2. **Slack API** (`slack-credentials`)
   - Bot token with channels:write permissions
   - Access to relevant channels (#sales-leads, #executive-alerts, etc.)

3. **SMTP Email** (`email-credentials`)
   - SMTP server configuration
   - Sender email: <EMAIL>

4. **Calendly API** (`calendly-credentials`) - For quick-quote workflow
   - Personal access token
   - User ID for scheduling

5. **ElevenLabs API** (`elevenlabs-credentials`) - For AI voice calls
   - API key
   - Voice ID for text-to-speech

### Environment Variables

Set these in your n8n environment:

```bash
# ElevenLabs Voice Configuration
ELEVENLABS_VOICE_ID="your_voice_id_here"

# Webhook Authentication
N8N_WEBHOOK_SECRET="your_secure_webhook_secret"

# Email Configuration
EMAIL_FROM="<EMAIL>"
```

## Installation Instructions

### Step 1: Import Workflows

1. Open your n8n instance
2. Go to **Workflows** → **Import from File**
3. Import each JSON file:
   - `start-project-workflow.json`
   - `get-quote-workflow.json`
   - `quick-quote-workflow.json`
   - `lead-capture-workflow.json`

### Step 2: Configure Credentials

For each workflow, configure the required credentials:

#### HubSpot API Setup
1. Go to **Credentials** → **Create New**
2. Select **HubSpot API**
3. Enter your HubSpot API key or configure OAuth
4. Test the connection
5. Name it `hubspot-credentials`

#### Slack API Setup
1. Create a Slack app in your workspace
2. Add bot token scopes: `channels:write`, `chat:write`
3. Install app to workspace
4. Copy bot token to n8n credentials
5. Name it `slack-credentials`

#### SMTP Email Setup
1. Configure your email provider settings
2. Use `<EMAIL>` as sender
3. Test email delivery
4. Name it `email-credentials`

#### Calendly API Setup (Optional)
1. Generate personal access token in Calendly
2. Get your user ID from Calendly API
3. Configure in n8n credentials
4. Name it `calendly-credentials`

#### ElevenLabs API Setup (Optional)
1. Sign up for ElevenLabs account
2. Generate API key
3. Select or create a voice ID
4. Configure in n8n credentials
5. Name it `elevenlabs-credentials`

### Step 3: Configure Webhook URLs

Update your website's environment variables with the n8n webhook URLs:

```bash
# Replace with your actual n8n instance URL
N8N_START_PROJECT_WEBHOOK_URL="https://your-n8n-instance.com/webhook/start-project"
N8N_GET_QUOTE_WEBHOOK_URL="https://your-n8n-instance.com/webhook/get-quote"
N8N_QUICK_QUOTE_WEBHOOK_URL="https://your-n8n-instance.com/webhook/quick-quote"
N8N_LEAD_CAPTURE_WEBHOOK_URL="https://your-n8n-instance.com/webhook/lead-capture"
```

### Step 4: Activate Workflows

1. Open each imported workflow
2. Click **Activate** to enable the workflow
3. Test each webhook endpoint to ensure proper functionality

## Testing Procedures

### Manual Testing

Use the provided test script to validate all endpoints:

```bash
# From your website project root
node scripts/test-cta-integration.js
```

### Individual Workflow Testing

Test each workflow individually using n8n's test feature:

1. Open the workflow in n8n
2. Click on the webhook node
3. Use **Test URL** to send sample data
4. Verify all nodes execute successfully
5. Check CRM, Slack, and email integrations

### Sample Test Payloads

#### Start Project Test Data
```json
{
  "name": "Test Manager",
  "email": "<EMAIL>",
  "company": "Test Corp",
  "projectType": "custom-software",
  "budgetRange": "500k-1m",
  "timeline": "6-12-months",
  "urgencyLevel": "high",
  "privacyConsent": true
}
```

#### Get Quote Test Data
```json
{
  "name": "Test Lead",
  "email": "<EMAIL>",
  "serviceInterest": "web-application",
  "consultationType": "video",
  "timelineFlexibility": "flexible",
  "privacyConsent": true
}
```

## Monitoring and Maintenance

### Workflow Monitoring

1. **Execution Logs**: Monitor workflow executions in n8n dashboard
2. **Error Alerts**: Set up Slack notifications for failed executions
3. **Performance Metrics**: Track execution times and success rates
4. **CRM Sync**: Verify data synchronization with HubSpot

### Regular Maintenance Tasks

#### Weekly
- [ ] Review workflow execution logs
- [ ] Check CRM data synchronization
- [ ] Monitor email delivery rates
- [ ] Verify Slack notifications

#### Monthly
- [ ] Update lead scoring algorithms based on performance
- [ ] Review and optimize email templates
- [ ] Analyze conversion rates by workflow
- [ ] Update service-specific routing logic

#### Quarterly
- [ ] Review and update credential security
- [ ] Optimize workflow performance
- [ ] Update integration configurations
- [ ] Conduct comprehensive testing

### Troubleshooting

#### Common Issues

**Webhook Not Triggering**:
1. Verify webhook URL configuration
2. Check authentication headers
3. Validate JSON payload format
4. Review n8n execution logs

**CRM Integration Failures**:
1. Verify HubSpot API credentials
2. Check field mapping configurations
3. Validate required field values
4. Review API rate limits

**Email Delivery Issues**:
1. Check SMTP credentials
2. Verify sender domain authentication
3. Review email template formatting
4. Check spam filter configurations

**Slack Notifications Not Sending**:
1. Verify bot token permissions
2. Check channel access rights
3. Validate message formatting
4. Review Slack app configuration

### Performance Optimization

#### Lead Scoring Optimization
- Monitor conversion rates by score ranges
- Adjust scoring weights based on actual sales outcomes
- A/B test different routing thresholds
- Update qualification criteria quarterly

#### Email Template Optimization
- Track open and click rates
- A/B test subject lines and content
- Optimize for mobile responsiveness
- Update content based on service performance

#### Integration Performance
- Monitor API response times
- Implement retry logic for failed requests
- Cache frequently accessed data
- Optimize webhook payload sizes

## Support and Documentation

### Additional Resources
- **n8n Documentation**: https://docs.n8n.io/
- **HubSpot API Docs**: https://developers.hubspot.com/
- **Slack API Docs**: https://api.slack.com/
- **Calendly API Docs**: https://developer.calendly.com/
- **ElevenLabs API Docs**: https://docs.elevenlabs.io/

### Getting Help
- Review n8n community forums for common issues
- Check workflow execution logs for detailed error information
- Test individual nodes to isolate problems
- Verify all credential configurations and permissions

These workflows provide a comprehensive automation solution for Digital Wave Systems' lead capture and sales qualification process, designed for enterprise-grade reliability and scalability.
