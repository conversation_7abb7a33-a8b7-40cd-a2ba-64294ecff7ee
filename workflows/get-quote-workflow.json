{"name": "Get Quote - Lead Generation Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "get-quote", "authentication": "headerAuth", "options": {}}, "id": "webhook-get-quote", "name": "Webhook - Get Quote", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "get-quote-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validation-name", "leftValue": "={{ $json.name }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}, {"id": "validation-email", "leftValue": "={{ $json.email }}", "rightValue": "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$", "operator": {"type": "string", "operation": "regex"}}, {"id": "validation-service", "leftValue": "={{ $json.serviceInterest }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}, {"id": "validation-privacy", "leftValue": "={{ $json.privacyConsent }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "data-validation", "name": "Data Validation", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"jsCode": "// Lead Generation Scoring Algorithm\nconst data = $input.first().json;\n\nlet score = 20; // Base score for lead generation form\n\n// Service interest scoring\nconst serviceScores = {\n  'enterprise-solutions': 25,\n  'custom-software': 20,\n  'ai-ml': 20,\n  'cybersecurity': 15,\n  'cloud-devops': 15,\n  'web-application': 12,\n  'mobile-app': 12,\n  'not-sure': 8\n};\nscore += serviceScores[data.serviceInterest] || 10;\n\n// Timeline urgency scoring\nconst timelineScores = {\n  'immediate': 25,\n  'flexible': 15,\n  'exploring': 5\n};\nscore += timelineScores[data.timelineFlexibility];\n\n// Consultation type scoring (shows engagement level)\nconst consultationScores = {\n  'video': 20,\n  'phone': 15,\n  'email': 5\n};\nscore += consultationScores[data.consultationType];\n\n// Company provided\nif (data.company && data.company.trim().length > 0) {\n  score += 10;\n}\n\n// Phone provided\nif (data.phone && data.phone.trim().length > 0) {\n  score += 5;\n}\n\n// Cap at 100\nconst finalScore = Math.min(score, 100);\n\n// Determine nurturing sequence\nlet sequence, priority, responseTime, contentType;\n\nif (data.timelineFlexibility === 'immediate' || finalScore >= 70) {\n  sequence = 'high-intent-nurture';\n  priority = 'high';\n  responseTime = '4 hours';\n  contentType = 'solution-focused';\n} else if (data.timelineFlexibility === 'flexible' || finalScore >= 50) {\n  sequence = 'qualified-lead-nurture';\n  priority = 'medium';\n  responseTime = '12 hours';\n  contentType = 'educational-with-solutions';\n} else {\n  sequence = 'educational-nurture';\n  priority = 'low';\n  responseTime = '48 hours';\n  contentType = 'educational';\n}\n\n// Adjust based on consultation type\nif (data.consultationType === 'video' || data.consultationType === 'phone') {\n  if (priority === 'low') priority = 'medium';\n  sequence = 'consultation-focused-nurture';\n}\n\n// Service-specific adjustments\nif (['enterprise-solutions', 'custom-software', 'ai-ml'].includes(data.serviceInterest)) {\n  if (priority === 'low') priority = 'medium';\n  contentType = 'technical-focused';\n}\n\n// Generate content recommendations\nconst baseContent = [\n  'Software Development Best Practices Guide',\n  'Technology Stack Selection Checklist'\n];\n\nconst serviceContent = {\n  'custom-software': [\n    'Custom Software Development Process Guide',\n    'ROI Calculator for Custom Software Projects',\n    'Case Study: Enterprise Software Transformation'\n  ],\n  'web-application': [\n    'Modern Web Application Architecture Guide',\n    'Progressive Web App Benefits Whitepaper',\n    'Web Performance Optimization Checklist'\n  ],\n  'mobile-app': [\n    'Mobile App Development Strategy Guide',\n    'iOS vs Android: Platform Selection Guide',\n    'Mobile App Monetization Strategies'\n  ],\n  'ai-ml': [\n    'AI Implementation Roadmap for Businesses',\n    'Machine Learning Use Cases by Industry',\n    'AI Ethics and Compliance Guide'\n  ],\n  'cybersecurity': [\n    'Cybersecurity Assessment Checklist',\n    'Data Protection Compliance Guide',\n    'Security Architecture Best Practices'\n  ],\n  'cloud-devops': [\n    'Cloud Migration Strategy Guide',\n    'DevOps Implementation Roadmap',\n    'Cost Optimization in Cloud Infrastructure'\n  ],\n  'enterprise-solutions': [\n    'Enterprise Digital Transformation Guide',\n    'Legacy System Modernization Strategies',\n    'Enterprise Architecture Planning'\n  ]\n};\n\nconst serviceSpecificContent = serviceContent[data.serviceInterest] || [\n  'Technology Consulting Overview',\n  'Project Planning Best Practices'\n];\n\nconst contentRecommendations = [...baseContent, ...serviceSpecificContent];\n\nconst quoteId = `quote_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n\nreturn {\n  ...data,\n  quoteId,\n  leadScore: finalScore,\n  nurturingSequence: sequence,\n  priority,\n  responseTime,\n  contentType,\n  contentRecommendations,\n  timestamp: new Date().toISOString(),\n  source: 'lead_generation_form',\n  leadType: 'nurture_qualified',\n  formCompletionRate: 100,\n  dataQuality: 'medium'\n};"}, "id": "lead-scoring", "name": "Lead Scoring & Nurture Routing", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "high-intent-condition", "leftValue": "={{ $json.nurturingSequence }}", "rightValue": "high-intent-nurture", "operator": {"type": "string", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "nurture-routing", "name": "Nurture Sequence Routing", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "qualified-condition", "leftValue": "={{ $json.nurturingSequence }}", "rightValue": "qualified-lead-nurture", "operator": {"type": "string", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "qualified-routing", "name": "Qualified Lead Routing", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 400]}, {"parameters": {"resource": "contact", "operation": "create", "additionalFields": {"company": "={{ $json.company }}", "phone": "={{ $json.phone }}", "website": "={{ $json.pageUrl }}", "lifecyclestage": "lead", "lead_status": "new", "hs_lead_status": "NEW", "lead_source": "website_form", "service_interest": "={{ $json.serviceInterest }}", "consultation_type": "={{ $json.consultationType }}", "timeline_flexibility": "={{ $json.timelineFlexibility }}", "lead_score": "={{ $json.leadScore }}", "nurturing_sequence": "={{ $json.nurturingSequence }}"}, "email": "={{ $json.email }}", "firstname": "={{ $json.name.split(' ')[0] }}", "lastname": "={{ $json.name.split(' ').slice(1).join(' ') }}"}, "id": "hubspot-contact", "name": "Create HubSpot Contact", "type": "n8n-nodes-base.hubspot", "typeVersion": 2, "position": [1120, 200], "credentials": {"hubspotApi": {"id": "hubspot-credentials", "name": "HubSpot API"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": false,\n  \"error\": \"Validation failed\",\n  \"message\": \"Please fill in all required fields correctly\",\n  \"details\": [\n    $json.name ? \"\" : \"Name is required\",\n    /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test($json.email) ? \"\" : \"Valid email is required\",\n    $json.serviceInterest ? \"\" : \"Service interest is required\",\n    $json.privacyConsent ? \"\" : \"Privacy consent is required\"\n  ].filter(<PERSON><PERSON><PERSON>)\n} }}", "options": {}}, "id": "validation-error-response", "name": "Validation Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 400]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.email }}", "subject": "{{ $json.nurturingSequence === 'high-intent-nurture' ? 'Your development quote and next steps' : 'Thank you for your interest - Helpful resources inside' }}", "emailType": "html", "message": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Development Quote Response</title>\n    <style>\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background: linear-gradient(135deg, #3d4afc 0%, #667eea 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }\n        .content { background: white; padding: 30px; border: 1px solid #e1e5e9; }\n        .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; }\n        .button { display: inline-block; background: #3d4afc; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; }\n        .resources { background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; }\n        .next-steps { background: #e8f5e8; padding: 20px; border-radius: 6px; margin: 20px 0; }\n        .resource-list { list-style: none; padding: 0; }\n        .resource-list li { padding: 8px 0; border-bottom: 1px solid #e1e5e9; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>{{ $json.nurturingSequence === 'high-intent-nurture' ? 'Your Development Quote' : 'Thank You for Your Interest!' }}</h1>\n            <p>{{ $json.serviceInterest }} Development Solutions</p>\n        </div>\n        \n        <div class=\"content\">\n            <p>Dear {{ $json.name }},</p>\n            \n            <p>Thank you for your interest in our {{ $json.serviceInterest }} services. Based on your requirements, we've prepared some helpful resources for you.</p>\n            \n            <div class=\"resources\">\n                <h3>📚 Recommended Resources</h3>\n                <ul class=\"resource-list\">\n                    {{ $json.contentRecommendations.slice(0, 3).map(resource => `<li>📄 ${resource}</li>`).join('') }}\n                </ul>\n            </div>\n            \n            {{ $json.nurturingSequence === 'high-intent-nurture' ? `\n            <div class=\"next-steps\">\n                <h3>🚀 Next Steps</h3>\n                <p>Since you indicated an immediate timeline, our team will prioritize your request:</p>\n                <ul>\n                    <li><strong>Response Time:</strong> Within {{ $json.responseTime }}</li>\n                    <li><strong>Consultation:</strong> We'll schedule a detailed discussion</li>\n                    <li><strong>Proposal:</strong> You'll receive a customized quote</li>\n                </ul>\n            </div>\n            ` : `\n            <div class=\"next-steps\">\n                <h3>📈 Stay Connected</h3>\n                <p>We'll send you valuable insights and resources over the coming weeks to help with your {{ $json.serviceInterest }} project planning.</p>\n            </div>\n            `}\n            \n            {{ $json.consultationType === 'video' || $json.consultationType === 'phone' ? `\n            <p><strong>Consultation Request:</strong> We noted your interest in a {{ $json.consultationType }} consultation. Our team will reach out within {{ $json.responseTime }} to schedule this.</p>\n            ` : '' }}\n            \n            <div style=\"text-align: center; margin: 30px 0;\">\n                <a href=\"https://digitalwavesystems.com.co/case-studies\" class=\"button\">View Case Studies</a>\n                <a href=\"https://digitalwavesystems.com.co/services/{{ $json.serviceInterest }}\" class=\"button\">Learn More</a>\n            </div>\n            \n            <p>If you have any questions or would like to discuss your project immediately, please don't hesitate to reach out.</p>\n            \n            <p>Best regards,<br>\n            The Digital Wave Systems Team</p>\n        </div>\n        \n        <div class=\"footer\">\n            <p>Digital Wave Systems | Software Development Consultancy</p>\n            <p>📧 <EMAIL> | 🌐 digitalwavesystems.com.co</p>\n        </div>\n    </div>\n</body>\n</html>", "options": {}}, "id": "nurture-email", "name": "Send Nurture Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1340, 200], "credentials": {"smtp": {"id": "email-credentials", "name": "SMTP Email"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"message\": $json.timelineFlexibility === 'immediate' ? 'Thank you! Given your immediate timeline, our team will prioritize your request.' : 'Thank you! We\\'ll send you helpful resources and follow up soon.',\n  \"quoteId\": $json.quoteId,\n  \"leadScore\": $json.leadScore,\n  \"priority\": $json.priority,\n  \"nurturingSequence\": $json.nurturingSequence,\n  \"contentRecommendations\": $json.contentRecommendations.slice(0, 3),\n  \"nextSteps\": [\n    $json.consultationType === 'video' || $json.consultationType === 'phone' ? `We'll contact you within ${$json.responseTime} to schedule your ${$json.consultationType} consultation` : '',\n    'You\\'ll receive an email with helpful resources tailored to your project type',\n    'Our team will send you relevant case studies and guides',\n    'We\\'ll keep you updated on industry trends and best practices'\n  ].filter(<PERSON><PERSON><PERSON>)\n} }}", "options": {}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 200]}], "pinData": {}, "connections": {"Webhook - Get Quote": {"main": [[{"node": "Data Validation", "type": "main", "index": 0}]]}, "Data Validation": {"main": [[{"node": "Lead Scoring & Nurture Routing", "type": "main", "index": 0}], [{"node": "Validation Error Response", "type": "main", "index": 0}]]}, "Lead Scoring & Nurture Routing": {"main": [[{"node": "Nurture Sequence Routing", "type": "main", "index": 0}]]}, "Nurture Sequence Routing": {"main": [[{"node": "Create HubSpot Contact", "type": "main", "index": 0}], [{"node": "Qualified Lead Routing", "type": "main", "index": 0}]]}, "Qualified Lead Routing": {"main": [[{"node": "Create HubSpot Contact", "type": "main", "index": 0}], [{"node": "Create HubSpot Contact", "type": "main", "index": 0}]]}, "Create HubSpot Contact": {"main": [[{"node": "Send Nurture Email", "type": "main", "index": 0}]]}, "Send Nurture Email": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "get-quote-workflow", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "lead-generation", "name": "Lead Generation"}]}