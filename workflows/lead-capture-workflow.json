{"name": "Lead Capture - General Qualification Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "lead-capture", "authentication": "headerAuth", "options": {}}, "id": "webhook-lead-capture", "name": "Webhook - Lead Capture", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "lead-capture-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validation-name", "leftValue": "={{ $json.name }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}, {"id": "validation-email", "leftValue": "={{ $json.email }}", "rightValue": "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$", "operator": {"type": "string", "operation": "regex"}}], "combinator": "and"}, "options": {}}, "id": "data-validation", "name": "Data Validation", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"jsCode": "// General Lead Qualification and UTM Attribution\nconst data = $input.first().json;\n\nlet score = 30; // Base score for general lead capture\n\n// Service interest scoring\nconst serviceScores = {\n  'enterprise-solutions': 25,\n  'custom-software': 20,\n  'ai-ml': 20,\n  'cybersecurity': 15,\n  'cloud-devops': 15,\n  'web-application': 12,\n  'mobile-app': 12,\n  'consulting': 10,\n  'other': 5\n};\n\nif (data.serviceInterest) {\n  score += serviceScores[data.serviceInterest] || 10;\n}\n\n// Company information provided\nif (data.company && data.company.trim().length > 0) {\n  score += 10;\n}\n\n// Phone provided\nif (data.phone && data.phone.trim().length > 0) {\n  score += 8;\n}\n\n// Message/description provided\nif (data.message && data.message.length > 20) {\n  score += 12;\n}\n\n// Budget indication\nif (data.budget) {\n  const budgetScores = {\n    'under-10k': 5,\n    '10k-50k': 10,\n    '50k-100k': 15,\n    '100k-500k': 20,\n    '500k+': 25,\n    'flexible': 15\n  };\n  score += budgetScores[data.budget] || 10;\n}\n\n// Timeline urgency\nif (data.timeline) {\n  const timelineScores = {\n    'immediate': 20,\n    '1-month': 15,\n    '3-months': 10,\n    '6-months': 8,\n    'flexible': 5\n  };\n  score += timelineScores[data.timeline] || 5;\n}\n\n// UTM Attribution Analysis\nconst utmParams = data.utmParams || {};\nlet leadSource = 'direct';\nlet campaignType = 'organic';\n\nif (utmParams.source) {\n  leadSource = utmParams.source;\n  \n  // Adjust score based on traffic source quality\n  const sourceScores = {\n    'google': 10,\n    'linkedin': 15,\n    'facebook': 8,\n    'twitter': 6,\n    'email': 12,\n    'referral': 14,\n    'direct': 8\n  };\n  score += sourceScores[utmParams.source] || 5;\n}\n\nif (utmParams.medium) {\n  const mediumScores = {\n    'cpc': 12,\n    'organic': 10,\n    'email': 15,\n    'social': 8,\n    'referral': 12\n  };\n  score += mediumScores[utmParams.medium] || 5;\n}\n\n// Campaign analysis\nif (utmParams.campaign) {\n  campaignType = utmParams.campaign;\n  \n  // High-value campaign indicators\n  if (utmParams.campaign.includes('enterprise') || utmParams.campaign.includes('custom')) {\n    score += 10;\n  }\n}\n\n// Cap at 100\nconst finalScore = Math.min(score, 100);\n\n// Determine qualification level\nlet qualificationLevel, priority, responseTime, followUpSequence;\n\nif (finalScore >= 80) {\n  qualificationLevel = 'sales-qualified';\n  priority = 'high';\n  responseTime = '2 hours';\n  followUpSequence = 'sales-qualified-lead';\n} else if (finalScore >= 60) {\n  qualificationLevel = 'marketing-qualified';\n  priority = 'medium';\n  responseTime = '6 hours';\n  followUpSequence = 'marketing-qualified-lead';\n} else if (finalScore >= 40) {\n  qualificationLevel = 'lead';\n  priority = 'medium';\n  responseTime = '12 hours';\n  followUpSequence = 'standard-nurture';\n} else {\n  qualificationLevel = 'subscriber';\n  priority = 'low';\n  responseTime = '24 hours';\n  followUpSequence = 'educational-nurture';\n}\n\n// Service-specific routing\nlet assignedTeam = 'general-sales';\nif (['enterprise-solutions', 'custom-software'].includes(data.serviceInterest)) {\n  assignedTeam = 'enterprise-team';\n} else if (['ai-ml', 'cybersecurity'].includes(data.serviceInterest)) {\n  assignedTeam = 'specialized-team';\n}\n\nconst leadId = `lead_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n\nreturn {\n  ...data,\n  leadId,\n  leadScore: finalScore,\n  qualificationLevel,\n  priority,\n  responseTime,\n  followUpSequence,\n  assignedTeam,\n  leadSource,\n  campaignType,\n  timestamp: new Date().toISOString(),\n  source: 'general_lead_capture',\n  leadType: 'general_qualification',\n  formCompletionRate: 100,\n  dataQuality: data.company && data.phone && data.message ? 'high' : data.company || data.phone ? 'medium' : 'basic'\n};"}, "id": "lead-qualification", "name": "Lead Qualification & UTM Attribution", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "service-custom", "leftValue": "={{ $json.serviceInterest }}", "rightValue": "custom-software", "operator": {"type": "string", "operation": "equal"}}], "combinator": "or"}, "options": {}}, "id": "service-routing", "name": "Service-Specific Routing", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "enterprise-service", "leftValue": "={{ $json.serviceInterest }}", "rightValue": "enterprise-solutions", "operator": {"type": "string", "operation": "equal"}}], "combinator": "or"}, "options": {}}, "id": "enterprise-routing", "name": "Enterprise Service Routing", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 400]}, {"parameters": {"resource": "contact", "operation": "create", "additionalFields": {"company": "={{ $json.company }}", "phone": "={{ $json.phone }}", "website": "={{ $json.pageUrl }}", "lifecyclestage": "={{ $json.qualificationLevel === 'sales-qualified' ? 'salesqualifiedlead' : $json.qualificationLevel === 'marketing-qualified' ? 'marketingqualifiedlead' : 'lead' }}", "lead_status": "new", "hs_lead_status": "NEW", "lead_source": "={{ $json.leadSource }}", "service_interest": "={{ $json.serviceInterest }}", "lead_score": "={{ $json.leadScore }}", "qualification_level": "={{ $json.qualificationLevel }}", "utm_source": "={{ $json.utmParams.source }}", "utm_medium": "={{ $json.utmParams.medium }}", "utm_campaign": "={{ $json.utmParams.campaign }}", "utm_term": "={{ $json.utmParams.term }}", "utm_content": "={{ $json.utmParams.content }}", "assigned_team": "={{ $json.assignedTeam }}", "follow_up_sequence": "={{ $json.followUpSequence }}"}, "email": "={{ $json.email }}", "firstname": "={{ $json.name.split(' ')[0] }}", "lastname": "={{ $json.name.split(' ').slice(1).join(' ') }}"}, "id": "hubspot-contact", "name": "Create HubSpot Contact", "type": "n8n-nodes-base.hubspot", "typeVersion": 2, "position": [1120, 300], "credentials": {"hubspotApi": {"id": "hubspot-credentials", "name": "HubSpot API"}}}, {"parameters": {"channel": "={{ $json.qualificationLevel === 'sales-qualified' ? '#sales-qualified-leads' : $json.qualificationLevel === 'marketing-qualified' ? '#marketing-qualified-leads' : '#general-leads' }}", "text": "🎯 NEW LEAD CAPTURED", "attachments": [{"color": "={{ $json.qualificationLevel === 'sales-qualified' ? '#00ff00' : $json.qualificationLevel === 'marketing-qualified' ? '#ff8c00' : '#3d4afc' }}", "fields": [{"title": "Lead Score", "value": "={{ $json.leadScore }}/100", "short": true}, {"title": "Qualification", "value": "={{ $json.qualificationLevel.toUpperCase() }}", "short": true}, {"title": "Contact", "value": "={{ $json.name }}", "short": true}, {"title": "Company", "value": "={{ $json.company || 'Not provided' }}", "short": true}, {"title": "Service Interest", "value": "={{ $json.serviceInterest || 'General inquiry' }}", "short": true}, {"title": "Lead Source", "value": "={{ $json.leadSource }}", "short": true}, {"title": "Campaign", "value": "={{ $json.campaignType || 'Direct' }}", "short": true}, {"title": "Response Time", "value": "={{ $json.responseTime }}", "short": true}, {"title": "Message", "value": "={{ ($json.message || 'No message provided').substring(0, 200) }}{{ ($json.message || '').length > 200 ? '...' : '' }}", "short": false}], "footer": "Digital Wave Systems CRM", "ts": "={{ Math.floor(Date.now() / 1000) }}"}], "otherOptions": {}}, "id": "slack-notification", "name": "Slack Team Notification", "type": "n8n-nodes-base.slack", "typeVersion": 2, "position": [1120, 500], "credentials": {"slackApi": {"id": "slack-credentials", "name": "Slack API"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": false,\n  \"error\": \"Validation failed\",\n  \"message\": \"Please fill in all required fields correctly\",\n  \"details\": [\n    $json.name ? \"\" : \"Name is required\",\n    /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test($json.email) ? \"\" : \"Valid email is required\"\n  ].filter(Bo<PERSON>an)\n} }}", "options": {}}, "id": "validation-error-response", "name": "Validation Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 400]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.email }}", "subject": "Thank you for contacting Digital Wave Systems", "emailType": "html", "message": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Thank You for Contacting Us</title>\n    <style>\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background: linear-gradient(135deg, #3d4afc 0%, #667eea 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }\n        .content { background: white; padding: 30px; border: 1px solid #e1e5e9; }\n        .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; }\n        .button { display: inline-block; background: #3d4afc; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; }\n        .lead-details { background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; }\n        .next-steps { background: #e8f5e8; padding: 20px; border-radius: 6px; margin: 20px 0; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>Thank You for Reaching Out!</h1>\n            <p>We've received your inquiry and will respond soon</p>\n        </div>\n        \n        <div class=\"content\">\n            <p>Dear {{ $json.name }},</p>\n            \n            <p>Thank you for contacting Digital Wave Systems. We've received your inquiry and our {{ $json.assignedTeam }} will review it and respond within {{ $json.responseTime }}.</p>\n            \n            <div class=\"lead-details\">\n                <h3>📋 Your Inquiry Summary</h3>\n                <p><strong>Contact:</strong> {{ $json.name }}</p>\n                {{ $json.company ? `<p><strong>Company:</strong> ${$json.company}</p>` : '' }}\n                {{ $json.serviceInterest ? `<p><strong>Service Interest:</strong> ${$json.serviceInterest}</p>` : '' }}\n                <p><strong>Lead ID:</strong> {{ $json.leadId }}</p>\n                <p><strong>Qualification Level:</strong> {{ $json.qualificationLevel.charAt(0).toUpperCase() + $json.qualificationLevel.slice(1).replace('-', ' ') }}</p>\n            </div>\n            \n            <div class=\"next-steps\">\n                <h3>🚀 What Happens Next?</h3>\n                <ul>\n                    <li><strong>Response Time:</strong> Our team will contact you within {{ $json.responseTime }}</li>\n                    <li><strong>Follow-up Sequence:</strong> You'll be enrolled in our {{ $json.followUpSequence.replace('-', ' ') }} sequence</li>\n                    <li><strong>Resources:</strong> We'll send you relevant case studies and insights</li>\n                    {{ $json.qualificationLevel === 'sales-qualified' ? '<li><strong>Sales Call:</strong> A senior consultant will schedule a detailed discussion</li>' : '' }}\n                </ul>\n            </div>\n            \n            {{ $json.serviceInterest ? `\n            <p>Since you're interested in <strong>${$json.serviceInterest}</strong>, here are some relevant resources to explore while you wait:</p>\n            ` : '' }}\n            \n            <div style=\"text-align: center; margin: 30px 0;\">\n                <a href=\"https://digitalwavesystems.com.co/case-studies\" class=\"button\">View Case Studies</a>\n                {{ $json.serviceInterest ? `<a href=\"https://digitalwavesystems.com.co/services/${$json.serviceInterest}\" class=\"button\">Learn More</a>` : '' }}\n                <a href=\"https://digitalwavesystems.com.co/blog\" class=\"button\">Read Our Blog</a>\n            </div>\n            \n            <p>If you have any urgent questions or need immediate assistance, please don't hesitate to call us directly.</p>\n            \n            <p>Best regards,<br>\n            The Digital Wave Systems Team</p>\n        </div>\n        \n        <div class=\"footer\">\n            <p>Digital Wave Systems | Software Development Consultancy</p>\n            <p>📧 <EMAIL> | 🌐 digitalwavesystems.com.co</p>\n        </div>\n    </div>\n</body>\n</html>", "options": {}}, "id": "welcome-email", "name": "Send Welcome Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1340, 300], "credentials": {"smtp": {"id": "email-credentials", "name": "SMTP Email"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"Thank you for contacting us! We'll respond within \" + $json.responseTime + \".\",\n  \"leadId\": $json.leadId,\n  \"leadScore\": $json.leadScore,\n  \"qualificationLevel\": $json.qualificationLevel,\n  \"priority\": $json.priority,\n  \"responseTime\": $json.responseTime,\n  \"assignedTeam\": $json.assignedTeam,\n  \"nextSteps\": [\n    `Our ${$json.assignedTeam.replace('-', ' ')} will review your inquiry within ${$json.responseTime}`,\n    `You'll be enrolled in our ${$json.followUpSequence.replace('-', ' ')} sequence`,\n    'We\\'ll send you relevant resources and case studies',\n    $json.qualificationLevel === 'sales-qualified' ? 'A senior consultant will schedule a detailed discussion with you' : 'We\\'ll keep you updated with valuable insights and industry trends'\n  ]\n} }}", "options": {}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 300]}], "pinData": {}, "connections": {"Webhook - Lead Capture": {"main": [[{"node": "Data Validation", "type": "main", "index": 0}]]}, "Data Validation": {"main": [[{"node": "Lead Qualification & UTM Attribution", "type": "main", "index": 0}], [{"node": "Validation Error Response", "type": "main", "index": 0}]]}, "Lead Qualification & UTM Attribution": {"main": [[{"node": "Service-Specific Routing", "type": "main", "index": 0}]]}, "Service-Specific Routing": {"main": [[{"node": "Create HubSpot Contact", "type": "main", "index": 0}], [{"node": "Enterprise Service Routing", "type": "main", "index": 0}]]}, "Enterprise Service Routing": {"main": [[{"node": "Create HubSpot Contact", "type": "main", "index": 0}], [{"node": "Create HubSpot Contact", "type": "main", "index": 0}]]}, "Create HubSpot Contact": {"main": [[{"node": "Slack Team Notification", "type": "main", "index": 0}]]}, "Slack Team Notification": {"main": [[{"node": "Send Welcome Email", "type": "main", "index": 0}]]}, "Send Welcome Email": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "lead-capture-workflow", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "general-qualification", "name": "General Qualification"}]}