{"name": "Quick Quote - Consultation Scheduling Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "quick-quote", "authentication": "headerAuth", "options": {}}, "id": "webhook-quick-quote", "name": "Webhook - Quick Quote", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "quick-quote-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validation-name", "leftValue": "={{ $json.name }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}, {"id": "validation-email", "leftValue": "={{ $json.email }}", "rightValue": "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$", "operator": {"type": "string", "operation": "regex"}}, {"id": "validation-consultation", "leftValue": "={{ $json.consultationType }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}], "combinator": "and"}, "options": {}}, "id": "data-validation", "name": "Data Validation", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "video-consultation", "leftValue": "={{ $json.consultationType }}", "rightValue": "video", "operator": {"type": "string", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "consultation-routing", "name": "Consultation Type Routing", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "phone-consultation", "leftValue": "={{ $json.consultationType }}", "rightValue": "phone", "operator": {"type": "string", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "phone-routing", "name": "Phone Consultation Routing", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 500]}, {"parameters": {"url": "https://api.calendly.com/scheduled_events", "authentication": "predefinedCredentialType", "nodeCredentialType": "calen<PERSON><PERSON><PERSON>", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.calendlyApi.accessToken }}"}]}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "user", "value": "https://api.calendly.com/users/{{ $credentials.calendlyApi.userId }}"}, {"name": "status", "value": "active"}]}, "options": {}}, "id": "calendly-integration", "name": "Calendly Integration", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 200], "credentials": {"calendlyApi": {"id": "calendly-credentials", "name": "Calendly API"}}}, {"parameters": {"jsCode": "// Generate consultation ID and prepare callback data\nconst data = $input.first().json;\n\nconst consultationId = `consult_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n\n// Determine callback priority based on urgency and service type\nlet priority = 'medium';\nlet responseTime = '2 hours';\n\nif (data.urgency === 'immediate' || data.serviceType === 'enterprise-solutions') {\n  priority = 'high';\n  responseTime = '1 hour';\n} else if (data.serviceType === 'custom-software' || data.serviceType === 'ai-ml') {\n  priority = 'medium';\n  responseTime = '2 hours';\n} else {\n  priority = 'low';\n  responseTime = '4 hours';\n}\n\n// Generate callback time slot (next business hour)\nconst now = new Date();\nconst callbackTime = new Date(now.getTime() + (2 * 60 * 60 * 1000)); // 2 hours from now\n\n// Format callback time\nconst timeOptions = {\n  timeZone: 'America/Bogota',\n  year: 'numeric',\n  month: '2-digit',\n  day: '2-digit',\n  hour: '2-digit',\n  minute: '2-digit',\n  hour12: true\n};\n\nconst formattedCallbackTime = callbackTime.toLocaleString('en-US', timeOptions);\n\nreturn {\n  ...data,\n  consultationId,\n  priority,\n  responseTime,\n  callbackTime: callbackTime.toISOString(),\n  formattedCallbackTime,\n  timestamp: new Date().toISOString(),\n  source: 'quick_quote_phone',\n  consultationType: 'phone_callback'\n};"}, "id": "phone-callback-prep", "name": "Phone Callback Preparation", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 400]}, {"parameters": {"url": "https://api.elevenlabs.io/v1/text-to-speech/{{ $env.ELEVENLABS_VOICE_ID }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "elevenlabsApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{ $credentials.elevenlabsApi.apiKey }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "Hello {{ $json.name }}, this is Digital Wave Systems calling about your {{ $json.serviceType || 'software development' }} inquiry. We received your request for a phone consultation and wanted to discuss your project requirements. Is this a good time to talk?"}, {"name": "model_id", "value": "eleven_monolingual_v1"}, {"name": "voice_settings", "value": {"stability": 0.5, "similarity_boost": 0.5}}]}, "options": {}}, "id": "ai-voice-prep", "name": "AI Voice Preparation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 600], "credentials": {"elevenlabsApi": {"id": "elevenlabs-credentials", "name": "ElevenLabs API"}}}, {"parameters": {"resource": "contact", "operation": "create", "additionalFields": {"company": "={{ $json.company }}", "phone": "={{ $json.phone }}", "website": "={{ $json.pageUrl }}", "lifecyclestage": "lead", "lead_status": "consultation_requested", "hs_lead_status": "OPEN", "lead_source": "quick_quote_form", "consultation_type": "={{ $json.consultationType }}", "consultation_id": "={{ $json.consultationId }}", "callback_time": "={{ $json.callbackTime }}", "priority": "={{ $json.priority }}"}, "email": "={{ $json.email }}", "firstname": "={{ $json.name.split(' ')[0] }}", "lastname": "={{ $json.name.split(' ').slice(1).join(' ') }}"}, "id": "hubspot-contact", "name": "Create HubSpot Contact", "type": "n8n-nodes-base.hubspot", "typeVersion": 2, "position": [1120, 300], "credentials": {"hubspotApi": {"id": "hubspot-credentials", "name": "HubSpot API"}}}, {"parameters": {"channel": "={{ $json.consultationType === 'phone' ? '#phone-consultations' : '#video-consultations' }}", "text": "📞 NEW CONSULTATION REQUEST", "attachments": [{"color": "{{ $json.priority === 'high' ? '#ff8c00' : '#3d4afc' }}", "fields": [{"title": "Type", "value": "={{ $json.consultationType.toUpperCase() }} Consultation", "short": true}, {"title": "Priority", "value": "={{ $json.priority.toUpperCase() }}", "short": true}, {"title": "Contact", "value": "={{ $json.name }}", "short": true}, {"title": "Company", "value": "={{ $json.company || 'Not provided' }}", "short": true}, {"title": "Phone", "value": "={{ $json.phone || 'Not provided' }}", "short": true}, {"title": "Response Time", "value": "={{ $json.responseTime }}", "short": true}, {"title": "Callback Time", "value": "={{ $json.formattedCallbackTime || 'ASAP' }}", "short": false}], "footer": "Digital Wave Systems CRM", "ts": "={{ Math.floor(Date.now() / 1000) }}"}], "otherOptions": {}}, "id": "slack-notification", "name": "Slack Team Notification", "type": "n8n-nodes-base.slack", "typeVersion": 2, "position": [1120, 500], "credentials": {"slackApi": {"id": "slack-credentials", "name": "Slack API"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": false,\n  \"error\": \"Validation failed\",\n  \"message\": \"Please fill in all required fields correctly\",\n  \"details\": [\n    $json.name ? \"\" : \"Name is required\",\n    /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test($json.email) ? \"\" : \"Valid email is required\",\n    $json.consultationType ? \"\" : \"Consultation type is required\"\n  ].filter(<PERSON><PERSON><PERSON>)\n} }}", "options": {}}, "id": "validation-error-response", "name": "Validation Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 700]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.email }}", "subject": "{{ $json.consultationType === 'phone' ? 'Phone consultation scheduled - We\\'ll call you soon!' : 'Video consultation confirmed - Calendar invite sent' }}", "emailType": "html", "message": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Consultation Confirmation</title>\n    <style>\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background: linear-gradient(135deg, #3d4afc 0%, #667eea 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }\n        .content { background: white; padding: 30px; border: 1px solid #e1e5e9; }\n        .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; }\n        .button { display: inline-block; background: #3d4afc; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; }\n        .consultation-details { background: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; }\n        .preparation { background: #e8f5e8; padding: 20px; border-radius: 6px; margin: 20px 0; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>{{ $json.consultationType === 'phone' ? '📞 Phone Consultation Scheduled' : '🎥 Video Consultation Confirmed' }}</h1>\n            <p>We're excited to discuss your project!</p>\n        </div>\n        \n        <div class=\"content\">\n            <p>Dear {{ $json.name }},</p>\n            \n            <p>Thank you for requesting a {{ $json.consultationType }} consultation with Digital Wave Systems. {{ $json.consultationType === 'phone' ? 'Our team will call you within ' + $json.responseTime + '.' : 'Your video consultation has been confirmed.' }}</p>\n            \n            <div class=\"consultation-details\">\n                <h3>📋 Consultation Details</h3>\n                <p><strong>Type:</strong> {{ $json.consultationType.charAt(0).toUpperCase() + $json.consultationType.slice(1) }} Consultation</p>\n                <p><strong>Contact:</strong> {{ $json.name }}</p>\n                {{ $json.company ? `<p><strong>Company:</strong> ${$json.company}</p>` : '' }}\n                {{ $json.phone ? `<p><strong>Phone:</strong> ${$json.phone}</p>` : '' }}\n                {{ $json.consultationType === 'phone' ? `<p><strong>Expected Call Time:</strong> ${$json.formattedCallbackTime || 'Within ' + $json.responseTime}</p>` : '' }}\n                <p><strong>Consultation ID:</strong> {{ $json.consultationId }}</p>\n            </div>\n            \n            <div class=\"preparation\">\n                <h3>🚀 How to Prepare</h3>\n                <ul>\n                    <li><strong>Project Overview:</strong> Be ready to discuss your project goals and requirements</li>\n                    <li><strong>Timeline:</strong> Think about your desired project timeline and key milestones</li>\n                    <li><strong>Budget:</strong> Have a rough budget range in mind for the discussion</li>\n                    <li><strong>Questions:</strong> Prepare any specific questions about our development process</li>\n                </ul>\n            </div>\n            \n            {{ $json.consultationType === 'phone' ? `\n            <p><strong>📞 Phone Consultation:</strong> Please ensure your phone is available and you're in a quiet location for our call. We'll call the number you provided: ${$json.phone || 'Please reply with your phone number'}.</p>\n            ` : `\n            <p><strong>🎥 Video Consultation:</strong> You should receive a calendar invitation shortly with the video meeting link. Please test your camera and microphone before the call.</p>\n            `}\n            \n            <div style=\"text-align: center; margin: 30px 0;\">\n                <a href=\"https://digitalwavesystems.com.co/case-studies\" class=\"button\">View Our Work</a>\n                <a href=\"https://digitalwavesystems.com.co/process\" class=\"button\">Our Process</a>\n            </div>\n            \n            <p>If you need to reschedule or have any questions before our consultation, please don't hesitate to contact us.</p>\n            \n            <p>Looking forward to speaking with you!</p>\n            \n            <p>Best regards,<br>\n            The Digital Wave Systems Team</p>\n        </div>\n        \n        <div class=\"footer\">\n            <p>Digital Wave Systems | Software Development Consultancy</p>\n            <p>📧 <EMAIL> | 🌐 digitalwavesystems.com.co</p>\n        </div>\n    </div>\n</body>\n</html>", "options": {}}, "id": "consultation-email", "name": "Send Consultation Confirmation", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1340, 300], "credentials": {"smtp": {"id": "email-credentials", "name": "SMTP Email"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"message\": $json.consultationType === 'phone' ? `Perfect! We'll call you within ${$json.responseTime}.` : 'Great! Your video consultation has been scheduled.',\n  \"consultationId\": $json.consultationId,\n  \"consultationType\": $json.consultationType,\n  \"priority\": $json.priority,\n  \"responseTime\": $json.responseTime,\n  \"callbackTime\": $json.formattedCallbackTime,\n  \"nextSteps\": [\n    $json.consultationType === 'phone' ? `We'll call you at ${$json.phone || 'the number you provided'} within ${$json.responseTime}` : 'You\\'ll receive a calendar invitation with the video meeting link',\n    'Please prepare an overview of your project requirements',\n    'Think about your timeline and budget expectations',\n    'Prepare any specific questions about our development process'\n  ]\n} }}", "options": {}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 300]}], "pinData": {}, "connections": {"Webhook - Quick Quote": {"main": [[{"node": "Data Validation", "type": "main", "index": 0}]]}, "Data Validation": {"main": [[{"node": "Consultation Type Routing", "type": "main", "index": 0}], [{"node": "Validation Error Response", "type": "main", "index": 0}]]}, "Consultation Type Routing": {"main": [[{"node": "Calendly Integration", "type": "main", "index": 0}], [{"node": "Phone Consultation Routing", "type": "main", "index": 0}]]}, "Phone Consultation Routing": {"main": [[{"node": "Phone Callback Preparation", "type": "main", "index": 0}], [{"node": "Create HubSpot Contact", "type": "main", "index": 0}]]}, "Phone Callback Preparation": {"main": [[{"node": "AI Voice Preparation", "type": "main", "index": 0}]]}, "AI Voice Preparation": {"main": [[{"node": "Create HubSpot Contact", "type": "main", "index": 0}]]}, "Calendly Integration": {"main": [[{"node": "Create HubSpot Contact", "type": "main", "index": 0}]]}, "Create HubSpot Contact": {"main": [[{"node": "Slack Team Notification", "type": "main", "index": 0}]]}, "Slack Team Notification": {"main": [[{"node": "Send Consultation Confirmation", "type": "main", "index": 0}]]}, "Send Consultation Confirmation": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "quick-quote-workflow", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "consultation-scheduling", "name": "Consultation Scheduling"}]}