# Digital Wave Systems - Lead Capture & Automation Platform

## Project Overview

Digital Wave Systems is a comprehensive software development consultancy website built with Next.js 14, featuring an advanced lead capture and automation system. The platform implements intent-based CTA forms with sophisticated n8n automation workflows to optimize lead qualification and sales processes.

### Key Features

- **Intent-Based Lead Capture**: Multiple form types targeting different user intent levels
- **Advanced Lead Scoring**: Differentiated algorithms for enterprise vs. lead generation forms
- **n8n Automation Integration**: Professional workflows for lead routing and follow-up
- **HeroUI Design System**: Consistent, professional UI components throughout
- **Responsive Design**: Mobile-first approach with modern web standards
- **Analytics Integration**: Comprehensive tracking and conversion optimization

## Architecture Overview

### Technology Stack

- **Frontend**: Next.js 14 with TypeScript
- **UI Framework**: HeroUI (NextUI) design system
- **Styling**: Tailwind CSS with HeroUI components
- **Animations**: Anime.js for professional animations
- **Forms**: React Hook Form with HeroUI validation
- **API**: Next.js API routes for webhook endpoints
- **Automation**: n8n workflows for lead processing
- **Analytics**: Google Analytics 4 integration

### Design System

- **Primary Brand Color**: `#3d4afc`
- **Typography**: HeroUI text utilities
- **Components**: Exclusively HeroUI components (Card, Button, Input, etc.)
- **Responsive**: Mobile-first with HeroUI breakpoints
- **Dark Mode**: Full dark mode support throughout

## CTA Forms Implementation

### Form Strategy

The platform implements a dual-modal approach with forms optimized for different user intent levels:

#### 1. "Start Your Project" Form (High-Intent Sales)

**Purpose**: Enterprise sales qualification for immediate project needs

**Target Audience**:
- Decision makers with budget approval
- Companies with defined project requirements
- Enterprise clients ready for detailed discussions

**Form Structure**:
- **5-step multi-step form** with progress tracking
- **25+ comprehensive fields** covering:
  - Contact and company information
  - Detailed project requirements
  - Technical specifications and compliance needs
  - Decision-making process and timeline
  - Budget range (up to $2M+)

**Lead Scoring Algorithm**:
```typescript
Base Score: 50 points
+ Budget Range: up to 30 points (enterprise budgets prioritized)
+ Company Size: up to 20 points (enterprise = highest score)
+ Urgency Level: up to 15 points (urgent = immediate attention)
+ Decision Speed: up to 15 points (faster decision = higher score)
+ Complexity Bonus: up to 20 points (compliance, integrations, platforms)
```

**Sales Routing**:
- **Score 85+ or Urgent**: Executive team, 1-hour response
- **Score 70+ or High**: Senior sales, 2-hour response
- **Score 55+**: Sales team, 4-hour response
- **Score <55**: Sales team, 8-hour response

#### 2. "Get Development Quote" Form (Lead Generation)

**Purpose**: Lead nurturing for early-stage prospects

**Target Audience**:
- Researchers exploring solutions
- Early-stage prospects without immediate budget
- Users seeking educational content and guidance

**Form Structure**:
- **Single-step minimal form** for maximum conversion
- **8 core fields** including:
  - Basic contact information
  - Service interest selection
  - Consultation preference (video/phone/email)
  - Timeline flexibility

**Lead Scoring Algorithm**:
```typescript
Base Score: 20 points
+ Service Interest: up to 25 points (enterprise solutions = highest)
+ Timeline Urgency: up to 25 points (immediate = priority)
+ Consultation Type: up to 20 points (video > phone > email)
+ Context Bonus: up to 15 points (company info, phone provided)
```

**Nurturing Sequences**:
- **High-Intent Nurture**: Solution-focused content, 4-hour response
- **Qualified Lead Nurture**: Educational with solutions, 12-hour response
- **Educational Nurture**: Pure educational content, 48-hour response

### Consultation Scheduling Logic

**Problem Solved**: Prevents premature Calendly display before user commitment

**Implementation**:
1. **Step 1**: User selects consultation type (video/phone/email)
2. **Step 2**: User clicks specific action button
3. **Step 3**: Show appropriate integration (Calendly for video, n8n webhook for phone)

**Calendly Integration**:
```typescript
const calendlyUrl = `https://calendly.com/your-link?
  hide_event_type_details=1&
  hide_gdpr_banner=1&
  prefill_name=${encodeURIComponent(name)}&
  prefill_email=${encodeURIComponent(email)}`;
```

## API Endpoints

### Webhook Endpoints

#### `/api/n8n-webhooks/start-project`
- **Purpose**: High-intent project inquiries
- **Method**: POST
- **Authentication**: Bearer token
- **Payload**: Comprehensive project data (25+ fields)
- **Response**: Project ID, lead score, priority, next steps

#### `/api/n8n-webhooks/get-quote`
- **Purpose**: Lead generation quote requests
- **Method**: POST
- **Authentication**: Bearer token
- **Payload**: Minimal lead data (8 fields)
- **Response**: Quote ID, lead score, nurturing sequence, content recommendations

#### `/api/n8n-webhooks/quick-quote`
- **Purpose**: Immediate consultation scheduling
- **Method**: POST
- **Authentication**: Bearer token
- **Payload**: Consultation preference data
- **Response**: Consultation ID, callback timing, next steps

#### `/api/n8n-webhooks/lead-capture`
- **Purpose**: General lead qualification
- **Method**: POST
- **Authentication**: Bearer token
- **Payload**: Progressive profiling data
- **Response**: Lead ID, qualification status, routing information

### Data Validation

All endpoints implement comprehensive validation:
- **Required field validation** with specific error messages
- **Email format validation** using regex patterns
- **Data sanitization** to prevent injection attacks
- **Rate limiting** to prevent spam and abuse
- **CORS protection** for secure cross-origin requests

## n8n Integration Strategy

### Workflow Architecture

```
Website Form → API Endpoint → n8n Webhook → 
Lead Scoring → Routing Logic → CRM Integration → 
Team Notification → Follow-up Automation → 
Engagement Tracking → Progressive Qualification
```

### Automation Workflows

#### 1. High-Intent Project Workflow
- **Lead Scoring**: Enterprise-focused algorithm
- **Executive Routing**: Immediate attention for high-value leads
- **CRM Integration**: Detailed opportunity creation
- **Proposal Automation**: Automated proposal generation process

#### 2. Lead Generation Workflow
- **Nurture Sequences**: Educational content delivery
- **Progressive Qualification**: Engagement-based scoring
- **Content Personalization**: Service-specific resources
- **Consultation Scheduling**: Automated booking when ready

#### 3. Consultation Booking Workflow
- **Calendar Integration**: Calendly API for video calls
- **Callback Automation**: Immediate phone consultation setup
- **Preparation Materials**: Pre-consultation resources
- **Follow-up Sequences**: Post-consultation proposal delivery

### Integration Points

- **CRM**: HubSpot/Salesforce for lead management
- **Email**: SendGrid/Mailchimp for automated sequences
- **Calendar**: Calendly and Google Calendar integration
- **Notifications**: Slack and email alerts for team coordination
- **Analytics**: Google Analytics for conversion tracking

## Getting Started

### Prerequisites

- Node.js 18+ and npm/yarn
- n8n instance (cloud or self-hosted)
- CRM account (HubSpot/Salesforce)
- Email service provider account
- Calendly account for consultation booking

### Installation

1. **Clone the repository**:
```bash
git clone https://github.com/your-org/dws-web.git
cd dws-web
```

2. **Install dependencies**:
```bash
npm install
# or
yarn install
```

3. **Environment configuration**:
```bash
cp .env.example .env.local
```

4. **Configure environment variables**:
```bash
# Database
DATABASE_URL="your_database_url"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your_nextauth_secret"

# n8n Integration
N8N_WEBHOOK_SECRET="your_secure_webhook_secret"
N8N_LEAD_CAPTURE_WEBHOOK_URL="https://n8n-4u4r.onrender.com/webhook/lead-capture"
N8N_START_PROJECT_WEBHOOK_URL="https://n8n-4u4r.onrender.com/webhook/start-project"
N8N_GET_QUOTE_WEBHOOK_URL="https://n8n-4u4r.onrender.com/webhook/get-quote"
N8N_QUICK_QUOTE_WEBHOOK_URL="https://n8n-4u4r.onrender.com/webhook/quick-quote"

# Analytics
GOOGLE_ANALYTICS_ID="your_ga_id"
```

5. **Start development server**:
```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Testing

### Automated Testing

Run the comprehensive test suite:
```bash
# Test all CTA integration endpoints
npm run test:cta

# Or run manually
node scripts/test-cta-integration.js
```

### Manual Testing Checklist

#### Form Functionality
- [ ] All forms submit successfully with valid data
- [ ] Validation works correctly for required fields
- [ ] Error messages display appropriately
- [ ] Success messages and redirects work

#### n8n Integration
- [ ] Webhooks receive data correctly
- [ ] Lead scoring algorithms function properly
- [ ] CRM integration creates records
- [ ] Email sequences trigger appropriately

#### Consultation Scheduling
- [ ] Calendly integration works for video calls
- [ ] Phone callback requests trigger properly
- [ ] Email-only requests route correctly
- [ ] Confirmation emails send successfully

### Performance Testing

- **Form Submission**: Target <2 seconds response time
- **Webhook Processing**: Complete within 5 minutes
- **Page Load Speed**: <3 seconds for all pages
- **Mobile Performance**: Lighthouse score >90

## Documentation

### Available Documentation

- **n8n Workflow Design**: `/docs/n8n-workflow-design-prompt.md`
- **Dual Modal Forms**: `/docs/Dual-Modal-Forms-Implementation.md`
- **Quick Quote Guide**: `/docs/Quick-Quote-Implementation.md`
- **CTA Enhancement**: `/docs/CTA-Enhancement-README.md`
- **n8n Integration**: `/docs/n8n-integration-guide.md`

### Key Metrics

#### Conversion Metrics
- **Form completion rates** by source and type
- **Lead quality scores** distribution
- **Sales qualified lead rates**
- **Revenue attribution** by form source

#### Performance Metrics
- **API response times** for all endpoints
- **Webhook success rates** and error tracking
- **n8n workflow execution** monitoring
- **User experience metrics** (Core Web Vitals)

## Deployment

### Production Deployment

1. **Build the application**:
```bash
npm run build
```

2. **Deploy to your hosting platform** (Vercel, Netlify, etc.)

3. **Configure production environment variables**

4. **Set up n8n workflows** using the provided workflow designs

### Environment Variables

See `.env.example` for complete list of required environment variables including:
- Database connection strings
- Authentication secrets
- n8n webhook URLs and secrets
- Third-party API keys (Analytics, Email, CRM)

## Support and Contributing

### Development Guidelines

1. **Follow HeroUI design patterns** - Use only HeroUI components
2. **Maintain TypeScript types** - All new code must be properly typed
3. **Test thoroughly** - Include tests for new functionality
4. **Document changes** - Update relevant documentation
5. **Performance first** - Optimize for speed and user experience

### Code Review Process

1. Create feature branch from main
2. Implement changes with comprehensive testing
3. Submit pull request with detailed description
4. Code review and approval required
5. Merge to main and deploy to staging
6. Production deployment after validation

---

**Digital Wave Systems** - Transforming businesses through innovative software solutions and intelligent automation.
