import type { Config } from "tailwindcss";
import { heroui } from "@heroui/react";

const config: Config = {
  darkMode: "class",
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: "#3d4afc",
        "primary-dark": "#2847c3",
        "primary-light": "#8094fa",
        "primary-50": "#f0f2ff",
        "primary-100": "#e6eaff",
        "primary-200": "#d1d9ff",
        "primary-300": "#b3c0ff",
        "primary-400": "#8094fa",
        "primary-500": "#3d4afc",
        "primary-600": "#2847c3",
        "primary-700": "#1e3a9e",
        "primary-800": "#1a2f7a",
        "primary-900": "#162556",
        dark: "#0a0e1a",
        "dark-2": "#0f1419",
        "dark-3": "#151b26",
        "dark-4": "#1a2332",
        "dark-5": "#1f2b3e",
        "dark-6": "#8892b0",
        "dark-7": "#a8b2d1",
        "body-color": "#64748b",
        "body-color-dark": "#94a3b8",
        stroke: "#e2e8f0",
        "stroke-dark": "#334155",
        blue: {
          50: "#eff6ff",
          100: "#dbeafe",
          200: "#bfdbfe",
          300: "#93c5fd",
          400: "#60a5fa",
          500: "#3b82f6",
          600: "#2563eb",
          700: "#1d4ed8",
          800: "#1e40af",
          900: "#1e3a8a",
          950: "#172554",
        },
        gray: {
          50: "#f8fafc",
          100: "#f1f5f9",
          200: "#e2e8f0",
          300: "#cbd5e1",
          400: "#94a3b8",
          500: "#64748b",
          600: "#475569",
          700: "#334155",
          800: "#1e293b",
          900: "#0f172a",
          950: "#020617",
        },
      },
      boxShadow: {
        "card-shadow": "0px 10px 30px  #000000", 
        "card-shadow-dark": "0px 10px 30px rgba(0, 0, 0, 0.18)",
        "input-shadow": "0px 4px 10px rgba(0, 0, 0, 0.04)",
        "input-shadow-dark": "0px 4px 10px rgba(0, 0, 0, 0.15)",
        "pricing-shadow": "0px 30px 60px rgba(0, 0, 0, 0.08)",
        "pricing-shadow-dark": "0px 30px 60px rgba(0, 0, 0, 0.18)",
        "icon-shadow": "0px 10px 15px rgba(74, 108, 247, 0.2)",
        "icon-shadow-dark": "0px 10px 15px rgba(74, 108, 247, 0.3)",
        "shape-shadow": "20px 20px 50px rgba(0, 0, 0, 0.08)",
        "shape-shadow-dark": "20px 20px 50px rgba(0, 0, 0, 0.2)",
        "lg": "0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
        "lg-dark": "0 10px 25px -3px rgba(0, 0, 0, 0.25), 0 4px 6px -2px rgba(0, 0, 0, 0.2)",
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic": "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      fontFamily: {
        'sans': ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
      },
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1.2' }],
        '6xl': ['3.75rem', { lineHeight: '1.2' }],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'scale-in': 'scaleIn 0.3s ease-out',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
      transitionTimingFunction: {
        'bounce-soft': 'cubic-bezier(0.34, 1.56, 0.64, 1)',
      },
    },
  },
  plugins: [
    require("tailgrids/plugin"),
    heroui({
      themes: {
        light: {
          colors: {
            primary: {
              50: "#f0f2ff",
              100: "#e6eaff",
              200: "#d1d9ff",
              300: "#b3c0ff",
              400: "#8094fa",
              500: "#3d4afc",
              600: "#2847c3",
              700: "#1e3a9e",
              800: "#1a2f7a",
              900: "#162556",
              DEFAULT: "#3d4afc",
              foreground: "#ffffff",
            },
            focus: "#3d4afc",
          },
        },
        dark: {
          colors: {
            primary: {
              50: "#f0f2ff",
              100: "#e6eaff",
              200: "#d1d9ff",
              300: "#b3c0ff",
              400: "#8094fa",
              500: "#3d4afc",
              600: "#2847c3",
              700: "#1e3a9e",
              800: "#1a2f7a",
              900: "#162556",
              DEFAULT: "#3d4afc",
              foreground: "#ffffff",
            },
            focus: "#3d4afc",
          },
        },
      },
    })
  ],
};
export default config;
